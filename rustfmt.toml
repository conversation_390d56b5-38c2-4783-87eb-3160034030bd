# Reorder imports alphabetically
reorder_imports = true # stable
# Merge imports into a single use statement.
imports_granularity = "Crate"
# vertically align args, etc. if beyond "max_width"
# default: 100
use_small_heuristics = "Max" # stable
# wrap comments if over width
wrap_comments = true
comment_width = 100
# Use field initialize shorthand if possible.
use_field_init_shorthand = true # stable
# Format code snippet included in doc comments.
format_code_in_doc_comments = true
# Max width for code snippets included in doc comments.
# default: 100
doc_comment_code_block_width = 100
