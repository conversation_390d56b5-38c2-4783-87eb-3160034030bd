# ثغرة تسرب مفاتيح الشبكة من الذاكرة

## الوصف التقني
مفاتيح الشبكة (libp2p) مشتقة من BLS وتُخزن في الذاكرة، ما قد يسمح بهجمات تسرب الذاكرة أو إعادة الاستخدام إذا لم يتم تأمين الذاكرة بشكل جيد.

## الأثر
- إمكانية استخراج المفاتيح من الذاكرة في حال وجود وصول للنظام.
- هجمات privilege escalation أو سرقة هوية العقدة.

## PoC (خطوات إعادة الإنتاج)
1. تشغيل العقدة في بيئة غير آمنة.
2. تنفيذ core dump أو هجوم تسرب ذاكرة.
3. استخراج المفاتيح من الذاكرة.

## اختبار إثبات
- مراجعة الكود تؤكد وجود المفاتيح في الذاكرة لفترات طويلة:
```rust
#[derive(Debug, Clone)]
pub struct KeyConfig {
    inner: Arc<KeyConfigInner>,
}
// ...existing code...
struct KeyConfigInner {
    primary_keypair: BlsKeypair,
    primary_network_keypair: NetworkKeypair,
    worker_network_keypair: NetworkKeypair,
}
```
**النتيجة:**
إمكانية استخراج المفاتيح من الذاكرة في حال وجود وصول للنظام.

## توصيات الإصلاح
- استخدام مكتبات memory-safe (مثل zeroize) لمسح المفاتيح من الذاكرة فور الانتهاء منها.
- دعم التكامل مع HSM أو وحدات TPM لتخزين المفاتيح الحساسة خارج الذاكرة التقليدية.
- تقييد صلاحيات النظام لمنع عمليات core dump أو الوصول غير المصرح به للذاكرة.
