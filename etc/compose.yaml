services:
  # build image once
  telcoin-base:
    image: local-telcoin-network:latest
    build:
      context: ../
      dockerfile: etc/Dockerfile

  # use built image for all 4 services
  setup1:
    image: local-telcoin-network:latest
    depends_on:
      - telcoin-base
    environment:
      - TN_PRIMARY_ADDR=/ip4/**********/udp/49590/quic-v1
      - TN_WORKER_ADDR=/ip4/**********/udp/49595/quic-v1
      - EXECUTION_ADDRESS=0x1111111111111111111111111111111111111111
      - TN_BLS_PASSPHRASE=local
    user: "root"
    command:
      - "bash"
      - "/setup_validator.sh"
    volumes:
      - ./setup_validator.sh:/setup_validator.sh
      - validator1-data:/home/<USER>/data
  setup2:
    image: local-telcoin-network:latest
    depends_on:
      - telcoin-base
    environment:
      - TN_PRIMARY_ADDR=/ip4/**********/udp/49590/quic-v1
      - TN_WORKER_ADDR=/ip4/**********/udp/49595/quic-v1
      - EXECUTION_ADDRESS=0x2222222222222222222222222222222222222222
      - TN_BLS_PASSPHRASE=local
    user: "root"
    command:
      - "bash"
      - "/setup_validator.sh"
    volumes:
      - ./setup_validator.sh:/setup_validator.sh
      - validator2-data:/home/<USER>/data
  setup3:
    image: local-telcoin-network:latest
    depends_on:
      - telcoin-base
    environment:
      - TN_PRIMARY_ADDR=/ip4/**********/udp/49590/quic-v1
      - TN_WORKER_ADDR=/ip4/**********/udp/49595/quic-v1
      - EXECUTION_ADDRESS=0x3333333333333333333333333333333333333333
      - TN_BLS_PASSPHRASE=local
    user: "root"
    command:
      - "bash"
      - "/setup_validator.sh"
    volumes:
      - ./setup_validator.sh:/setup_validator.sh
      - validator3-data:/home/<USER>/data
  setup4:
    image: local-telcoin-network:latest
    depends_on:
      - telcoin-base
    environment:
      - TN_PRIMARY_ADDR=/ip4/**********/udp/49590/quic-v1
      - TN_WORKER_ADDR=/ip4/**********/udp/49595/quic-v1
      - EXECUTION_ADDRESS=0x4444444444444444444444444444444444444444
      - TN_BLS_PASSPHRASE=local
    user: "root"
    command:
      - "bash"
      - "/setup_validator.sh"
    volumes:
      - ./setup_validator.sh:/setup_validator.sh
      - validator4-data:/home/<USER>/data

  # create and distribute committee / worker cache files
  comittee:
    image: local-telcoin-network:latest
    environment:
      - TN_BLS_PASSPHRASE=local
    user: "root"
    command:
      - "bash"
      - "/genesis.sh"
    depends_on:
      - setup1
      - setup2
      - setup3
      - setup4
    volumes:
      - ./genesis.sh:/genesis.sh
      - sharedgenesisdir:/home/<USER>/data/genesis
      - validator1-data:/home/<USER>/data/validator-1
      - validator2-data:/home/<USER>/data/validator-2
      - validator3-data:/home/<USER>/data/validator-3
      - validator4-data:/home/<USER>/data/validator-4

  # start nodes
  validator1:
    image: local-telcoin-network:latest
    depends_on:
      - comittee
    environment:
      - RUST_LOG=info,execution=debug
      - TN_BLS_PASSPHRASE=local # must match value used in setup1 service
    user: "1101:1101"
    command: >
      /usr/local/bin/telcoin node
      --datadir /home/<USER>/data
      --http.addr 0.0.0.0
      --http.port 8545
      --metrics 127.0.0.1:9101
    ports:
      - 8545:8545
    volumes:
      - validator1-data:/home/<USER>/data
    networks:
      validators:
        ipv4_address: **********
  validator2:
    image: local-telcoin-network:latest
    depends_on:
      - comittee
    environment:
      - RUST_LOG=info,execution=debug
      - TN_BLS_PASSPHRASE=local # must match value used in setup2 service
    user: "1101:1101"
    command: >
      /usr/local/bin/telcoin node
      --datadir /home/<USER>/data
      --http.addr 0.0.0.0
      --http.port 8545
      --metrics 127.0.0.1:9101
    ports:
      - 8544:8545
    volumes:
      - validator2-data:/home/<USER>/data
    networks:
      validators:
        ipv4_address: **********
  validator3:
    image: local-telcoin-network:latest
    depends_on:
      - comittee
    environment:
      - RUST_LOG=info,execution=debug
      - TN_BLS_PASSPHRASE=local # must match value used in setup3 service
    user: "1101:1101"
    command: >
      /usr/local/bin/telcoin node
      --datadir /home/<USER>/data
      --http.addr 0.0.0.0
      --http.port 8545
      --metrics 127.0.0.1:9101
    ports:
      - 8543:8545
    volumes:
      - validator3-data:/home/<USER>/data
    networks:
      validators:
        ipv4_address: **********
  validator4:
    image: local-telcoin-network:latest
    depends_on:
      - comittee
    environment:
      - RUST_LOG=info,execution=debug
      - TN_BLS_PASSPHRASE=local # must match value used in setup4 service
    user: "1101:1101"
    command: >
      /usr/local/bin/telcoin node
      --datadir /home/<USER>/data
      --http.addr 0.0.0.0
      --http.port 8545
      --metrics 127.0.0.1:9101
    ports:
      - 8542:8545
    volumes:
      - validator4-data:/home/<USER>/data
    networks:
      validators:
        ipv4_address: **********

volumes:
  validator1-data:
  validator2-data:
  validator3-data:
  validator4-data:
  sharedgenesisdir:

networks:
  validators:
    driver: bridge
    ipam:
      config:
        - subnet: *********/16
