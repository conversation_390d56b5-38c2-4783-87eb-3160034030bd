#!/bin/bash

USER_ID=1101

# Ensure the data directory exists and is owned by nonroot user
if [ ! -d /home/<USER>/data/node-keys ]; then
    echo "creating validator keys"
    export TN_BLS_PASSPHRASE="local"
    /usr/local/bin/telcoin keytool generate validator --datadir /home/<USER>/data --address "${EXECUTION_ADDRESS}"

    chown -R ${USER_ID}:${USER_ID} /home/<USER>/data

    echo "Keys generated and ownership/permissions set"

    ls -la /home/<USER>/data/
    ls -la /home/<USER>/data/node-keys/
else
    echo "Setup already complete"
    ls -la /home/<USER>/data/
    ls -la /home/<USER>/data/node-keys/ 2>/dev/null || echo "node-keys directory not found"
fi
