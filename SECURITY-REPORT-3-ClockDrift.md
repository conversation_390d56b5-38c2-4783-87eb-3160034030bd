# ثغرة هجوم clock drift على التحقق الزمني

## الوصف التقني
التحقق من الطابع الزمني يعتمد على مقارنة timestamp مع parent فقط، ما قد يسمح باستغلال انحراف الساعة (clock drift) لإدخال دفعات بتوقيتات غير متسقة.

## الأثر
- إدخال دفعات بتوقيتات غير متسقة.
- مشاكل في ترتيب الدُفعات أو تنفيذها.

## PoC (خطوات إعادة الإنتاج)
1. إرسال دفعات بتوقيتات متقدمة أو متأخرة بشكل طفيف، مع انحراف ساعة العقدة.
2. ملاحظة قبولها أو رفضها حسب انحراف الساعة.

## اختبار إثبات
```rust
#[tokio::test]
async fn test_invalid_batch_wrong_timestamp() {
    // ...existing code...
    batch.timestamp = wrong_timestamp;
    assert_matches!(
        validator.validate_batch(batch.clone().seal_slow()),
        Err(BatchValidationError::TimestampIsInPast{parent_timestamp, timestamp}) if parent_timestamp == wrong_timestamp && timestamp == wrong_timestamp
    );
    // ...existing code...
}
```
**النتيجة:**
إمكانية إدخال دفعات بتوقيتات غير متسقة في بيئة موزعة.

## توصيات الإصلاح
- إضافة تحقق منطق زمني أكثر صرامة يأخذ في الاعتبار الانحرافات الزمنية المتوقعة بين العقد (clock skew tolerance).
- رفض الدُفعات التي تتجاوز انحرافًا زمنيًا محددًا مسبقًا عن الوقت الحالي للشبكة.
- تسجيل جميع الدُفعات ذات التوقيتات الشاذة لمراجعتها وتحليلها لاحقًا.
