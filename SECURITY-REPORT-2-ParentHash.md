# ثغرة ضعف تحقق parent_hash في batch-validator

## الوصف التقني
التحقق من parent_hash في batch-validator ليس إلزاميًا دائمًا لتجنب false negatives، ما قد يسمح بهجمات سباق أو إعادة ترتيب الدُفعات في ظروف معينة، خاصة في حالات التزامن أو إعادة التنظيم.

## الأثر
- إمكانية تمرير دفعات غير متسلسلة.
- هجمات سباق أو إعادة ترتيب الدُفعات.

## PoC (خطوات إعادة الإنتاج)
1. تعديل parent_hash في دفعة وإرسالها في توقيت متزامن مع دفعات صحيحة.
2. ملاحظة قبولها في بعض الحالات.

## اختبار إثبات
يوجد اختبار (معلق) في batch-validator يوضح ذلك:
```rust
async fn _test_invalid_batch_wrong_parent_hash() {
    // ...existing code...
    let wrong_parent_hash = B256::random();
    let invalid_batch = Batch {
        // ...existing code...
        parent_hash: wrong_parent_hash,
        // ...existing code...
    };
    assert_matches!(
        validator.validate_batch(invalid_batch.seal_slow()),
        Err(BatchValidationError::CanonicalChain { block_hash }) if block_hash == wrong_parent_hash
    );
}
```
**النتيجة:**
إمكانية تمرير دفعات غير متسلسلة في ظروف سباق.

## توصيات الإصلاح
- جعل التحقق من parent_hash إلزاميًا في جميع الحالات، حتى لو أدى ذلك إلى بعض false negatives المؤقتة.
- إضافة آلية انتظار (wait/retry) في حال عدم توفر parent_hash بدلاً من قبول الدُفعة مباشرة.
- مراقبة حالات السباق وتسجيلها لتحليلها لاحقًا.
