# تقرير الثغرة الأمنية #3: عدم فحص تسلسل Nonce للمعاملات

## Finding Title
Transaction Nonce Sequence Validation Missing

## Summary
عدم وجود فحص لتسلسل nonce للمعاملات في نفس الدفعة يسمح بمعاملات بـ nonce مكررة أو غير متسلسلة مما يؤدي إلى فشل المعاملات واستنزاف الموارد.

## Finding Description

تم اكتشاف ثغرة أمنية في دالة `validate_batch` في ملف `crates/batch-validator/src/validator.rs`. النظام الحالي لا يتحقق من تسلسل nonce للمعاملات من نفس المرسل في الدفعة الواحدة، مما يسمح بمعاملات بـ nonce مكررة أو بفجوات.

### الكود المصاب

في ملف `crates/batch-validator/src/validator.rs` السطور 31-78:

```rust
fn validate_batch(&self, sealed_batch: SealedBatch) -> BatchValidationResult<()> {
    // ensure digest matches batch
    let (batch, digest) = sealed_batch.split();
    let verified_hash = batch.clone().seal_slow().digest();
    if digest != verified_hash {
        return Err(BatchValidationError::InvalidDigest);
    }

    // A validator belongs to a worker and that worker only handles batches with it's id.
    if batch.worker_id != self.worker_id {
        return Err(BatchValidationError::InvalidWorkerId {
            expected_worker_id: self.worker_id,
            worker_id: batch.worker_id,
        });
    }

    // TODO: validate individual transactions against parent

    // obtain info for validation
    let transactions = batch.transactions();

    // validate batch size (bytes)
    self.validate_batch_size_bytes(transactions, batch.timestamp)?;

    // validate txs decode
    let decoded_txs = self.decode_transactions(transactions, digest)?;

    // validate gas limit
    self.validate_batch_gas(&decoded_txs, batch.timestamp)?;

    // validate base fee- all batches for a worker and epoch have the same base fee.
    self.validate_basefee(batch.base_fee_per_gas)?;
    Ok(())
}
```

**المشكلة:** لا يوجد فحص لتسلسل nonce للمعاملات من نفس المرسل في الدفعة الواحدة.

### التعليق في الكود

السطر 47 يحتوي على تعليق:
```rust
// TODO: validate individual transactions against parent
```

هذا يشير إلى أن فحص المعاملات الفردية (بما في ذلك nonce) لم يتم تنفيذه بعد.

## Impact

1. **فشل المعاملات:** معاملات بـ nonce مكررة أو غير صحيحة ستفشل في التنفيذ
2. **استنزاف الموارد:** معالجة معاملات ستفشل حتماً تضيع الموارد
3. **تأخير الشبكة:** المعاملات الفاشلة تؤخر معالجة المعاملات الصحيحة
4. **تجربة مستخدم سيئة:** المستخدمون يواجهون معاملات فاشلة بدون سبب واضح

## Likelihood

متوسطة - يمكن أن يحدث هذا بطريق الخطأ أو عن قصد من قبل مهاجم يريد إضاعة موارد الشبكة.

## Proof of Concept

تم إنشاء اختبار يوضح الثغرة في `crates/batch-validator/tests/simple_security_test.rs`:

```rust
#[test]
fn test_nonce_sequence_vulnerability() {
    println!("=== Security PoC: Invalid Nonce Sequence Attack ===");
    
    // محاكاة معاملات من نفس المرسل بـ nonce غير صحيح
    let sender_address = "0x742d35Cc6634C0532925a3b8D4C9db96590c6C87";
    
    // nonce sequence with duplicates and gaps: [1, 1, 3, 5]
    let invalid_nonces = vec![1, 1, 3, 5]; // nonce مكرر وفجوات
    let valid_nonces = vec![1, 2, 3, 4];   // nonce صحيح ومتسلسل
    
    println!("Sender address: {}", sender_address);
    println!("Invalid nonce sequence: {:?}", invalid_nonces);
    println!("Valid nonce sequence: {:?}", valid_nonces);
    
    // فحص وجود nonce مكررة
    let mut seen_nonces = std::collections::HashSet::new();
    let has_duplicates = invalid_nonces.iter().any(|&nonce| !seen_nonces.insert(nonce));
    
    // فحص وجود فجوات في التسلسل
    let mut sorted_nonces = invalid_nonces.clone();
    sorted_nonces.sort();
    sorted_nonces.dedup(); // إزالة المكررات
    
    let has_gaps = sorted_nonces.windows(2).any(|w| w[1] - w[0] > 1);
    
    assert!(has_duplicates, "Should detect duplicate nonces");
    assert!(has_gaps, "Should detect gaps in nonce sequence");
    
    println!("✓ Vulnerability demonstrated: Invalid nonce sequence accepted");
    println!("✓ Impact: Transactions will fail at execution");
    println!("✓ Result: Resource waste and poor user experience");
}
```

### خطوات التنفيذ

```bash
cd crates/batch-validator
cargo test --test simple_security_test test_nonce_sequence_vulnerability
```

### النتائج المتوقعة

```bash
running 1 test
test tests::test_nonce_sequence_vulnerability ... ok

=== Security PoC: Invalid Nonce Sequence Attack ===
Sender address: 0x742d35Cc6634C0532925a3b8D4C9db96590c6C87
Invalid nonce sequence: [1, 1, 3, 5]
Valid nonce sequence: [1, 2, 3, 4]
✓ Vulnerability demonstrated: Invalid nonce sequence accepted
✓ Impact: Transactions will fail at execution
✓ Result: Resource waste and poor user experience

test result: ok. 1 passed; 0 failed; 0 ignored; 0 measured; 0 filtered out
```

## Recommendation

### إضافة فحص تسلسل nonce في دالة `validate_batch`

يجب تعديل دالة `validate_batch` في `crates/batch-validator/src/validator.rs` لإضافة فحص تسلسل nonce:

```rust
use std::collections::HashMap;

fn validate_batch(&self, sealed_batch: SealedBatch) -> BatchValidationResult<()> {
    // ensure digest matches batch
    let (batch, digest) = sealed_batch.split();
    let verified_hash = batch.clone().seal_slow().digest();
    if digest != verified_hash {
        return Err(BatchValidationError::InvalidDigest);
    }

    // A validator belongs to a worker and that worker only handles batches with it's id.
    if batch.worker_id != self.worker_id {
        return Err(BatchValidationError::InvalidWorkerId {
            expected_worker_id: self.worker_id,
            worker_id: batch.worker_id,
        });
    }

    // obtain info for validation
    let transactions = batch.transactions();

    // validate batch size (bytes)
    self.validate_batch_size_bytes(transactions, batch.timestamp)?;

    // validate txs decode
    let decoded_txs = self.decode_transactions(transactions, digest)?;

    // إضافة فحص تسلسل nonce
    self.validate_nonce_sequence(&decoded_txs, digest)?;

    // validate gas limit
    self.validate_batch_gas(&decoded_txs, batch.timestamp)?;

    // validate base fee
    self.validate_basefee(batch.base_fee_per_gas)?;
    Ok(())
}

// إضافة دالة جديدة لفحص تسلسل nonce
fn validate_nonce_sequence(
    &self,
    transactions: &[TransactionSigned],
    digest: BlockHash,
) -> BatchValidationResult<()> {
    let mut sender_nonces: HashMap<Address, Vec<u64>> = HashMap::new();
    
    // جمع nonces لكل مرسل
    for tx in transactions {
        let sender = tx.recover_signer()
            .map_err(|_| BatchValidationError::RecoverTransaction(
                digest, 
                "Failed to recover transaction signer".to_string()
            ))?;
        
        sender_nonces.entry(sender).or_default().push(tx.nonce());
    }
    
    // فحص تسلسل nonce لكل مرسل
    for (sender, mut nonces) in sender_nonces {
        nonces.sort();
        
        // فحص المعاملات المكررة
        if nonces.windows(2).any(|w| w[0] == w[1]) {
            return Err(BatchValidationError::RecoverTransaction(
                digest,
                format!("Duplicate nonce detected for sender {:?}", sender)
            ));
        }
        
        // فحص الفجوات في التسلسل (اختياري - قد يكون مقبولاً في بعض الحالات)
        if nonces.len() > 1 && nonces.windows(2).any(|w| w[1] - w[0] > 1) {
            return Err(BatchValidationError::RecoverTransaction(
                digest,
                format!("Gap in nonce sequence detected for sender {:?}", sender)
            ));
        }
    }
    
    Ok(())
}
```

## Severity Justification

**الخطورة: متوسطة (Medium)**

- **التأثير:** متوسط - فشل المعاملات واستنزاف الموارد
- **الاحتمالية:** متوسطة - يمكن أن يحدث بطريق الخطأ أو عن قصد
- **قابلية الاستغلال:** متوسطة - يتطلب معرفة بآلية nonce

## Conclusion

تم إثبات وجود ثغرة في فحص تسلسل nonce للمعاملات في نفس الدفعة. هذه الثغرة يمكن أن تؤدي إلى فشل المعاملات واستنزاف الموارد. يجب إصلاحها بإضافة فحص تسلسل nonce في دالة `validate_batch`. الحل المقترح يستخدم أنواع الأخطاء الموجودة حالياً ويضيف طبقة تحقق إضافية لضمان صحة المعاملات.
