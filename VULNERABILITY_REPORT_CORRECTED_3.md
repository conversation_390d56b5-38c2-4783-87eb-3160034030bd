# تقرير الثغرة الأمنية #3: عدم فحص تسلسل Nonce للمعاملات

## Finding Title
Transaction Nonce Sequence Validation Missing

## Summary
عدم وجود فحص لتسلسل nonce للمعاملات في نفس الدفعة يسمح بمعاملات بـ nonce مكررة أو غير متسلسلة مما يؤدي إلى فشل المعاملات واستنزاف الموارد.

## Finding Description

تم اكتشاف ثغرة أمنية في دالة `validate_batch` في ملف `crates/batch-validator/src/validator.rs`. النظام الحالي لا يتحقق من تسلسل nonce للمعاملات من نفس المرسل في الدفعة الواحدة، مما يسمح بمعاملات بـ nonce مكررة أو بفجوات.

### الكود المصاب

في ملف `crates/batch-validator/src/validator.rs` السطور 31-78:

```rust
fn validate_batch(&self, sealed_batch: SealedBatch) -> BatchValidationResult<()> {
    // ensure digest matches batch
    let (batch, digest) = sealed_batch.split();
    let verified_hash = batch.clone().seal_slow().digest();
    if digest != verified_hash {
        return Err(BatchValidationError::InvalidDigest);
    }

    // A validator belongs to a worker and that worker only handles batches with it's id.
    if batch.worker_id != self.worker_id {
        return Err(BatchValidationError::InvalidWorkerId {
            expected_worker_id: self.worker_id,
            worker_id: batch.worker_id,
        });
    }

    // TODO: validate individual transactions against parent

    // obtain info for validation
    let transactions = batch.transactions();

    // validate batch size (bytes)
    self.validate_batch_size_bytes(transactions, batch.timestamp)?;

    // validate txs decode
    let decoded_txs = self.decode_transactions(transactions, digest)?;

    // validate gas limit
    self.validate_batch_gas(&decoded_txs, batch.timestamp)?;

    // validate base fee- all batches for a worker and epoch have the same base fee.
    self.validate_basefee(batch.base_fee_per_gas)?;
    Ok(())
}
```

**المشكلة:** لا يوجد فحص لتسلسل nonce للمعاملات من نفس المرسل في الدفعة الواحدة.

### التعليق في الكود

السطر 47 يحتوي على تعليق:
```rust
// TODO: validate individual transactions against parent
```

هذا يشير إلى أن فحص المعاملات الفردية (بما في ذلك nonce) لم يتم تنفيذه بعد.

## Impact

1. **فشل المعاملات:** معاملات بـ nonce مكررة أو غير صحيحة ستفشل في التنفيذ
2. **استنزاف الموارد:** معالجة معاملات ستفشل حتماً تضيع الموارد
3. **تأخير الشبكة:** المعاملات الفاشلة تؤخر معالجة المعاملات الصحيحة
4. **تجربة مستخدم سيئة:** المستخدمون يواجهون معاملات فاشلة بدون سبب واضح

## Likelihood

متوسطة - يمكن أن يحدث هذا بطريق الخطأ أو عن قصد من قبل مهاجم يريد إضاعة موارد الشبكة.

## Proof of Concept

تم إنشاء اختبارات حقيقية تستدعي دالة `validate_batch` الفعلية. يجب إنشاء ملف `crates/batch-validator/tests/security_poc_nonce_validation.rs` بالكود الكامل التالي:

### الكود الكامل للاختبار

```rust
use tempfile::TempDir;
use tn_types::{TaskManager, U256, Address, Bytes, Batch, test_genesis, BatchValidation, TransactionTrait};
use tn_reth::{test_utils::TransactionFactory, RethChainSpec, recover_raw_transaction};
use std::sync::Arc;

// استيراد BatchValidator من الكريت الفعلي
use tn_batch_validator::BatchValidator;

// تعريف TestTools وtest_tools محليًا (من كود الاختبار الأصلي)
struct TestTools {
    pub validator: BatchValidator,
    pub valid_batch: Batch,
}

async fn test_tools(path: &std::path::Path, task_manager: &TaskManager) -> TestTools {
    use tn_reth::{RethEnv, test_utils::TransactionFactory};
    use tn_types::{Batch, Address, U256, test_genesis};
    use std::sync::Arc;

    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), path, task_manager).unwrap();
    let tx_pool = reth_env.init_txn_pool().unwrap();
    let validator = BatchValidator::new(reth_env, Some(tx_pool), 0, Default::default());

    let timestamp = chain.genesis_timestamp() + 1;
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let genesis_hash = chain.genesis_hash();
    let transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        None,
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    let valid_batch = Batch {
        transactions: vec![transaction],
        parent_hash: genesis_hash,
        beneficiary: Address::ZERO,
        timestamp,
        base_fee_per_gas: Some(1),
        worker_id: 0,
        received_at: None,
    };
    TestTools { validator, valid_batch }
}

#[tokio::test]
async fn test_invalid_nonce_sequence_dos() {
    println!("=== Testing Invalid Nonce Sequence DoS Attack ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملات بتسلسل nonce غير صحيح
    let tx1 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    // تخطي nonce (إنشاء فجوة)
    tx_factory.inc_nonce();
    tx_factory.inc_nonce();

    let tx2 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    // فك تشفير المعاملات للوصول إلى nonce
    let decoded_tx1 = recover_raw_transaction(&tx1).unwrap().into_inner();
    let decoded_tx2 = recover_raw_transaction(&tx2).unwrap().into_inner();

    println!("Transaction 1 nonce: {}", decoded_tx1.nonce());
    println!("Transaction 2 nonce: {}", decoded_tx2.nonce());
    println!("Nonce gap detected: {} -> {}", decoded_tx1.nonce(), decoded_tx2.nonce());

    let invalid_nonce_batch = Batch {
        transactions: vec![tx1, tx2],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(invalid_nonce_batch.seal_slow());

    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_ok() {
        println!("✗ Vulnerability confirmed: Invalid nonce sequences pass validation");
        println!("  Impact: Resource exhaustion, wasted computation");
        println!("  Recommendation: Add nonce sequence validation in validate_batch()");
    } else {
        println!("✓ Nonce validation working correctly");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly rejects invalid nonce sequences");
    }
}

#[tokio::test]
async fn test_duplicate_nonce_dos() {
    println!("=== Testing Duplicate Nonce DoS Attack ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملتين بنفس nonce
    let tx1 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    // فك تشفير المعاملة الأولى للحصول على nonce
    let decoded_tx1 = recover_raw_transaction(&tx1).unwrap().into_inner();

    // إعادة تعيين nonce لإنشاء معاملة مكررة
    tx_factory.set_nonce(decoded_tx1.nonce());

    let tx2 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value + U256::from(1000), // قيمة مختلفة لكن نفس nonce
        Bytes::new(),
    );

    // فك تشفير المعاملات للوصول إلى nonce
    let decoded_tx1_dup = recover_raw_transaction(&tx1).unwrap().into_inner();
    let decoded_tx2_dup = recover_raw_transaction(&tx2).unwrap().into_inner();

    println!("Transaction 1 nonce: {}", decoded_tx1_dup.nonce());
    println!("Transaction 2 nonce: {}", decoded_tx2_dup.nonce());
    println!("Duplicate nonce detected: both transactions have nonce {}", decoded_tx1_dup.nonce());

    let duplicate_nonce_batch = Batch {
        transactions: vec![tx1, tx2],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(duplicate_nonce_batch.seal_slow());

    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_ok() {
        println!("✗ Vulnerability confirmed: Duplicate nonce transactions pass validation");
        println!("  Impact: Only one transaction will succeed, wasting resources");
        println!("  Recommendation: Add duplicate nonce detection in validate_batch()");
    } else {
        println!("✓ Duplicate nonce validation working correctly");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly rejects duplicate nonce transactions");
    }
}

#[tokio::test]
async fn test_valid_nonce_sequence() {
    println!("=== Testing Valid Nonce Sequence (Control Test) ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملات بتسلسل nonce صحيح
    let tx1 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    let tx2 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    // فك تشفير المعاملات للوصول إلى nonce
    let decoded_tx1_valid = recover_raw_transaction(&tx1).unwrap().into_inner();
    let decoded_tx2_valid = recover_raw_transaction(&tx2).unwrap().into_inner();

    println!("Transaction 1 nonce: {}", decoded_tx1_valid.nonce());
    println!("Transaction 2 nonce: {}", decoded_tx2_valid.nonce());
    println!("Valid nonce sequence: {} -> {}", decoded_tx1_valid.nonce(), decoded_tx2_valid.nonce());

    let valid_nonce_batch = Batch {
        transactions: vec![tx1, tx2],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(valid_nonce_batch.seal_slow());

    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_ok() {
        println!("✓ Valid nonce sequence accepted correctly");
    } else {
        println!("✗ Valid nonce sequence rejected unexpectedly");
        println!("  Result: {:?}", result.err().unwrap());
    }
}
```

### خطوات التنفيذ

```bash
cd crates/batch-validator
cargo test --test security_poc_nonce_validation -- --nocapture
```

### النتائج الفعلية

```bash
running 3 tests
=== Testing Invalid Nonce Sequence DoS Attack ===
=== Testing Valid Nonce Sequence (Control Test) ===
=== Testing Duplicate Nonce DoS Attack ===
Transaction 1 nonce: 0
Transaction 2 nonce: 3
Nonce gap detected: 0 -> 3
Validation result: Ok(())
✗ Vulnerability confirmed: Invalid nonce sequences pass validation
  Impact: Resource exhaustion, wasted computation
  Recommendation: Add nonce sequence validation in validate_batch()
test test_invalid_nonce_sequence_dos ... ok
Transaction 1 nonce: 0
Transaction 2 nonce: 1
Valid nonce sequence: 0 -> 1
Transaction 1 nonce: 0
Transaction 2 nonce: 0
Duplicate nonce detected: both transactions have nonce 0
Validation result: Ok(())
✓ Valid nonce sequence accepted correctly
Validation result: Ok(())
✗ Vulnerability confirmed: Duplicate nonce transactions pass validation
  Impact: Only one transaction will succeed, wasting resources
  Recommendation: Add duplicate nonce detection in validate_batch()
test test_valid_nonce_sequence ... ok
test test_duplicate_nonce_dos ... ok

test result: ok. 3 passed; 0 failed; 0 ignored; 0 measured; 0 filtered out; finished in 1.15s
```

**تحليل النتائج:**

1. **اختبار فجوات Nonce:** تم إثبات أن المعاملات بـ nonce 0 و 3 (فجوة = 2) تمر التحقق بنجاح
2. **اختبار Nonce مكررة:** تم إثبات أن معاملتين بنفس nonce (0) تمران التحقق بنجاح
3. **اختبار التحكم:** المعاملات بتسلسل صحيح (0 → 1) تمر التحقق كما هو متوقع

**الخلاصة:** النظام الحالي لا يفحص تسلسل nonce مما يسمح بمعاملات ستفشل حتماً في التنفيذ.

## Recommendation

### إضافة فحص تسلسل nonce في دالة `validate_batch`

يجب تعديل دالة `validate_batch` في `crates/batch-validator/src/validator.rs` لإضافة فحص تسلسل nonce:

```rust
use std::collections::HashMap;

fn validate_batch(&self, sealed_batch: SealedBatch) -> BatchValidationResult<()> {
    // ensure digest matches batch
    let (batch, digest) = sealed_batch.split();
    let verified_hash = batch.clone().seal_slow().digest();
    if digest != verified_hash {
        return Err(BatchValidationError::InvalidDigest);
    }

    // A validator belongs to a worker and that worker only handles batches with it's id.
    if batch.worker_id != self.worker_id {
        return Err(BatchValidationError::InvalidWorkerId {
            expected_worker_id: self.worker_id,
            worker_id: batch.worker_id,
        });
    }

    // obtain info for validation
    let transactions = batch.transactions();

    // validate batch size (bytes)
    self.validate_batch_size_bytes(transactions, batch.timestamp)?;

    // validate txs decode
    let decoded_txs = self.decode_transactions(transactions, digest)?;

    // إضافة فحص تسلسل nonce
    self.validate_nonce_sequence(&decoded_txs, digest)?;

    // validate gas limit
    self.validate_batch_gas(&decoded_txs, batch.timestamp)?;

    // validate base fee
    self.validate_basefee(batch.base_fee_per_gas)?;
    Ok(())
}

// إضافة دالة جديدة لفحص تسلسل nonce
fn validate_nonce_sequence(
    &self,
    transactions: &[TransactionSigned],
    digest: BlockHash,
) -> BatchValidationResult<()> {
    let mut sender_nonces: HashMap<Address, Vec<u64>> = HashMap::new();
    
    // جمع nonces لكل مرسل
    for tx in transactions {
        let sender = tx.recover_signer()
            .map_err(|_| BatchValidationError::RecoverTransaction(
                digest, 
                "Failed to recover transaction signer".to_string()
            ))?;
        
        sender_nonces.entry(sender).or_default().push(tx.nonce());
    }
    
    // فحص تسلسل nonce لكل مرسل
    for (sender, mut nonces) in sender_nonces {
        nonces.sort();
        
        // فحص المعاملات المكررة
        if nonces.windows(2).any(|w| w[0] == w[1]) {
            return Err(BatchValidationError::RecoverTransaction(
                digest,
                format!("Duplicate nonce detected for sender {:?}", sender)
            ));
        }
        
        // فحص الفجوات في التسلسل (اختياري - قد يكون مقبولاً في بعض الحالات)
        if nonces.len() > 1 && nonces.windows(2).any(|w| w[1] - w[0] > 1) {
            return Err(BatchValidationError::RecoverTransaction(
                digest,
                format!("Gap in nonce sequence detected for sender {:?}", sender)
            ));
        }
    }
    
    Ok(())
}
```

## Severity Justification

**الخطورة: متوسطة (Medium)**

- **التأثير:** متوسط - فشل المعاملات واستنزاف الموارد
- **الاحتمالية:** متوسطة - يمكن أن يحدث بطريق الخطأ أو عن قصد
- **قابلية الاستغلال:** متوسطة - يتطلب معرفة بآلية nonce

## Conclusion

تم إثبات وجود ثغرة في فحص تسلسل nonce للمعاملات في نفس الدفعة. هذه الثغرة يمكن أن تؤدي إلى فشل المعاملات واستنزاف الموارد. يجب إصلاحها بإضافة فحص تسلسل nonce في دالة `validate_batch`. الحل المقترح يستخدم أنواع الأخطاء الموجودة حالياً ويضيف طبقة تحقق إضافية لضمان صحة المعاملات.
