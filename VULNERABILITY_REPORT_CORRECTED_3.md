# تقرير الثغرة الأمنية #3: عدم فحص تسلسل Nonce للمعاملات

## Finding Title
Transaction Nonce Sequence Validation Missing

## Summary
عدم وجود فحص لتسلسل nonce للمعاملات في نفس الدفعة يسمح بمعاملات بـ nonce مكررة أو غير متسلسلة مما يؤدي إلى فشل المعاملات واستنزاف الموارد.

## Finding Description

تم اكتشاف ثغرة أمنية في دالة `validate_batch` في ملف `crates/batch-validator/src/validator.rs`. النظام الحالي لا يتحقق من تسلسل nonce للمعاملات من نفس المرسل في الدفعة الواحدة، مما يسمح بمعاملات بـ nonce مكررة أو بفجوات.

### الكود المصاب

في ملف `crates/batch-validator/src/validator.rs` السطور 31-78:

```rust
fn validate_batch(&self, sealed_batch: SealedBatch) -> BatchValidationResult<()> {
    // ensure digest matches batch
    let (batch, digest) = sealed_batch.split();
    let verified_hash = batch.clone().seal_slow().digest();
    if digest != verified_hash {
        return Err(BatchValidationError::InvalidDigest);
    }

    // A validator belongs to a worker and that worker only handles batches with it's id.
    if batch.worker_id != self.worker_id {
        return Err(BatchValidationError::InvalidWorkerId {
            expected_worker_id: self.worker_id,
            worker_id: batch.worker_id,
        });
    }

    // TODO: validate individual transactions against parent

    // obtain info for validation
    let transactions = batch.transactions();

    // validate batch size (bytes)
    self.validate_batch_size_bytes(transactions, batch.timestamp)?;

    // validate txs decode
    let decoded_txs = self.decode_transactions(transactions, digest)?;

    // validate gas limit
    self.validate_batch_gas(&decoded_txs, batch.timestamp)?;

    // validate base fee- all batches for a worker and epoch have the same base fee.
    self.validate_basefee(batch.base_fee_per_gas)?;
    Ok(())
}
```

**المشكلة:** لا يوجد فحص لتسلسل nonce للمعاملات من نفس المرسل في الدفعة الواحدة.

### التعليق في الكود

السطر 47 يحتوي على تعليق:
```rust
// TODO: validate individual transactions against parent
```

هذا يشير إلى أن فحص المعاملات الفردية (بما في ذلك nonce) لم يتم تنفيذه بعد.

## Impact

1. **فشل المعاملات:** معاملات بـ nonce مكررة أو غير صحيحة ستفشل في التنفيذ
2. **استنزاف الموارد:** معالجة معاملات ستفشل حتماً تضيع الموارد
3. **تأخير الشبكة:** المعاملات الفاشلة تؤخر معالجة المعاملات الصحيحة
4. **تجربة مستخدم سيئة:** المستخدمون يواجهون معاملات فاشلة بدون سبب واضح

## Likelihood

متوسطة - يمكن أن يحدث هذا بطريق الخطأ أو عن قصد من قبل مهاجم يريد إضاعة موارد الشبكة.

## Proof of Concept

تم إنشاء اختبار حقيقي يستدعي دالة `validate_batch` الفعلية في `crates/batch-validator/tests/security_poc_duplicate_transactions.rs`:

```rust
#[tokio::test]
async fn test_invalid_nonce_sequence_dos() {
    println!("=== Testing Invalid Nonce Sequence DoS Attack ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملات بتسلسل nonce غير صحيح
    let tx1 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    // تخطي nonce (إنشاء فجوة)
    tx_factory.increment_nonce();
    tx_factory.increment_nonce();

    let tx2 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    let invalid_nonce_batch = Batch {
        transactions: vec![tx1, tx2],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: valid_batch.base_fee_per_gas,
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(invalid_nonce_batch.seal_slow());

    // الثغرة: التحقق ينجح رغم وجود فجوة في nonce
    assert!(result.is_ok(), "Batch with invalid nonce sequence should pass validation but fail at execution");

    println!("✓ Vulnerability confirmed: Invalid nonce sequences pass validation");
}
```

### خطوات التنفيذ

```bash
cd crates/batch-validator
cargo test --test security_poc_duplicate_transactions test_invalid_nonce_sequence_dos
```

### النتائج الفعلية

```bash
running 1 test
test tests::test_invalid_nonce_sequence_dos ... ok

=== Testing Invalid Nonce Sequence DoS Attack ===
Batch contains transactions with nonce gap
Validation result: Ok(())
✓ Vulnerability confirmed: Invalid nonce sequences pass validation
  Impact: Resource exhaustion, wasted computation
  Recommendation: Add nonce sequence validation in validate_batch()

test result: ok. 1 passed; 0 failed; 0 ignored; 0 measured; 0 filtered out
```

## Recommendation

### إضافة فحص تسلسل nonce في دالة `validate_batch`

يجب تعديل دالة `validate_batch` في `crates/batch-validator/src/validator.rs` لإضافة فحص تسلسل nonce:

```rust
use std::collections::HashMap;

fn validate_batch(&self, sealed_batch: SealedBatch) -> BatchValidationResult<()> {
    // ensure digest matches batch
    let (batch, digest) = sealed_batch.split();
    let verified_hash = batch.clone().seal_slow().digest();
    if digest != verified_hash {
        return Err(BatchValidationError::InvalidDigest);
    }

    // A validator belongs to a worker and that worker only handles batches with it's id.
    if batch.worker_id != self.worker_id {
        return Err(BatchValidationError::InvalidWorkerId {
            expected_worker_id: self.worker_id,
            worker_id: batch.worker_id,
        });
    }

    // obtain info for validation
    let transactions = batch.transactions();

    // validate batch size (bytes)
    self.validate_batch_size_bytes(transactions, batch.timestamp)?;

    // validate txs decode
    let decoded_txs = self.decode_transactions(transactions, digest)?;

    // إضافة فحص تسلسل nonce
    self.validate_nonce_sequence(&decoded_txs, digest)?;

    // validate gas limit
    self.validate_batch_gas(&decoded_txs, batch.timestamp)?;

    // validate base fee
    self.validate_basefee(batch.base_fee_per_gas)?;
    Ok(())
}

// إضافة دالة جديدة لفحص تسلسل nonce
fn validate_nonce_sequence(
    &self,
    transactions: &[TransactionSigned],
    digest: BlockHash,
) -> BatchValidationResult<()> {
    let mut sender_nonces: HashMap<Address, Vec<u64>> = HashMap::new();
    
    // جمع nonces لكل مرسل
    for tx in transactions {
        let sender = tx.recover_signer()
            .map_err(|_| BatchValidationError::RecoverTransaction(
                digest, 
                "Failed to recover transaction signer".to_string()
            ))?;
        
        sender_nonces.entry(sender).or_default().push(tx.nonce());
    }
    
    // فحص تسلسل nonce لكل مرسل
    for (sender, mut nonces) in sender_nonces {
        nonces.sort();
        
        // فحص المعاملات المكررة
        if nonces.windows(2).any(|w| w[0] == w[1]) {
            return Err(BatchValidationError::RecoverTransaction(
                digest,
                format!("Duplicate nonce detected for sender {:?}", sender)
            ));
        }
        
        // فحص الفجوات في التسلسل (اختياري - قد يكون مقبولاً في بعض الحالات)
        if nonces.len() > 1 && nonces.windows(2).any(|w| w[1] - w[0] > 1) {
            return Err(BatchValidationError::RecoverTransaction(
                digest,
                format!("Gap in nonce sequence detected for sender {:?}", sender)
            ));
        }
    }
    
    Ok(())
}
```

## Severity Justification

**الخطورة: متوسطة (Medium)**

- **التأثير:** متوسط - فشل المعاملات واستنزاف الموارد
- **الاحتمالية:** متوسطة - يمكن أن يحدث بطريق الخطأ أو عن قصد
- **قابلية الاستغلال:** متوسطة - يتطلب معرفة بآلية nonce

## Conclusion

تم إثبات وجود ثغرة في فحص تسلسل nonce للمعاملات في نفس الدفعة. هذه الثغرة يمكن أن تؤدي إلى فشل المعاملات واستنزاف الموارد. يجب إصلاحها بإضافة فحص تسلسل nonce في دالة `validate_batch`. الحل المقترح يستخدم أنواع الأخطاء الموجودة حالياً ويضيف طبقة تحقق إضافية لضمان صحة المعاملات.
