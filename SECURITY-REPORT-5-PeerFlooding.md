# ثغرة Peer flooding لإزاحة أقران شرعيين

## الوصف التقني
هناك حدود لعدد الأقران المحظورين/المنفصلين، ما قد يسمح بهجمات إغراق (peer flooding) لإزاحة أقران شرعيين من الذاكرة.

## الأثر
- إزاحة أقران شرعيين من الذاكرة.
- تقليل كفاءة الشبكة أو عزل عقد شرعية.

## PoC (خطوات إعادة الإنتاج)
1. إرسال طلبات اتصال من آلاف الأقران المزيفين حتى امتلاء قائمة المحظورين/المنفصلين.
2. ملاحظة إزاحة أقران شرعيين.

## اختبار إثبات
- اختبار في peer_manager يوضح إمكانية امتلاء القائمة وإزاحة أقران شرعيين:
```rust
#[tokio::test]
async fn test_register_disconnected_basic() {
    // ...existing code...
    // register connection
    let connection = ConnectionType::IncomingConnection { multiaddr };
    assert!(peer_manager.register_peer_connection(&peer_id, connection));
    // ...existing code...
}
```
**النتيجة:**
إمكانية إزاحة أقران شرعيين من الذاكرة عبر هجوم flooding.

## توصيات الإصلاح
- تطبيق آلية rate limiting على طلبات الاتصال الجديدة من الأقران غير المعروفين.
- إعطاء أولوية للأقران ذوي السمعة العالية أو المشاركين في التوافق عند إدارة قائمة الأقران.
- مراقبة سلوك الشبكة وتنبيه المشغلين عند رصد نشاط إغراق غير طبيعي.
