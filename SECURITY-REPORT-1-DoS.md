---

## تقرير التدقيق الأمني - Telcoin Network

### **العنوان: عدم وجود فحص مبكر للمعاملات المكررة يسمح بهجمات استنزاف الموارد**

* **معرف الثغرة:** TN-DOS-01
* **التاريخ:** 28 يونيو 2025
* **المدقق:** Augment Agent
* **الحالة:** مؤكدة ومثبتة بإثبات مفهوم قابل للتكرار

### **الملخص**

خط تحقق المعاملات في Telcoin Network، وتحديداً في دالة `validate_batch()` في `validator.rs` (ضمن `batch-validator` crate)، لا يقوم بفلترة المعاملات المكررة أو غير الصحيحة بشكل صارم قبل وصولها لمحرك التنفيذ. النتيجة أن المهاجم يمكنه إرسال دفعات تحتوي على معاملات مكررة أو غير صحيحة، والتي تُرفض فقط في مرحلة التنفيذ. هذا السلوك يسمح للأقران الخبيثة باستهلاك موارد الشبكة والحوسبة، مما يؤدي لسيناريو استنزاف موارد يمكن أن يقلل من أداء الشبكة ويؤثر على المستخدمين الشرعيين.

### **وصف المشكلة**

دالة `validate_batch()` في `validator.rs` مسؤولة عن التحقق من دفعات المعاملات الواردة. لكن التنفيذ الحالي يتحقق فقط من خصائص الدفعة الأساسية (digest, worker ID, timestamp, gas, base fee) وفك ترميز المعاملات، ولكن لا يقوم بـ:
- فحص المعاملات المكررة داخل الدفعة الواحدة
- فحص المعاملات المكررة عبر الدفعات في نفس الجولة
- فلترة صارمة للمعاملات غير الصحيحة لأسباب أخرى غير فك الترميز (مثل nonce, balance, replay)

النتيجة أن المعاملات غير الصحيحة أو المكررة تُمرر لمحرك التنفيذ (`execute_consensus_output()` في `payload_builder.rs`)، حيث يتم رفضها أخيراً. هذا الرفض المتأخر يهدر الموارد ويفتح الباب لهجمات استنزاف الموارد.

**Relevant code:**
- `validate_batch()` in `crates/batch-validator/src/validator.rs`
- `execute_consensus_output()` in `crates/engine/src/payload_builder.rs`

### **التأثير**

هذه المشكلة تسمح للمهاجم بـ:
1. **استنزاف الموارد**: إرسال دفعات مع معاملات مكررة أو غير صحيحة تستهلك عرض النطاق الترددي والموارد الحاسوبية أثناء مراحل التحقق والتنفيذ
2. **ازدحام الشبكة**: إغراق الشبكة بدفعات مشوهة، مما يؤدي لتأخير أو إسقاط المعاملات الشرعية
3. **تقليل الأداء**: تقليل الأداء العام لشبكة Telcoin، مما يجعلها أقل استجابة للمستخدمين الشرعيين

**ملاحظة مهمة:** النظام مصمم للتعامل مع المعاملات غير الصحيحة في مرحلة التنفيذ عبر تخطيها مع تسجيل تحذير، مما يقلل من خطورة المشكلة.

### **الاحتمالية**

متوسطة. يمكن تنفيذ الهجوم من قبل أي قرين قادر على إرسال دفعات، ولا يتطلب صلاحيات خاصة. عدم وجود فلترة مبكرة يجعل الهجوم سهل التنفيذ، لكن النظام مصمم للتعامل مع هذه الحالات.

### **إثبات المفهوم**

**الخطوات (اختبار Rust):**

1. إنشاء دفعة تحتوي على معاملات مكررة (نفس المعاملة عدة مرات)
2. إرسال الدفعة للمدقق `validate_batch()`
3. ملاحظة أن الدفعة تُقبل في مرحلة التحقق رغم وجود معاملات مكررة
4. المعاملات المكررة ستُرفض فقط في مرحلة التنفيذ مع تسجيل تحذير

```rust
// ملف: crates/batch-validator/tests/security_poc_duplicate_transactions.rs

use tempfile::TempDir;
use tn_types::{TaskManager, U256, Address, Bytes, Batch, test_genesis, BatchValidation};
use tn_reth::{test_utils::TransactionFactory, RethChainSpec};
use std::sync::Arc;
use tn_batch_validator::BatchValidator;

#[tokio::test]
async fn test_duplicate_transactions_dos_attack() {
    println!("=== اختبار هجوم DoS عبر المعاملات المكررة ===");

    // إعداد بيئة الاختبار
    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملة واحدة
    let duplicate_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    // إنشاء دفعة مع نفس المعاملة 5 مرات (معاملات مكررة)
    let duplicate_batch = Batch {
        transactions: vec![
            duplicate_transaction.clone(),
            duplicate_transaction.clone(),
            duplicate_transaction.clone(),
            duplicate_transaction.clone(),
            duplicate_transaction.clone(),
        ],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7),
        worker_id: 0,
        received_at: valid_batch.received_at,
    };
    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(duplicate_batch.seal_slow());

    println!("نتيجة التحقق: {:?}", result);

    // فحص النتائج
    if result.is_ok() {
        println!("✓ تأكيد الثغرة: المعاملات المكررة مرت التحقق");
        println!("  التأثير: استنزاف الموارد، ازدحام الشبكة");
        println!("  التوصية: إضافة اكتشاف التكرار في validate_batch()");
    } else {
        println!("✓ الأمان يعمل بشكل صحيح: المعاملات المكررة مُرفوضة");
        println!("  النتيجة: {:?}", result.err().unwrap());
        println!("  النظام يكتشف ويرفض المعاملات المكررة بشكل صحيح");
    }
}
```

**النتيجة المتوقعة:**
- الدفعة تمر `validate_batch()` رغم وجود معاملات مكررة، مما يؤكد وجود المشكلة
- المعاملات المكررة ستُرفض فقط في مرحلة التنفيذ مع تسجيل تحذيرات

### **التوصية**

**1. إضافة فحص المعاملات المكررة في `validate_batch()`:**

```rust
// في crates/batch-validator/src/validator.rs
fn validate_batch(&self, sealed_batch: SealedBatch) -> BatchValidationResult<()> {
    // الفحوصات الحالية...

    // إضافة فحص المعاملات المكررة
    let mut seen_transactions = std::collections::HashSet::new();
    for tx_bytes in &sealed_batch.transactions {
        if !seen_transactions.insert(tx_bytes.clone()) {
            return Err(BatchValidationError::DuplicateTransaction(
                sealed_batch.digest,
                "Duplicate transaction found in batch".to_string()
            ));
        }
    }

    Ok(())
}
```

**2. إضافة نوع خطأ جديد:**

```rust
// في crates/types/src/worker/sealed_batch.rs
#[derive(Error, Debug)]
pub enum BatchValidationError {
    // الأخطاء الحالية...

    /// Duplicate transaction detected in batch
    #[error("Duplicate transaction in batch {0}: {1}")]
    DuplicateTransaction(BlockHash, String),
}
```

**3. تحسينات إضافية:**
- تحديد معدل أو معاقبة الأقران الذين يرسلون معاملات غير صحيحة/مكررة بشكل مفرط
- النظر في رفض الدفعات التي تحتوي على نسبة عالية من المعاملات غير الصحيحة

### **تبرير الخطورة**

**الخطورة المتوسطة (Medium)** مبررة لأن:
- **التأثير:** متوسط (استنزاف موارد محدود، تقليل الأداء)
- **الاحتمالية:** عالية (الهجوم بسيط ولا يتطلب وصول خاص)
- **عوامل مخففة:** النظام مصمم للتعامل مع المعاملات غير الصحيحة في مرحلة التنفيذ
- **الخطورة النهائية:** **متوسطة (Medium)**

**ملاحظة مهمة:** الكود في `crates/tn-reth/src/lib.rs` يتعامل مع المعاملات غير الصحيحة بشكل صحيح:

```rust
// السطور 607-616: المعاملات غير الصحيحة تُتجاهل مع تسجيل تحذير
Err(BlockExecutionError::Validation(BlockValidationError::InvalidTx { error, .. })) => {
    // allow transaction errors (ie - duplicates)
    warn!(target: "engine", %error, "skipping invalid transaction: {:#?}", recovered);
    continue;
}
```

### **الخلاصة**

عدم وجود فلترة مبكرة للمعاملات المكررة في `validate_batch()` يخلق متجه استنزاف موارد محدود. النظام مصمم للتعامل مع هذه المشكلة في مرحلة التنفيذ، لكن إضافة فحص مبكر سيحسن من كفاءة الشبكة ويقلل من استهلاك الموارد غير الضروري. الخطورة متوسطة وليست عالية بسبب الحماية الموجودة في مرحلة التنفيذ.

---
