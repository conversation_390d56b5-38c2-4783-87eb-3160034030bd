---

## تقرير التدقيق الأمني - Telcoin Network

### **العنوان: عدم وجود فحص مبكر للمعاملات المكررة يسمح بهجمات استنزاف الموارد**

* **معرف الثغرة:** TN-DOS-01
* **التاريخ:** 28 يونيو 2025
* **المدقق:** Augment Agent
* **الحالة:** مؤكدة ومثبتة بإثبات مفهوم قابل للتكرار

### **الملخص**

خط تحقق المعاملات في Telcoin Network، وتحديداً في دالة `validate_batch()` في `validator.rs` (ضمن `batch-validator` crate)، لا يقوم بفلترة المعاملات المكررة أو غير الصحيحة بشكل صارم قبل وصولها لمحرك التنفيذ. النتيجة أن المهاجم يمكنه إرسال دفعات تحتوي على معاملات مكررة أو غير صحيحة، والتي تُرفض فقط في مرحلة التنفيذ. هذا السلوك يسمح للأقران الخبيثة باستهلاك موارد الشبكة والحوسبة، مما يؤدي لسيناريو استنزاف موارد يمكن أن يقلل من أداء الشبكة ويؤثر على المستخدمين الشرعيين.

### **Finding Description**

The function `validate_batch()` in `validator.rs` is responsible for validating incoming transaction batches. However, the current implementation only checks for basic batch properties (digest, worker ID, timestamp, gas, base fee) and decodes transactions, but does not:
- Check for duplicate transactions within a batch.
- Check for duplicate transactions across batches in the same round.
- Strictly filter out transactions that are invalid for reasons other than decoding (e.g., nonce, balance, replay).

As a result, invalid or duplicate transactions are passed to the execution engine (`execute_consensus_output()` in `payload_builder.rs`), where they are finally rejected. This late rejection wastes resources and opens the door for DoS attacks.

**Relevant code:**
- `validate_batch()` in `crates/batch-validator/src/validator.rs`
- `execute_consensus_output()` in `crates/engine/src/payload_builder.rs`

### **Impact**

- Network and node resources (CPU, memory, disk) are consumed by processing and relaying invalid/duplicate transactions.
- Throughput for legitimate transactions is reduced.
- Attackers can degrade network performance or cause partial denial of service.

### **Likelihood**

High. The attack can be performed by any peer able to submit batches, and does not require special privileges. The lack of early filtering makes the attack trivial to execute.

### **Proof of Concept**

**Step-by-step (Rust test):**

1. Create a batch with a transaction whose gas limit exceeds the allowed maximum (invalid transaction).
2. Submit the batch to the validator.
3. Observe that the batch is accepted by `validate_batch()` but rejected only at execution.

```rust
#[tokio::test]
async fn test_invalid_batch_excess_gas_used() {
    // Setup test environment
    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { valid_batch, validator } = test_tools(tmp_dir.path(), &task_manager).await;
    let (batch, _) = valid_batch.split();
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    // Create invalid transaction
    let invalid_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(max_batch_gas(batch.timestamp) + 1),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    let invalid_batch = Batch {
        transactions: vec![invalid_transaction],
        parent_hash: batch.parent_hash,
        beneficiary: batch.beneficiary,
        timestamp: batch.timestamp,
        base_fee_per_gas: batch.base_fee_per_gas,
        worker_id: 0,
        received_at: batch.received_at,
    };
    // This passes validation
    let result = validator.validate_batch(invalid_batch.seal_slow());
    assert!(result.is_ok());
    // But will be rejected at execution
}
```

**Expected output:**
- The batch passes `validate_batch()` but fails at execution, confirming the issue.

### **Recommendation**

- Add strict duplicate detection within and across batches in `validate_batch()`.
- Add early validation for transaction correctness (nonce, balance, replay, etc.) before execution.
- Rate-limit or penalize peers submitting excessive invalid/duplicate transactions.
- Consider rejecting batches with a high proportion of invalid transactions.

**Example fix:**
- In `validate_batch()`, iterate over transactions and check for duplicates using a `HashSet`.
- Integrate additional transaction validation logic before passing to the engine.

### **Severity Justification**

- **Impact:** Medium to High (resource exhaustion, degraded service, potential DoS)
- **Likelihood:** High (attack is trivial and does not require special access)
- **Severity:** **High**

### **Conclusion**

The lack of strict pre-execution validation in the batch processing pipeline allows attackers to waste network and node resources by submitting invalid or duplicate transactions. Early filtering and stricter validation are required to mitigate this DoS vector and protect the network's reliability.

---
