{
  "compilerOptions": {
    /* Visit https://aka.ms/tsconfig to read more about this file */

    /* Language and Environment */
    "target": "es2022" /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */,
    /* Modules */
    "module": "nodenext" /* Specify what module code is generated. */,
    "moduleResolution": "nodenext",
    "resolveJsonModule": true,
    "esModuleInterop": true /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */,
    "incremental": true,
    "forceConsistentCasingInFileNames": true /* Ensure that casing is correct in imports. */,
    "declaration": true,
    "outDir": "./node/dist",
    "declarationDir": "./node/dist/types",
    "rootDir": "./node/src",
    "baseUrl": "./node",
    /* Type Checking */
    "strict": true /* Enable all strict type-checking options. */,
    "skipLibCheck": true /* Skip type checking all .d.ts files. */
  },
  "include": ["node/src/**/*"],
  "exclude": ["node_modules", "node/dist"]
}
