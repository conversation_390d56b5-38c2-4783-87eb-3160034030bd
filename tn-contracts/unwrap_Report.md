
# Vulnerability Report: Zero-Value Unwraps

---

## English Version

### **Vulnerability: `unwrap` Function Does Not Prevent Zero-Value Withdrawals**

- **ID:** TEL-L-01
- **Date:** June 26, 2025
- **Auditor:** AI Security Assistant
- **Status:** Confirmed and proven with an executable Proof of Concept.

### **Summary**

The `unwrap()` function in `RecoverableWrapper.sol` (inherited by `InterchainTEL.sol`) allows users to call it with an `amount` of zero. While this does not lead to a direct theft of funds, it represents undesirable behavior and lacks basic input validation. This can be exploited to emit worthless `Unwrap` events, potentially confusing off-chain monitoring systems and allowing for network spam at the cost of only gas fees.

### **Vulnerability Description**

The `unwrap(uint256 amount)` function is an external function that any user can call to redeem underlying `WTEL` tokens by burning their `iTEL` tokens.

**Target Code (`src/recoverable-wrapper/RecoverableWrapper.sol`):**
```solidity
function unwrap(uint256 amount) external override {
    if (unwrapDisabled[msg.sender]) {
        revert UnwrapNotAllowed(msg.sender);
    }
    // 🚨 FLAW: No check to ensure amount > 0

    // _burnSettled(msg.sender, 0) will succeed
    _burnSettled(msg.sender, amount);

    // SafeERC20.safeTransfer(..., 0) will succeed
    SafeERC20.safeTransfer(baseERC20, msg.sender, amount);
    
    // An event is emitted for a zero-value operation
    emit Unwrap(msg.sender, msg.sender, amount);
}
```
**The Flaw:** The function lacks a `require(amount > 0)` check. As a result, any user can call `unwrap(0)`. The transaction will complete successfully, as burning zero tokens and transferring zero tokens are valid operations, and an `Unwrap` event will be emitted with a zero value.

### **Impact**

**Low.** The impact is not directly financial but affects protocol quality and integrity:
1.  **Monitoring System Confusion:** An attacker could flood the network with fake `Unwrap` events, making it difficult for analysis tools and monitoring systems to distinguish real activity from noise.
2.  **Transaction Spam:** The function can be used as a cheap way to add spam data to the blockchain by emitting meaningless events.
3.  **Lack of Best Practices:** Failure to validate basic inputs (like zero amounts) is poor programming practice and may indicate similar oversights in other parts of the codebase.

### **Likelihood**

**High.** Any user can call this function with a zero value at any time. There are no prerequisites or costs other than gas.

### **Proof of Concept (PoC)**

The following test demonstrates that a call to `unwrap(0)` succeeds and emits an event.

#### `test/recoverable-wrapper/RecoverableWrapper_POC_ZeroUnwrap.t.sol`
```solidity
// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ITSTestHelper } from "../ITS/ITSTestHelper.sol";
import { IRecoverableWrapper } from "src/interfaces/IRecoverableWrapper.sol";

/**
 * @title PoC: Zero-Value Unwraps Are Allowed
 * @notice This PoC demonstrates that the unwrap() function can be successfully
 * called with an amount of 0, which emits an event for a no-op action.
 */
contract RecoverableWrapper_ZeroUnwrap_POC is ITSTestHelper {
    address user = makeAddr("user");
    address admin = makeAddr("admin");

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
    }

    function test_POC_CanUnwrapZeroAmount() public {
        // The user has 0 iTEL and 0 wTEL, but can still call unwrap(0).
        uint256 user_iTEL_Balance = iTEL.balanceOf(user);
        assertEq(user_iTEL_Balance, 0);

        // We expect an Unwrap event to be emitted with the amount of 0.
        vm.expectEmit(true, true, true, true);
        emit IRecoverableWrapper.Unwrap(user, user, 0);

        // The call to unwrap(0) should succeed.
        vm.prank(user);
        iTEL.unwrap(0);

        console.log("SUCCESS: unwrap(0) was called successfully and emitted an event.");
    }
}
```

#### **Execution Command & Result**
```bash
forge test --match-path test/recoverable-wrapper/RecoverableWrapper_POC_ZeroUnwrap.t.sol -vv
```
**Result:**
The test passes successfully, confirming the vulnerability.
```
[PASS] test_POC_CanUnwrapZeroAmount() (gas: 74938)
Logs:
  SUCCESS: unwrap(0) was called successfully and emitted an event.

Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 49.58ms
```

### **Recommendation**

A simple `require` check should be added at the beginning of the `unwrap` function to ensure the amount is greater than zero.

**Suggested Fix:**
```solidity
// src/recoverable-wrapper/RecoverableWrapper.sol
function unwrap(uint256 amount) external override {
    // --- FIX: Add zero amount check ---
    require(amount > 0, "Unwrap amount must be greater than zero");

    if (unwrapDisabled[msg.sender]) {
        revert UnwrapNotAllowed(msg.sender);
    }

    _burnSettled(msg.sender, amount);
    SafeERC20.safeTransfer(baseERC20, msg.sender, amount);
    emit Unwrap(msg.sender, msg.sender, amount);
}
```
This simple change prevents valueless operations and makes the contract more robust.

### **Severity Justification**

*   **Impact:** **Low** - No direct financial impact, but it can cause a nuisance and indicates poor code quality.
*   **Likelihood:** **High** - Anyone can exploit it at any time.

The combination of Low Impact and High Likelihood classifies this vulnerability as **Low Severity**.

---
---

## النسخة العربية

### **عنوان الثغرة: وظيفة `unwrap` لا تمنع عمليات السحب بقيمة صفر**

*   **معرف الثغرة:** TEL-L-01
*   **التاريخ:** 26 يونيو 2025
*   **المدقق:** مساعد الذكاء الاصطناعي الأمني
*   **الحالة:** مؤكدة ومثبتة بإثبات مفهوم قابل للتنفيذ.

### **ملخص**

وظيفة `unwrap()` في عقد `RecoverableWrapper.sol` (الموروث من `InterchainTEL.sol`) تسمح للمستخدمين باستدعائها بقيمة `amount` تساوي صفرًا. على الرغم من أن هذا لا يؤدي إلى سرقة مباشرة للأموال، إلا أنه يمثل سلوكًا غير مرغوب فيه ويفتقر إلى التحقق الأساسي من صحة المدخلات. يمكن استغلال هذا السلوك لإصدار أحداث `Unwrap` عديمة القيمة، مما قد يربك أنظمة المراقبة خارج السلسلة (off-chain) ويسمح بإرسال معاملات غير مرغوب فيها (spam) إلى الشبكة دون أي تكلفة سوى رسوم الغاز.

### **وصف الثغرة**

وظيفة `unwrap(uint256 amount)` هي وظيفة عامة (`external`) يمكن لأي مستخدم استدعاؤها لاسترداد الرموز الأساسية (`WTEL`) مقابل حرق رموز `iTEL` الخاصة به.

**الكود المستهدف (`src/recoverable-wrapper/RecoverableWrapper.sol`):**
```solidity
function unwrap(uint256 amount) external override {
    if (unwrapDisabled[msg.sender]) {
        revert UnwrapNotAllowed(msg.sender);
    }
    // 🚨 FLAW: No check to ensure amount > 0

    // _burnSettled(msg.sender, 0) will succeed
    _burnSettled(msg.sender, amount);

    // SafeERC20.safeTransfer(..., 0) will succeed
    SafeERC20.safeTransfer(baseERC20, msg.sender, amount);
    
    // An event is emitted for a zero-value operation
    emit Unwrap(msg.sender, msg.sender, amount);
}
```
**الخلل الرئيسي:** تفتقر الوظيفة إلى شرط `require(amount > 0)`. نتيجة لذلك، يمكن لأي مستخدم استدعاء `unwrap(0)`. ستكتمل المعاملة بنجاح، حيث أن حرق صفر من الرموز وتحويل صفر من الرموز هي عمليات صالحة، وسيتم إصدار حدث `Unwrap` بقيمة صفر.

### **التأثير (Impact)**

**منخفض (Low).** التأثير ليس ماليًا مباشرًا، ولكنه يؤثر على جودة وسلامة البروتوكول:
1.  **إرباك أنظمة المراقبة:** يمكن للمهاجم إغراق الشبكة بأحداث `Unwrap` وهمية، مما يجعل من الصعب على أدوات التحليل والمراقبة التمييز بين النشاط الحقيقي والضوضاء.
2.  **معاملات غير مرغوب فيها (Spam):** يمكن استخدام هذه الوظيفة كطريقة رخيصة لإضافة بيانات غير مرغوب فيها إلى البلوكتشين عن طريق إصدار أحداث لا معنى لها.
3.  **نقص في أفضل الممارسات:** عدم التحقق من المدخلات الأساسية (مثل المبالغ الصفرية) يعتبر ممارسة سيئة في البرمجة وقد يشير إلى وجود إغفالات مماثلة في أجزاء أخرى من الكود.

### **الاحتمالية (Likelihood)**

**عالية (High).** يمكن لأي مستخدم استدعاء هذه الوظيفة بقيمة صفر في أي وقت. لا توجد أي شروط مسبقة أو تكلفة (بخلاف الغاز).

### **إثبات المفهوم (Proof of Concept)**

الاختبار التالي يوضح أن استدعاء `unwrap(0)` ينجح ويصدر حدثًا.

#### `test/recoverable-wrapper/RecoverableWrapper_POC_ZeroUnwrap.t.sol`
```solidity
// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ITSTestHelper } from "../ITS/ITSTestHelper.sol";
import { IRecoverableWrapper } from "src/interfaces/IRecoverableWrapper.sol";

/**
 * @title PoC: Zero-Value Unwraps Are Allowed
 * @notice This PoC demonstrates that the unwrap() function can be successfully
 * called with an amount of 0, which emits an event for a no-op action.
 */
contract RecoverableWrapper_ZeroUnwrap_POC is ITSTestHelper {
    address user = makeAddr("user");
    address admin = makeAddr("admin");

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
    }

    function test_POC_CanUnwrapZeroAmount() public {
        // The user has 0 iTEL and 0 wTEL, but can still call unwrap(0).
        uint256 user_iTEL_Balance = iTEL.balanceOf(user);
        assertEq(user_iTEL_Balance, 0);

        // We expect an Unwrap event to be emitted with the amount of 0.
        vm.expectEmit(true, true, true, true);
        emit IRecoverableWrapper.Unwrap(user, user, 0);

        // The call to unwrap(0) should succeed.
        vm.prank(user);
        iTEL.unwrap(0);

        console.log("SUCCESS: unwrap(0) was called successfully and emitted an event.");
    }
}
```

#### **أوامر التشغيل والنتيجة**
```bash
forge test --match-path test/recoverable-wrapper/RecoverableWrapper_POC_ZeroUnwrap.t.sol -vv
```
**النتيجة:**
يجتاز الاختبار بنجاح، مما يؤكد وجود الثغرة.
```
[PASS] test_POC_CanUnwrapZeroAmount() (gas: 74938)
Logs:
  SUCCESS: unwrap(0) was called successfully and emitted an event.

Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 49.58ms
```

### **التوصية (Recommendation)**

يجب إضافة شرط تحقق بسيط في بداية وظيفة `unwrap` للتأكد من أن المبلغ أكبر من صفر.

**اقتراح الإصلاح:**
```solidity
// src/recoverable-wrapper/RecoverableWrapper.sol
function unwrap(uint256 amount) external override {
    // --- FIX: Add zero amount check ---
    require(amount > 0, "Unwrap amount must be greater than zero");

    if (unwrapDisabled[msg.sender]) {
        revert UnwrapNotAllowed(msg.sender);
    }

    _burnSettled(msg.sender, amount);
    SafeERC20.safeTransfer(baseERC20, msg.sender, amount);
    emit Unwrap(msg.sender, msg.sender, amount);
}
```
هذا التغيير البسيط يمنع العمليات عديمة القيمة ويجعل العقد أكثر قوة.

### **تبرير درجة الخطورة**

*   **التأثير (Impact):** **منخفض (Low)** - لا يوجد تأثير مالي مباشر، ولكن يمكن أن يسبب إزعاجًا (nuisance) ويشير إلى ضعف في جودة الكود.
*   **الاحتمالية (Likelihood):** **عالية (High)** - يمكن لأي شخص استغلالها.

الجمع بين التأثير المنخفض والاحتمالية العالية يصنف هذه الثغرة على أنها **منخفضة الخطورة (Low Severity)**.
