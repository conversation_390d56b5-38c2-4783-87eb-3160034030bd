// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ConsensusRegistryTestUtils } from "./ConsensusRegistryTestUtils.sol";
import { ConsensusRegistry } from "src/consensus/ConsensusRegistry.sol";
import { RewardInfo } from "src/interfaces/IStakeManager.sol";

/**
 * @title Response to Friend's Analysis
 * @notice This test proves that the friend's mathematical analysis is incorrect
 */
contract ConsensusRegistry_FriendAnalysis_Response is Test, ConsensusRegistryTestUtils {
    address systemAddress;

    function setUp() public {
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);
        systemAddress = consensusRegistry.SYSTEM_ADDRESS();
        
        // Fund the issuance contract
        vm.deal(crOwner, epochIssuance_);
        vm.prank(crOwner);
        consensusRegistry.allocateIssuance{value: epochIssuance_}();
    }

    function test_ProveZeroRewards_ActualContract() public {
        console.log("=== Proving Zero Rewards with ACTUAL Contract Call ===");
        
        // Get current system values
        uint256 currentEpochIssuance = consensusRegistry.getCurrentEpochInfo().epochIssuance;
        uint256 currentStakeAmount = consensusRegistry.getCurrentStakeConfig().stakeAmount;
        
        console.log("Current epoch issuance: %s wei", currentEpochIssuance);
        console.log("Current stake amount: %s wei", currentStakeAmount);
        
        // Create realistic scenario with 100 validators, 1 header each
        uint256 numValidators = 100;
        uint256 headersPerValidator = 1;

        // Calculate expected values
        uint256 expectedWeight = currentStakeAmount * headersPerValidator;
        uint256 expectedTotalWeight = numValidators * expectedWeight;
        uint256 expectedReward = (currentEpochIssuance * expectedWeight) / expectedTotalWeight;

        console.log("Expected weight per validator: %s", expectedWeight);
        console.log("Expected total weight: %s", expectedTotalWeight);
        console.log("Expected reward per validator: %s", expectedReward);
        
        // Create reward infos
        RewardInfo[] memory rewardInfos = new RewardInfo[](numValidators);
        for (uint256 i = 0; i < numValidators; i++) {
            address validator = makeAddr(string(abi.encodePacked("validator", i)));
            // Mint validator first
            vm.prank(crOwner);
            consensusRegistry.mint(validator);

            // Check if validator is retired (this is the problem!)
            bool retired = consensusRegistry.isRetired(validator);
            console.log("Validator %s is retired: %s", i, retired);

            rewardInfos[i] = RewardInfo(validator, headersPerValidator);
        }
        
        // Get initial rewards for first validator
        uint256 initialReward = consensusRegistry.getRewards(rewardInfos[0].validatorAddress);
        console.log("Initial reward for validator 0: %s", initialReward);
        
        // Apply incentives using ACTUAL contract
        vm.prank(systemAddress);
        consensusRegistry.applyIncentives(rewardInfos);
        
        // Get final rewards
        uint256 finalReward = consensusRegistry.getRewards(rewardInfos[0].validatorAddress);
        uint256 actualReward = finalReward - initialReward;
        
        console.log("Final reward for validator 0: %s", finalReward);
        console.log("Actual reward received: %s", actualReward);
        
        // Friend's claim: rewards should be zero
        if (actualReward == 0) {
            console.log("FRIEND IS CORRECT: Rewards are zero");
        } else {
            console.log("FRIEND IS WRONG: Rewards are NOT zero");
            console.log("Expected reward per validator (friend's formula): %s", currentEpochIssuance / numValidators);
            console.log("Actual reward per validator: %s", actualReward);
        }
        
        // Calculate total distributed
        uint256 totalDistributed = 0;
        for (uint256 i = 0; i < numValidators; i++) {
            uint256 validatorReward = consensusRegistry.getRewards(rewardInfos[i].validatorAddress);
            totalDistributed += validatorReward;
        }
        
        console.log("Total distributed: %s", totalDistributed);
        console.log("Expected total: %s", currentEpochIssuance);
        
        // Verify the math
        uint256 expectedPerValidator = currentEpochIssuance / numValidators;
        console.log("Expected per validator (simple division): %s", expectedPerValidator);
        console.log("Actual per validator: %s", actualReward);
        
        // The key test: are rewards actually zero?
        assertGt(actualReward, 0, "Rewards should NOT be zero - friend's analysis is wrong");
    }

    function test_FriendsMathematicalError() public {
        console.log("=== Demonstrating Friend's Mathematical Error ===");
        
        uint256 epochIssuance = 25806e18; // Current epoch issuance
        uint256 stakeAmount = 1000000e18; // Current stake amount
        uint256 numValidators = 3;
        uint256 headersPerValidator = 1;
        
        console.log("Scenario: %s validators, %s headers each", numValidators, headersPerValidator);
        console.log("Epoch issuance: %s", epochIssuance);
        console.log("Stake amount: %s", stakeAmount);
        
        // Friend's incorrect calculation
        console.log("\n=== Friend's Incorrect Calculation ===");
        uint256 friendTotalWeight = numValidators * stakeAmount * headersPerValidator;
        uint256 friendReward = epochIssuance / friendTotalWeight; // This is WRONG!
        console.log("Friend's total weight: %s", friendTotalWeight);
        console.log("Friend's reward calculation: %s", friendReward);
        console.log("Friend's result: %s (ZERO)", friendReward);
        
        // Correct calculation
        console.log("\n=== Correct Calculation ===");
        uint256 correctWeight = stakeAmount * headersPerValidator;
        uint256 correctTotalWeight = numValidators * correctWeight;
        uint256 correctReward = (epochIssuance * correctWeight) / correctTotalWeight;
        console.log("Correct weight per validator: %s", correctWeight);
        console.log("Correct total weight: %s", correctTotalWeight);
        console.log("Correct reward calculation: (%s * %s) / %s", epochIssuance, correctWeight, correctTotalWeight);
        console.log("Correct result: %s", correctReward);
        
        // Simplified form
        uint256 simplifiedReward = epochIssuance / numValidators;
        console.log("Simplified (stakeAmount cancels out): %s", simplifiedReward);
        
        // Verify they match
        assertEq(correctReward, simplifiedReward, "Correct calculation should match simplified form");
        assertGt(correctReward, 0, "Rewards should NOT be zero");
        
        console.log("\n=== Conclusion ===");
        console.log("Friend's error: Used epochIssuance / totalWeight instead of (epochIssuance * weight) / totalWeight");
        console.log("The stakeAmount DOES cancel out when all validators have same stake and headers");
        console.log("But the reward is NOT zero - it's epochIssuance / numValidators");
    }

    function test_ActualContractBehavior_MultipleScenarios() public {
        console.log("=== Testing Actual Contract Behavior ===");
        
        uint256[] memory validatorCounts = new uint256[](4);
        validatorCounts[0] = 3;
        validatorCounts[1] = 10;
        validatorCounts[2] = 100;
        validatorCounts[3] = 1000;
        
        for (uint256 v = 0; v < validatorCounts.length; v++) {
            uint256 numValidators = validatorCounts[v];
            console.log("\n--- Testing with %s validators ---", numValidators);
            
            // Reset state
            setUp();
            
            // Create validators and reward infos
            RewardInfo[] memory rewardInfos = new RewardInfo[](numValidators);
            for (uint256 i = 0; i < numValidators; i++) {
                address validator = makeAddr(string(abi.encodePacked("validator", v, "_", i)));
                vm.prank(crOwner);
                consensusRegistry.mint(validator);
                rewardInfos[i] = RewardInfo(validator, 1); // 1 header each
            }
            
            // Apply incentives
            vm.prank(systemAddress);
            consensusRegistry.applyIncentives(rewardInfos);
            
            // Check first validator's reward
            uint256 actualReward = consensusRegistry.getRewards(rewardInfos[0].validatorAddress);
            uint256 expectedReward = epochIssuance_ / numValidators;
            
            console.log("Expected reward per validator: %s", expectedReward);
            console.log("Actual reward per validator: %s", actualReward);
            
            if (actualReward == 0) {
                console.log("ZERO REWARDS CONFIRMED");
            } else {
                console.log("NON-ZERO REWARDS - Friend's analysis is wrong");
                
                // Calculate precision loss
                uint256 totalExpected = expectedReward * numValidators;
                uint256 precisionLoss = epochIssuance_ - totalExpected;
                console.log("Precision loss: %s wei", precisionLoss);
            }
            
            // Verify rewards are reasonable
            if (numValidators <= 1000) { // For reasonable validator counts
                assertGt(actualReward, 0, "Rewards should not be zero for reasonable validator counts");
            }
        }
    }

    function test_CommentInCode_Explanation() public {
        console.log("=== Explaining the Comment in Code ===");
        
        // The comment says: "will be 0 if `epochIssuance` is too small or `totalWeight` too large"
        // Let's test when this actually happens
        
        uint256 smallEpochIssuance = 1000; // Very small issuance
        uint256 largeStakeAmount = 1000000e18; // Large stake
        uint256 manyValidators = 1000000; // Many validators
        uint256 manyHeaders = 1000; // Many headers each
        
        console.log("Testing extreme scenario:");
        console.log("Small epoch issuance: %s", smallEpochIssuance);
        console.log("Large stake amount: %s", largeStakeAmount);
        console.log("Many validators: %s", manyValidators);
        console.log("Many headers each: %s", manyHeaders);
        
        uint256 weight = largeStakeAmount * manyHeaders;
        uint256 totalWeight = manyValidators * weight;
        uint256 rewardAmount = (smallEpochIssuance * weight) / totalWeight;
        
        console.log("Weight per validator: %s", weight);
        console.log("Total weight: %s", totalWeight);
        console.log("Reward amount: %s", rewardAmount);
        
        if (rewardAmount == 0) {
            console.log("YES: In extreme cases, rewards can be zero");
            console.log("But this requires EXTREME parameters, not the current system config");
        }
        
        // Test with current config
        uint256 currentEpochIssuance = epochIssuance_;
        uint256 currentStakeAmount = stakeAmount_;
        uint256 reasonableValidators = 1000;
        uint256 reasonableHeaders = 10;
        
        uint256 currentWeight = currentStakeAmount * reasonableHeaders;
        uint256 currentTotalWeight = reasonableValidators * currentWeight;
        uint256 currentReward = (currentEpochIssuance * currentWeight) / currentTotalWeight;
        
        console.log("\nWith current system config:");
        console.log("Current epoch issuance: %s", currentEpochIssuance);
        console.log("Current stake amount: %s", currentStakeAmount);
        console.log("Reasonable validators: %s", reasonableValidators);
        console.log("Reasonable headers: %s", reasonableHeaders);
        console.log("Resulting reward: %s", currentReward);
        
        assertGt(currentReward, 0, "Current config should NOT result in zero rewards");
    }
}
