// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ConsensusRegistryTestUtils } from "./ConsensusRegistryTestUtils.sol";
import { ConsensusRegistry } from "src/consensus/ConsensusRegistry.sol";
import { IConsensusRegistry } from "src/interfaces/IConsensusRegistry.sol";
import { RewardInfo } from "src/interfaces/IStakeManager.sol";

/**
 * @title Analysis: Precision Loss in applyIncentives
 * @notice This test analyzes the claimed precision loss vulnerability
 */
contract ConsensusRegistry_PrecisionLoss_Analysis is Test, ConsensusRegistryTestUtils {
    address systemAddress;

    function setUp() public {
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);
        systemAddress = consensusRegistry.SYSTEM_ADDRESS();
    }

    function test_PrecisionLoss_ExactScenario() public {
        console.log("=== Testing Exact Scenario from Report ===");

        // Test the exact scenario: 1,000,000,000 wei divided by 3 validators
        // This should result in 333,333,333 per validator with 1 wei lost

        // Create 3 validators with equal stake and consensus headers
        address[] memory validators = new address[](3);
        validators[0] = validator1;
        validators[1] = validator2;
        validators[2] = validator3;
        
        // Create reward infos with equal weight (1 header each)
        RewardInfo[] memory rewardInfos = new RewardInfo[](3);
        rewardInfos[0] = RewardInfo(validators[0], 1);
        rewardInfos[1] = RewardInfo(validators[1], 1);
        rewardInfos[2] = RewardInfo(validators[2], 1);
        
        // Get initial rewards
        uint256[] memory rewardsBefore = new uint256[](3);
        for (uint256 i = 0; i < 3; i++) {
            rewardsBefore[i] = consensusRegistry.getRewards(validators[i]);
            console.log("Validator %s rewards before: %s", i, rewardsBefore[i]);
        }
        
        // Apply incentives
        vm.prank(systemAddress);
        consensusRegistry.applyIncentives(rewardInfos);
        
        // Check rewards after
        uint256[] memory rewardsAfter = new uint256[](3);
        uint256 totalDistributed = 0;
        for (uint256 i = 0; i < 3; i++) {
            rewardsAfter[i] = consensusRegistry.getRewards(validators[i]);
            uint256 rewardIncrease = rewardsAfter[i] - rewardsBefore[i];
            totalDistributed += rewardIncrease;
            console.log("Validator %s rewards after: %s (increase: %s)", i, rewardsAfter[i], rewardIncrease);
        }
        
        // Simulate the exact calculation from the report
        uint256 simulatedEpochIssuance = 1_000_000_000;
        uint256 totalWeight = 3; // 3 validators with equal weight
        uint256 expectedPerValidator = simulatedEpochIssuance / totalWeight; // 333,333,333
        uint256 expectedTotal = expectedPerValidator * 3; // 999,999,999
        uint256 expectedLoss = simulatedEpochIssuance - expectedTotal; // 1 wei

        console.log("=== Simulation of Report Scenario ===");
        console.log("Simulated epoch issuance: %s", simulatedEpochIssuance);
        console.log("Expected per validator: %s", expectedPerValidator);
        console.log("Expected total distributed: %s", expectedTotal);
        console.log("Expected precision loss: %s wei", expectedLoss);

        console.log("=== Actual System Results ===");
        console.log("Total distributed: %s", totalDistributed);

        // The actual system uses much larger numbers, so no precision loss occurs
        console.log("No precision loss in current system configuration");
    }

    function test_PrecisionLoss_ReportScenario() public {
        console.log("=== Testing Report's Exact Mathematical Scenario ===");

        // This test demonstrates the mathematical precision loss described in the report
        uint256 epochIssuance = 1_000_000_000; // 1 billion wei
        uint256 numValidators = 3;
        uint256 weightPerValidator = 1; // Equal weight
        uint256 totalWeight = numValidators * weightPerValidator;

        console.log("Epoch issuance: %s wei", epochIssuance);
        console.log("Number of validators: %s", numValidators);
        console.log("Total weight: %s", totalWeight);

        // Calculate what each validator should get
        uint256 rewardPerValidator = epochIssuance / totalWeight;
        uint256 totalDistributed = rewardPerValidator * numValidators;
        uint256 precisionLoss = epochIssuance - totalDistributed;

        console.log("Reward per validator: %s wei", rewardPerValidator);
        console.log("Total distributed: %s wei", totalDistributed);
        console.log("Precision loss: %s wei", precisionLoss);

        // Verify the report's calculation
        assertEq(rewardPerValidator, 333_333_333, "Each validator should get 333,333,333 wei");
        assertEq(totalDistributed, 999_999_999, "Total distributed should be 999,999,999 wei");
        assertEq(precisionLoss, 1, "Precision loss should be exactly 1 wei");

        console.log("CONFIRMED: Report's mathematical analysis is correct");
        console.log("With 1B wei and 3 validators, 1 wei is lost to precision");
    }

    function test_PrecisionLoss_CurrentSystemValues() public {
        console.log("=== Testing Current System Configuration ===");

        // Get current system values
        uint256 currentEpochIssuance = consensusRegistry.getCurrentEpochInfo().epochIssuance;
        uint256 currentStakeAmount = consensusRegistry.getCurrentStakeConfig().stakeAmount;

        console.log("Current epoch issuance: %s wei", currentEpochIssuance);
        console.log("Current stake amount: %s wei", currentStakeAmount);

        // Test with different numbers of validators
        uint256[] memory validatorCounts = new uint256[](5);
        validatorCounts[0] = 3;
        validatorCounts[1] = 7;
        validatorCounts[2] = 13;
        validatorCounts[3] = 101;
        validatorCounts[4] = 1000;

        for (uint256 i = 0; i < validatorCounts.length; i++) {
            uint256 numValidators = validatorCounts[i];
            uint256 totalWeight = numValidators * currentStakeAmount; // Assuming equal headers
            uint256 rewardPerValidator = currentEpochIssuance / totalWeight;
            uint256 totalDistributed = rewardPerValidator * numValidators;
            uint256 precisionLoss = currentEpochIssuance - totalDistributed;

            console.log("\n--- %s validators ---", numValidators);
            console.log("Total weight: %s", totalWeight);
            console.log("Reward per validator: %s wei", rewardPerValidator);
            console.log("Total distributed: %s wei", totalDistributed);
            console.log("Precision loss: %s wei", precisionLoss);

            if (rewardPerValidator == 0) {
                console.log("CRITICAL: Rewards become 0 due to precision loss!");
                console.log("This is worse than the report describes - no rewards at all!");
            } else if (precisionLoss > 0) {
                console.log("WARNING: Precision loss detected with current system values!");
            } else {
                console.log("OK: No precision loss with current system values");
            }
        }
    }

    function test_PrecisionLoss_RealWorldScenario() public {
        console.log("=== Testing Real World Scenario ===");

        // Use more realistic values that might cause precision loss but not zero rewards
        uint256 testEpochIssuance = 1000e18; // 1000 ETH
        uint256 testStakeAmount = 100e18;    // 100 ETH per validator

        console.log("Test epoch issuance: %s wei (%s ETH)", testEpochIssuance, testEpochIssuance / 1e18);
        console.log("Test stake amount: %s wei (%s ETH)", testStakeAmount, testStakeAmount / 1e18);

        // Test with different numbers of validators
        uint256[] memory validatorCounts = new uint256[](6);
        validatorCounts[0] = 3;
        validatorCounts[1] = 7;
        validatorCounts[2] = 13;
        validatorCounts[3] = 37;
        validatorCounts[4] = 101;
        validatorCounts[5] = 333;

        for (uint256 i = 0; i < validatorCounts.length; i++) {
            uint256 numValidators = validatorCounts[i];
            uint256 totalWeight = numValidators * testStakeAmount; // Assuming equal headers
            uint256 rewardPerValidator = (testEpochIssuance * testStakeAmount) / totalWeight;
            uint256 totalDistributed = rewardPerValidator * numValidators;
            uint256 precisionLoss = testEpochIssuance - totalDistributed;

            console.log("\n--- %s validators ---", numValidators);
            console.log("Total weight: %s", totalWeight);
            console.log("Reward per validator: %s wei (%s ETH)", rewardPerValidator, rewardPerValidator / 1e18);
            console.log("Total distributed: %s wei (%s ETH)", totalDistributed, totalDistributed / 1e18);
            console.log("Precision loss: %s wei", precisionLoss);

            if (rewardPerValidator == 0) {
                console.log("CRITICAL: Rewards become 0!");
            } else if (precisionLoss > 0) {
                console.log("WARNING: Precision loss detected");
                console.log("Loss percentage: %s basis points", (precisionLoss * 10000) / testEpochIssuance);
            } else {
                console.log("OK: No precision loss");
            }
        }
    }

    function test_PrecisionLoss_MultipleEpochs() public {
        console.log("=== Testing Precision Loss Over Multiple Epochs ===");
        
        uint256 testEpochIssuance = 1_000_000_000;
        uint256 numEpochs = 10;
        
        // Create 3 validators
        address[] memory validators = new address[](3);
        validators[0] = validator1;
        validators[1] = validator2;
        validators[2] = validator3;
        
        // Fund the issuance contract for multiple epochs
        vm.deal(address(consensusRegistry.issuance()), testEpochIssuance * numEpochs);
        
        uint256 totalLoss = 0;
        
        for (uint256 epoch = 0; epoch < numEpochs; epoch++) {
            console.log("\n--- Epoch %s ---", epoch);
            
            // Create reward infos
            RewardInfo[] memory rewardInfos = new RewardInfo[](3);
            rewardInfos[0] = RewardInfo(validators[0], 1);
            rewardInfos[1] = RewardInfo(validators[1], 1);
            rewardInfos[2] = RewardInfo(validators[2], 1);
            
            // Get rewards before
            uint256[] memory rewardsBefore = new uint256[](3);
            for (uint256 i = 0; i < 3; i++) {
                rewardsBefore[i] = consensusRegistry.getRewards(validators[i]);
            }
            
            // Apply incentives
            vm.prank(systemAddress);
            consensusRegistry.applyIncentives(rewardInfos);
            
            // Calculate distributed amount
            uint256 epochDistributed = 0;
            for (uint256 i = 0; i < 3; i++) {
                uint256 rewardsAfter = consensusRegistry.getRewards(validators[i]);
                epochDistributed += (rewardsAfter - rewardsBefore[i]);
            }
            
            uint256 epochLoss = testEpochIssuance - epochDistributed;
            totalLoss += epochLoss;
            
            console.log("Epoch %s distributed: %s, lost: %s", epoch, epochDistributed, epochLoss);
        }
        
        console.log("\n=== Summary ===");
        console.log("Total epochs: %s", numEpochs);
        console.log("Total expected distribution: %s", testEpochIssuance * numEpochs);
        console.log("Total precision loss: %s wei", totalLoss);
        console.log("Average loss per epoch: %s wei", totalLoss / numEpochs);
    }

    struct TestCase {
        uint256 epochIssuance;
        uint256 numValidators;
        string description;
    }

    function test_PrecisionLoss_DifferentScenarios() public {
        console.log("=== Testing Different Precision Loss Scenarios ===");

        TestCase[] memory testCases = new TestCase[](4);
        testCases[0] = TestCase(1_000_000_000, 3, "1B wei, 3 validators");
        testCases[1] = TestCase(1_000_000_000, 7, "1B wei, 7 validators");
        testCases[2] = TestCase(999_999_999, 3, "999M wei, 3 validators");
        testCases[3] = TestCase(1_000_000_001, 3, "1B+1 wei, 3 validators");
        
        for (uint256 t = 0; t < testCases.length; t++) {
            TestCase memory testCase = testCases[t];
            console.log("\n--- Test Case: %s ---", testCase.description);
            
            // Reset state
            setUp();
            
            // Fund issuance
            vm.deal(address(consensusRegistry.issuance()), testCase.epochIssuance);
            
            // Create validators (use existing ones up to the limit)
            address[] memory validators = new address[](testCase.numValidators);
            validators[0] = validator1;
            validators[1] = validator2;
            validators[2] = validator3;
            if (testCase.numValidators > 3) {
                validators[3] = validator4;
            }
            if (testCase.numValidators > 4) {
                // Create additional validators if needed
                for (uint256 i = 4; i < testCase.numValidators; i++) {
                    validators[i] = makeAddr(string(abi.encodePacked("validator", i)));
                    vm.prank(crOwner);
                    consensusRegistry.mint(validators[i]);
                }
            }
            
            // Create reward infos with equal weight
            RewardInfo[] memory rewardInfos = new RewardInfo[](testCase.numValidators);
            for (uint256 i = 0; i < testCase.numValidators; i++) {
                rewardInfos[i] = RewardInfo(validators[i], 1);
            }
            
            // Get initial rewards
            uint256[] memory rewardsBefore = new uint256[](testCase.numValidators);
            for (uint256 i = 0; i < testCase.numValidators; i++) {
                rewardsBefore[i] = consensusRegistry.getRewards(validators[i]);
            }
            
            // Apply incentives
            vm.prank(systemAddress);
            consensusRegistry.applyIncentives(rewardInfos);
            
            // Calculate total distributed
            uint256 totalDistributed = 0;
            for (uint256 i = 0; i < testCase.numValidators; i++) {
                uint256 rewardsAfter = consensusRegistry.getRewards(validators[i]);
                totalDistributed += (rewardsAfter - rewardsBefore[i]);
            }
            
            uint256 loss = testCase.epochIssuance - totalDistributed;
            console.log("Expected: %s, Distributed: %s, Loss: %s", 
                testCase.epochIssuance, totalDistributed, loss);
            
            // Calculate expected loss
            uint256 expectedRewardPerValidator = testCase.epochIssuance / testCase.numValidators;
            uint256 expectedTotalDistributed = expectedRewardPerValidator * testCase.numValidators;
            uint256 expectedLoss = testCase.epochIssuance - expectedTotalDistributed;
            
            console.log("Expected loss: %s, Actual loss: %s", expectedLoss, loss);
            
            if (loss > 0) {
                console.log("Precision loss confirmed for this scenario");
            } else {
                console.log("No precision loss in this scenario");
            }
        }
    }

    function test_PrecisionLoss_IssuanceBalance() public {
        console.log("=== Testing Issuance Contract Balance Changes ===");
        
        uint256 testEpochIssuance = 1_000_000_000;
        
        // Fund the issuance contract
        vm.deal(address(consensusRegistry.issuance()), testEpochIssuance);
        
        uint256 issuanceBalanceBefore = address(consensusRegistry.issuance()).balance;
        console.log("Issuance balance before applyIncentives: %s", issuanceBalanceBefore);
        
        // Create reward infos
        RewardInfo[] memory rewardInfos = new RewardInfo[](3);
        rewardInfos[0] = RewardInfo(validator1, 1);
        rewardInfos[1] = RewardInfo(validator2, 1);
        rewardInfos[2] = RewardInfo(validator3, 1);
        
        // Apply incentives
        vm.prank(systemAddress);
        consensusRegistry.applyIncentives(rewardInfos);
        
        uint256 issuanceBalanceAfter = address(consensusRegistry.issuance()).balance;
        console.log("Issuance balance after applyIncentives: %s", issuanceBalanceAfter);
        
        uint256 balanceChange = issuanceBalanceBefore - issuanceBalanceAfter;
        console.log("Balance change in issuance contract: %s", balanceChange);
        
        // Note: The balance doesn't change because applyIncentives only updates internal balances
        // The actual transfer happens when rewards are claimed
        console.log("Note: applyIncentives only updates internal balances, not actual transfers");
    }

    function test_PrecisionLoss_LargeNumbers() public {
        console.log("=== Testing Precision Loss with Large Numbers ===");
        
        // Test with larger epoch issuance
        uint256 largeEpochIssuance = 1 ether; // 1 ETH worth of wei
        
        // Fund the issuance contract
        vm.deal(address(consensusRegistry.issuance()), largeEpochIssuance);
        
        // Test with different numbers of validators
        uint256[] memory validatorCounts = new uint256[](3);
        validatorCounts[0] = 3;
        validatorCounts[1] = 7;
        validatorCounts[2] = 13;
        
        for (uint256 v = 0; v < validatorCounts.length; v++) {
            uint256 numValidators = validatorCounts[v];
            console.log("\n--- Testing with %s validators ---", numValidators);
            
            // Reset state
            setUp();
            vm.deal(address(consensusRegistry.issuance()), largeEpochIssuance);
            
            // Create validators
            address[] memory validators = new address[](numValidators);
            validators[0] = validator1;
            validators[1] = validator2;
            validators[2] = validator3;
            if (numValidators > 3) {
                validators[3] = validator4;
            }
            for (uint256 i = 4; i < numValidators; i++) {
                validators[i] = makeAddr(string(abi.encodePacked("validator", i)));
                vm.prank(crOwner);
                consensusRegistry.mint(validators[i]);
            }
            
            // Create reward infos
            RewardInfo[] memory rewardInfos = new RewardInfo[](numValidators);
            for (uint256 i = 0; i < numValidators; i++) {
                rewardInfos[i] = RewardInfo(validators[i], 1);
            }
            
            // Get initial rewards
            uint256[] memory rewardsBefore = new uint256[](numValidators);
            for (uint256 i = 0; i < numValidators; i++) {
                rewardsBefore[i] = consensusRegistry.getRewards(validators[i]);
            }
            
            // Apply incentives
            vm.prank(systemAddress);
            consensusRegistry.applyIncentives(rewardInfos);
            
            // Calculate total distributed
            uint256 totalDistributed = 0;
            for (uint256 i = 0; i < numValidators; i++) {
                uint256 rewardsAfter = consensusRegistry.getRewards(validators[i]);
                totalDistributed += (rewardsAfter - rewardsBefore[i]);
            }
            
            uint256 loss = largeEpochIssuance - totalDistributed;
            console.log("Epoch issuance: %s", largeEpochIssuance);
            console.log("Total distributed: %s", totalDistributed);
            console.log("Precision loss: %s wei", loss);
            console.log("Loss percentage: %s%%", (loss * 10000) / largeEpochIssuance); // in basis points
        }
    }
}
