// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ConsensusRegistryTestUtils } from "./ConsensusRegistryTestUtils.sol";
import { Slash, RewardInfo } from "src/interfaces/IStakeManager.sol";
import { ConsensusRegistry } from "src/consensus/ConsensusRegistry.sol";

contract ConsensusRegistry_Burn_POC is Test, ConsensusRegistryTestUtils {

    function setUp() public {
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);
        sysAddress = consensusRegistry.SYSTEM_ADDRESS();

        vm.deal(crOwner, epochIssuance_ * 2);
        vm.prank(crOwner);
        // Fund the issuance contract for two epochs worth of rewards
        consensusRegistry.allocateIssuance{ value: epochIssuance_ * 2 }();
    }

    function test_POC_LosesRewardsOnConsensusBurn() public {
        address validatorToBurn = validator1;

        // --- Step 1: Grant some rewards to the validator using the intended mechanism ---
        console.log("--- Step 1: Granting rewards to validator via applyIncentives ---");

        RewardInfo[] memory rewardInfos = new RewardInfo[](1);
        rewardInfos[0] = RewardInfo(validatorToBurn, 100); // Give 100 headers worth of rewards

        vm.prank(sysAddress);
        consensusRegistry.applyIncentives(rewardInfos);

        uint256 rewardsAmount = consensusRegistry.getRewards(validatorToBurn);
        assertTrue(rewardsAmount > 0, "Validator should have rewards after incentives");
        console.log("Validator has %s rewards before slashing.", rewardsAmount);

        // --- 2. Slash the validator with an amount that triggers a consensus burn ---
        console.log("\n--- Step 2: Slashing validator to trigger consensus burn ---");
        Slash[] memory slashes = new Slash[](1);
        // Slash amount greater than the total balance (stake + rewards)
        slashes[0] = Slash(validatorToBurn, stakeAmount_ + rewardsAmount + 1);

        uint256 recipientBalanceBefore = validatorToBurn.balance;

        vm.prank(sysAddress);
        consensusRegistry.applySlashes(slashes);

        // --- 3. Verify that the validator received ZERO rewards ---
        // `_consensusBurn` calls `_unstake`. Because it zeroes out the balance first, `_getRewards` inside `_unstake`
        // calculates 0 rewards. The stake is also gone due to the slash.
        uint256 recipientBalanceAfter = validatorToBurn.balance;

        // The validator's balance should not have increased, proving all funds (stake and rewards) were lost.
        assertEq(recipientBalanceAfter, recipientBalanceBefore, "Validator should have received no funds (stake or rewards)");

        // The isRetired flag should be true
        assertTrue(consensusRegistry.isRetired(validatorToBurn), "Validator should be retired");

        console.log("\nSUCCESS: Validator was burned and lost all their accrued rewards permanently.");
    }

    function test_CompareNormalUnstakeVsBurn() public {
        // Test normal unstake behavior for comparison
        // Create a new validator that's not active yet
        address newValidator = makeAddr("newValidator");

        vm.prank(crOwner);
        consensusRegistry.mint(newValidator);

        // Stake but don't activate (stays in Staked status)
        vm.deal(newValidator, stakeAmount_);
        vm.prank(newValidator);
        consensusRegistry.stake{value: stakeAmount_}(_createRandomBlsPubkey(999));

        // Grant rewards to this validator
        RewardInfo[] memory rewardInfos = new RewardInfo[](1);
        rewardInfos[0] = RewardInfo(newValidator, 100);
        vm.prank(sysAddress);
        consensusRegistry.applyIncentives(rewardInfos);

        uint256 rewardsAmount = consensusRegistry.getRewards(newValidator);
        uint256 balanceBefore = newValidator.balance;

        console.log("Normal unstake: Validator has %s rewards", rewardsAmount);

        // Normal unstake (validator is in Staked status)
        vm.prank(newValidator);
        consensusRegistry.unstake(newValidator);

        uint256 balanceAfter = newValidator.balance;
        uint256 received = balanceAfter - balanceBefore;

        console.log("Normal unstake: Validator received %s (stake + rewards)", received);
        assertEq(received, stakeAmount_ + rewardsAmount, "Normal unstake should return stake + rewards");
    }
}