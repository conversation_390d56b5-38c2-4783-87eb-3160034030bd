// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ConsensusRegistryTestUtils } from "./ConsensusRegistryTestUtils.sol";
import { ConsensusRegistry } from "src/consensus/ConsensusRegistry.sol";
import { IConsensusRegistry } from "src/interfaces/IConsensusRegistry.sol";

/**
 * @title Analysis: DoS Attack via Unbounded Loop in _getValidators
 * @notice This test analyzes the claimed DoS vulnerability in concludeEpoch
 */
contract ConsensusRegistry_DoS_Analysis is Test, ConsensusRegistryTestUtils {
    address systemAddress;

    function setUp() public {
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);
        systemAddress = consensusRegistry.SYSTEM_ADDRESS();
    }

    function test_DoS_Attack_SmallScale() public {
        console.log("=== Testing DoS Attack - Small Scale (100 validators) ===");
        
        uint256 numValidators = 100;
        console.log("Minting %s validators...", numValidators);
        
        // Mint validators
        vm.startPrank(crOwner);
        for (uint256 i = 0; i < numValidators; i++) {
            address validator = makeAddr(string(abi.encodePacked("validator", i)));
            consensusRegistry.mint(validator);
        }
        vm.stopPrank();
        
        console.log("Total supply after minting: %s", consensusRegistry.totalSupply());
        
        // Test concludeEpoch - use the initial validators as committee
        address[] memory futureCommittee = _createTokenIdCommittee(4);
        
        vm.prank(systemAddress);
        try consensusRegistry.concludeEpoch(futureCommittee) {
            console.log("SUCCESS: concludeEpoch succeeded with %s validators", numValidators);
        } catch (bytes memory reason) {
            console.log("FAILURE: concludeEpoch failed");
            console.logBytes(reason);
        }
    }

    function test_DoS_Attack_MediumScale() public {
        console.log("=== Testing DoS Attack - Medium Scale (500 validators) ===");
        
        uint256 numValidators = 500;
        console.log("Minting %s validators...", numValidators);
        
        // Mint validators
        vm.startPrank(crOwner);
        for (uint256 i = 0; i < numValidators; i++) {
            address validator = makeAddr(string(abi.encodePacked("validator", i)));
            consensusRegistry.mint(validator);
        }
        vm.stopPrank();
        
        console.log("Total supply after minting: %s", consensusRegistry.totalSupply());
        
        // Test concludeEpoch - use the initial validators as committee
        address[] memory futureCommittee = _createTokenIdCommittee(4);
        
        vm.prank(systemAddress);
        try consensusRegistry.concludeEpoch(futureCommittee) {
            console.log("SUCCESS: concludeEpoch succeeded with %s validators", numValidators);
        } catch (bytes memory reason) {
            console.log("FAILURE: concludeEpoch failed");
            console.logBytes(reason);
        }
    }

    function test_DoS_Attack_LargeScale() public {
        console.log("=== Testing DoS Attack - Large Scale (1000 validators) ===");
        
        uint256 numValidators = 1000;
        console.log("Minting %s validators...", numValidators);
        
        // Mint validators
        vm.startPrank(crOwner);
        for (uint256 i = 0; i < numValidators; i++) {
            address validator = makeAddr(string(abi.encodePacked("validator", i)));
            consensusRegistry.mint(validator);
        }
        vm.stopPrank();

        console.log("Total supply after minting: %s", consensusRegistry.totalSupply());

        // Test concludeEpoch - use the initial validators as committee
        address[] memory futureCommittee = _createTokenIdCommittee(4);

        vm.prank(systemAddress);
        try consensusRegistry.concludeEpoch(futureCommittee) {
            console.log("SUCCESS: concludeEpoch succeeded with %s validators", numValidators);
        } catch (bytes memory reason) {
            console.log("FAILURE: concludeEpoch failed");
            console.logBytes(reason);
        }
    }

    function test_DoS_Attack_ExtremeScale() public {
        console.log("=== Testing DoS Attack - Extreme Scale (3000 validators) ===");

        uint256 numValidators = 3000;
        console.log("Minting %s validators...", numValidators);

        // Mint validators
        vm.startPrank(crOwner);
        for (uint256 i = 0; i < numValidators; i++) {
            address validator = makeAddr(string(abi.encodePacked("validator", i)));
            consensusRegistry.mint(validator);
        }
        vm.stopPrank();
        
        console.log("Total supply after minting: %s", consensusRegistry.totalSupply());
        
        // Test concludeEpoch - use the initial validators as committee
        address[] memory futureCommittee = _createTokenIdCommittee(4);
        
        vm.prank(systemAddress);
        try consensusRegistry.concludeEpoch(futureCommittee) {
            console.log("SUCCESS: concludeEpoch succeeded with %s validators", numValidators);
        } catch (bytes memory reason) {
            console.log("FAILURE: concludeEpoch failed");
            console.logBytes(reason);
        }
    }

    function test_GasConsumption_Analysis() public {
        console.log("=== Gas Consumption Analysis ===");
        
        uint256[] memory validatorCounts = new uint256[](4);
        validatorCounts[0] = 100;
        validatorCounts[1] = 500;
        validatorCounts[2] = 1000;
        validatorCounts[3] = 2000;
        
        for (uint256 j = 0; j < validatorCounts.length; j++) {
            uint256 numValidators = validatorCounts[j];
            console.log("\n--- Testing with %s validators ---", numValidators);
            
            // Reset state for each test
            setUp();
            
            // Mint validators
            vm.startPrank(crOwner);
            for (uint256 i = 0; i < numValidators; i++) {
                address validator = makeAddr(string(abi.encodePacked("validator", j, "_", i)));
                consensusRegistry.mint(validator);
            }
            vm.stopPrank();
            
            // Measure gas for concludeEpoch
            address[] memory futureCommittee = _createTokenIdCommittee(4);
            
            uint256 gasBefore = gasleft();
            vm.prank(systemAddress);
            try consensusRegistry.concludeEpoch(futureCommittee) {
                uint256 gasUsed = gasBefore - gasleft();
                console.log("Gas used for concludeEpoch with %s validators: %s", numValidators, gasUsed);
            } catch {
                console.log("concludeEpoch failed with %s validators", numValidators);
            }
        }
    }

    function test_getValidators_DirectCall() public {
        console.log("=== Testing _getValidators Direct Call ===");
        
        uint256 numValidators = 1000;
        console.log("Minting %s validators...", numValidators);
        
        // Mint validators
        vm.startPrank(crOwner);
        for (uint256 i = 0; i < numValidators; i++) {
            address validator = makeAddr(string(abi.encodePacked("validator", i)));
            consensusRegistry.mint(validator);
        }
        vm.stopPrank();
        
        // Test getValidators (public wrapper for _getValidators)
        uint256 gasBefore = gasleft();
        try consensusRegistry.getValidators(IConsensusRegistry.ValidatorStatus.Active) {
            uint256 gasUsed = gasBefore - gasleft();
            console.log("Gas used for getValidators with %s validators: %s", numValidators, gasUsed);
        } catch (bytes memory reason) {
            console.log("FAILURE: getValidators failed");
            console.logBytes(reason);
        }
    }

    function test_MaxValidatorLimit() public {
        console.log("=== Testing Maximum Validator Limit ===");
        
        // Test the theoretical maximum (type(uint24).max - 1)
        uint256 maxValidators = type(uint24).max - 1;
        console.log("Theoretical maximum validators: %s", maxValidators);
        
        // Test with a smaller but still large number
        uint256 testValidators = 5000;
        console.log("Testing with %s validators...", testValidators);
        
        vm.startPrank(crOwner);
        for (uint256 i = 0; i < testValidators; i++) {
            address validator = makeAddr(string(abi.encodePacked("validator", i)));
            consensusRegistry.mint(validator);
        }
        vm.stopPrank();
        
        console.log("Successfully minted %s validators", testValidators);
        console.log("Total supply: %s", consensusRegistry.totalSupply());
        
        // Test if concludeEpoch still works
        address[] memory futureCommittee = _createTokenIdCommittee(4);
        
        vm.prank(systemAddress);
        try consensusRegistry.concludeEpoch(futureCommittee) {
            console.log("SUCCESS: concludeEpoch still works with %s validators", testValidators);
        } catch {
            console.log("FAILURE: concludeEpoch failed with %s validators", testValidators);
        }
    }
}
