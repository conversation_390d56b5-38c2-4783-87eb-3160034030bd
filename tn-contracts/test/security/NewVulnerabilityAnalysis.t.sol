// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ConsensusRegistryTestUtils } from "../consensus/ConsensusRegistryTestUtils.sol";
import { ConsensusRegistry } from "src/consensus/ConsensusRegistry.sol";
import { InterchainTEL } from "src/InterchainTEL.sol";
import { WTEL } from "src/WTEL.sol";
import { RewardInfo, Slash } from "src/interfaces/IStakeManager.sol";
import { IConsensusRegistry } from "src/interfaces/IConsensusRegistry.sol";

/**
 * @title New Vulnerability Analysis
 * @notice Testing for undiscovered vulnerabilities in Telcoin Network
 */
contract NewVulnerabilityAnalysis is Test, ConsensusRegistryTestUtils {
    address systemAddress;
    address attacker;
    address victim;

    function setUp() public {
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);
        systemAddress = consensusRegistry.SYSTEM_ADDRESS();
        
        attacker = makeAddr("attacker");
        victim = makeAddr("victim");
        
        // Fund the issuance contract
        vm.deal(crOwner, epochIssuance_);
        vm.prank(crOwner);
        consensusRegistry.allocateIssuance{value: epochIssuance_}();
    }

    function test_Vulnerability_SlashingBypass() public {
        console.log("=== Testing Potential Slashing Bypass ===");
        
        // Create a validator
        vm.prank(crOwner);
        consensusRegistry.mint(victim);
        
        // Victim stakes (BLS pubkey must be 96 bytes)
        vm.deal(victim, stakeAmount_);
        vm.prank(victim);
        bytes memory blsPubkey = new bytes(96);
        for (uint i = 0; i < 96; i++) {
            blsPubkey[i] = bytes1(uint8(i + 1));
        }
        consensusRegistry.stake{value: stakeAmount_}(blsPubkey);
        
        // Give victim some rewards
        RewardInfo[] memory rewardInfos = new RewardInfo[](1);
        rewardInfos[0] = RewardInfo(victim, 100);
        
        vm.prank(systemAddress);
        consensusRegistry.applyIncentives(rewardInfos);
        
        uint256 balanceBefore = consensusRegistry.getBalance(victim);
        console.log("Victim balance before slash: %s", balanceBefore);
        
        // Try to slash more than balance to trigger burn
        Slash[] memory slashes = new Slash[](1);
        slashes[0] = Slash(victim, balanceBefore + 1);
        
        vm.prank(systemAddress);
        consensusRegistry.applySlashes(slashes);
        
        // Check if validator was burned
        bool isRetired = consensusRegistry.isRetired(victim);
        console.log("Victim retired after slash: %s", isRetired);
        
        if (isRetired) {
            console.log("EXPECTED: Validator was burned due to insufficient balance");
        } else {
            console.log("POTENTIAL ISSUE: Validator not burned despite insufficient balance");
        }
    }

    function test_Vulnerability_StakeVersionManipulation() public {
        console.log("=== Testing Stake Version Manipulation ===");
        
        // Create new stake version with different parameters
        StakeConfig memory newConfig = StakeConfig(
            stakeAmount_ * 2, // Double stake amount
            minWithdrawAmount_,
            epochIssuance_,
            epochDuration_
        );
        
        vm.prank(crOwner);
        uint8 newVersion = consensusRegistry.upgradeStakeVersion(newConfig);
        
        console.log("New stake version: %s", newVersion);
        console.log("New stake amount: %s", newConfig.stakeAmount);
        
        // Create validators with different versions
        vm.prank(crOwner);
        consensusRegistry.mint(victim);
        
        vm.prank(crOwner);
        consensusRegistry.mint(attacker);
        
        // Victim stakes with old version (should fail)
        vm.deal(victim, stakeAmount_);
        vm.prank(victim);
        vm.expectRevert();
        consensusRegistry.stake{value: stakeAmount_}(abi.encodePacked(uint256(1)));
        
        console.log("CONFIRMED: Old stake amount rejected with new version");
        
        // Attacker stakes with new version
        vm.deal(attacker, newConfig.stakeAmount);
        vm.prank(attacker);
        bytes memory blsPubkey2 = new bytes(96);
        for (uint i = 0; i < 96; i++) {
            blsPubkey2[i] = bytes1(uint8(i + 2));
        }
        consensusRegistry.stake{value: newConfig.stakeAmount}(blsPubkey2);
        
        console.log("Attacker staked with new version successfully");
        
        // Test reward distribution with mixed versions
        RewardInfo[] memory rewardInfos = new RewardInfo[](1);
        rewardInfos[0] = RewardInfo(attacker, 100);
        
        vm.prank(systemAddress);
        consensusRegistry.applyIncentives(rewardInfos);
        
        uint256 attackerReward = consensusRegistry.getBalance(attacker) - newConfig.stakeAmount;
        console.log("Attacker reward with new version: %s", attackerReward);
    }

    function test_Vulnerability_EpochManipulation() public {
        console.log("=== Testing Epoch Manipulation ===");
        
        // Get current epoch info
        IConsensusRegistry.EpochInfo memory currentEpoch = consensusRegistry.getCurrentEpochInfo();
        console.log("Current issuance: %s", currentEpoch.epochIssuance);
        console.log("Current block height: %s", currentEpoch.blockHeight);
        
        // Try to conclude epoch with empty committee
        address[] memory emptyCommittee = new address[](0);
        
        vm.prank(systemAddress);
        vm.expectRevert();
        consensusRegistry.concludeEpoch(emptyCommittee);
        
        console.log("EXPECTED: Empty committee rejected");
        
        // Try with unsorted committee
        address[] memory unsortedCommittee = new address[](2);
        unsortedCommittee[0] = address(0x2);
        unsortedCommittee[1] = address(0x1);
        
        vm.prank(systemAddress);
        vm.expectRevert();
        consensusRegistry.concludeEpoch(unsortedCommittee);
        
        console.log("EXPECTED: Unsorted committee rejected");
    }

    function test_Vulnerability_ReentrancyInUnstake() public {
        console.log("=== Testing Reentrancy in Unstake ===");
        
        // Deploy malicious contract
        MaliciousValidator malicious = new MaliciousValidator(address(consensusRegistry));
        
        // Mint NFT for malicious contract
        vm.prank(crOwner);
        consensusRegistry.mint(address(malicious));
        
        // Fund malicious contract
        vm.deal(address(malicious), stakeAmount_);
        
        // Malicious contract stakes
        malicious.stake{value: stakeAmount_}();
        
        // Try reentrancy attack during unstake
        try malicious.attemptReentrancy() {
            console.log("CRITICAL: Reentrancy attack succeeded!");
        } catch {
            console.log("EXPECTED: Reentrancy attack failed (protected)");
        }
    }

    function test_Vulnerability_TokenIdCollision() public {
        console.log("=== Testing Token ID Collision ===");
        
        // Test edge case addresses
        address edgeCase1 = address(0x0);
        address edgeCase2 = address(type(uint160).max);
        
        // Try to mint to zero address
        vm.prank(crOwner);
        vm.expectRevert();
        consensusRegistry.mint(edgeCase1);
        
        console.log("EXPECTED: Zero address mint rejected");
        
        // Try to mint to max address
        vm.prank(crOwner);
        try consensusRegistry.mint(edgeCase2) {
            console.log("Max address mint succeeded");

            uint256 tokenId = uint160(edgeCase2);
            console.log("Token ID for max address: %s", tokenId);
        } catch {
            console.log("Max address mint failed");
        }
    }

    function test_Vulnerability_DelegationSignatureReplay() public {
        console.log("=== Testing Delegation Signature Replay ===");
        
        // Create validator and delegator
        vm.prank(crOwner);
        consensusRegistry.mint(victim);
        
        // Get delegation digest
        bytes memory blsPubkey = new bytes(96);
        for (uint i = 0; i < 96; i++) {
            blsPubkey[i] = bytes1(uint8(i + 1));
        }
        consensusRegistry.delegationDigest(blsPubkey, victim, attacker);

        console.log("Delegation digest created");
        
        // Test nonce access (nonce is internal, we can't access it directly)
        console.log("Delegation digest created successfully");
        
        // Simulate successful delegation (would need proper signature)
        // This test checks if nonce increments properly
        
        console.log("Testing nonce increment mechanism");
    }

    function test_Vulnerability_IssuanceManipulation() public {
        console.log("=== Testing Issuance Manipulation ===");
        
        // Check current issuance balance
        uint256 issuanceBalance = address(consensusRegistry.issuance()).balance;
        console.log("Current issuance balance: %s", issuanceBalance);
        
        // Try to allocate more issuance
        vm.deal(crOwner, 1000 ether);
        vm.prank(crOwner);
        consensusRegistry.allocateIssuance{value: 1000 ether}();
        
        uint256 newIssuanceBalance = address(consensusRegistry.issuance()).balance;
        console.log("New issuance balance: %s", newIssuanceBalance);
        
        if (newIssuanceBalance > issuanceBalance) {
            console.log("Issuance increased successfully");
        }
        
        // Test if issuance can be drained
        console.log("Testing issuance protection mechanisms");
    }
}

contract MaliciousValidator {
    ConsensusRegistry public consensusRegistry;
    bool public reentrancyAttempted = false;
    
    constructor(address _consensusRegistry) {
        consensusRegistry = ConsensusRegistry(payable(_consensusRegistry));
    }
    
    function stake() external payable {
        bytes memory blsPubkey = new bytes(96);
        for (uint i = 0; i < 96; i++) {
            blsPubkey[i] = bytes1(uint8(i + 1));
        }
        consensusRegistry.stake{value: msg.value}(blsPubkey);
    }
    
    function attemptReentrancy() external {
        consensusRegistry.unstake(address(this));
    }
    
    // This would be called during unstake if reentrancy is possible
    receive() external payable {
        if (!reentrancyAttempted) {
            reentrancyAttempted = true;
            // Try to call unstake again
            consensusRegistry.unstake(address(this));
        }
    }
}
