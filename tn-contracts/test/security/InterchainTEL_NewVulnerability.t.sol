// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity ^0.8.20;

import { Test, console } from "forge-std/Test.sol";
import { InterchainTEL } from "src/InterchainTEL.sol";
import { WTEL } from "src/WTEL.sol";
import { IInterchainTEL } from "src/interfaces/IInterchainTEL.sol";
import { IRecoverableWrapper } from "src/interfaces/IRecoverableWrapper.sol";
/**
 * @title InterchainTEL New Vulnerability Analysis
 * @notice Testing for undiscovered vulnerabilities in InterchainTEL
 */
contract InterchainTEL_NewVulnerability is Test {
    InterchainTEL public iTEL;
    WTEL public wTEL;
    address public attacker;
    address public victim;
    address public owner;

    function setUp() public {
        owner = makeAddr("owner");
        attacker = makeAddr("attacker");
        victim = makeAddr("victim");
        
        // Deploy WTEL
        wTEL = new WTEL();
        
        // Deploy InterchainTEL
        iTEL = new InterchainTEL(
            address(0x2), // origin TEL
            address(0x3), // origin linker
            bytes32(uint256(1)), // origin salt
            "ethereum", // origin chain
            address(0x1), // mock ITS
            "Interchain TEL", // name
            "iTEL", // symbol
            7 days, // recoverable window
            owner,
            address(wTEL),
            100 // max to clean
        );
        
        // Fund accounts
        vm.deal(attacker, 1000 ether);
        vm.deal(victim, 1000 ether);
    }

    function test_Vulnerability_PausedStateBypass() public {
        console.log("=== Testing Paused State Bypass ===");
        
        // Owner pauses the contract
        vm.prank(owner);
        iTEL.pause();
        
        console.log("Contract paused");
        
        // Try to doubleWrap while paused (should fail)
        vm.prank(attacker);
        vm.expectRevert();
        iTEL.doubleWrap{value: 1 ether}();
        
        console.log("EXPECTED: doubleWrap failed while paused");
        
        // Try to call mint while paused (should work for token manager)
        // This tests if system functions bypass pause
        console.log("Testing if system functions bypass pause");
    }

    function test_Vulnerability_WTELManipulation_Corrected() public {
        console.log("=== Testing WTEL Balance Manipulation (Corrected) ===");

        // Attacker wraps some TEL
        vm.prank(attacker);
        iTEL.doubleWrap{value: 10 ether}();

        // Use correct balance functions
        uint256 iTELTotalBalance = iTEL.balanceOf(attacker, true); // Include unsettled
        uint256 iTELSettledBalance = iTEL.balanceOf(attacker, false); // Only settled
        uint256 wTELBalance = wTEL.balanceOf(address(iTEL));
        uint256 iTELTotalSupply = iTEL.totalSupply();

        console.log("Attacker iTEL total balance: %s", iTELTotalBalance);
        console.log("Attacker iTEL settled balance: %s", iTELSettledBalance);
        console.log("Contract wTEL balance: %s", wTELBalance);
        console.log("iTEL total supply: %s", iTELTotalSupply);

        // Check if total supply matches WTEL backing
        if (iTELTotalSupply == wTELBalance) {
            console.log("EXPECTED: Total supply matches WTEL backing");
        } else {
            console.log("ISSUE: Total supply doesn't match WTEL backing");
            console.log("Difference: %s", iTELTotalSupply > wTELBalance ? iTELTotalSupply - wTELBalance : wTELBalance - iTELTotalSupply);
        }

        // Test realistic scenario: Someone sends WTEL directly to the contract
        vm.deal(attacker, 5 ether);
        vm.prank(attacker);
        wTEL.deposit{value: 5 ether}();

        // Now attacker has 5 WTEL, transfer it to iTEL contract
        vm.prank(attacker);
        wTEL.transfer(address(iTEL), 5 ether);

        uint256 newWTELBalance = wTEL.balanceOf(address(iTEL));
        uint256 newTotalSupply = iTEL.totalSupply();

        console.log("After direct WTEL transfer:");
        console.log("New wTEL balance: %s", newWTELBalance);
        console.log("iTEL total supply (unchanged): %s", newTotalSupply);

        if (newWTELBalance > newTotalSupply) {
            console.log("CONFIRMED: Contract now has more WTEL than iTEL supply");
            console.log("Excess WTEL: %s", newWTELBalance - newTotalSupply);

            // Test if this affects unwrap operations
            console.log("Testing if this affects unwrap operations...");

            // Fast forward to settle the attacker's balance
            vm.warp(block.timestamp + 7 days + 1);

            uint256 settledBalance = iTEL.balanceOf(attacker, false);
            console.log("Attacker settled balance: %s", settledBalance);

            // Try to unwrap
            vm.prank(attacker);
            try iTEL.unwrap(settledBalance) {
                console.log("SUCCESS: Unwrap worked despite excess WTEL");

                // Check final balances
                uint256 finalWTEL = wTEL.balanceOf(address(iTEL));
                uint256 finalSupply = iTEL.totalSupply();
                console.log("Final WTEL balance: %s", finalWTEL);
                console.log("Final iTEL supply: %s", finalSupply);

                if (finalWTEL > finalSupply) {
                    console.log("ISSUE CONFIRMED: Excess WTEL remains after unwrap");
                    console.log("Remaining excess: %s", finalWTEL - finalSupply);
                }
            } catch (bytes memory reason) {
                console.log("FAILURE: Unwrap failed");
                console.logBytes(reason);
            }
        }
    }

    function test_Vulnerability_RecoverableWindowBypass() public {
        console.log("=== Testing Recoverable Window Bypass ===");
        
        // Attacker wraps TEL
        vm.prank(attacker);
        iTEL.doubleWrap{value: 10 ether}();
        
        uint256 totalBalance = iTEL.balanceOf(attacker, true);
        uint256 settledBalance = iTEL.balanceOf(attacker, false);
        
        uint256 unsettledBalance = totalBalance - settledBalance;
        console.log("Total balance: %s", totalBalance);
        console.log("Settled balance: %s", settledBalance);
        console.log("Unsettled balance: %s", unsettledBalance);
        
        // Try to transfer unsettled funds immediately
        vm.prank(attacker);
        try iTEL.transfer(victim, 1 ether, true) {
            console.log("SUCCESS: Unsettled transfer allowed");
        } catch {
            console.log("EXPECTED: Unsettled transfer blocked");
        }
        
        // Fast forward time to just before settlement
        vm.warp(block.timestamp + 7 days - 1);
        
        uint256 newSettledBalance = iTEL.balanceOf(attacker, false);
        console.log("Settled balance before window: %s", newSettledBalance);

        // Fast forward past settlement window
        vm.warp(block.timestamp + 2);

        uint256 finalSettledBalance = iTEL.balanceOf(attacker, false);
        console.log("Settled balance after window: %s", finalSettledBalance);
        
        if (finalSettledBalance > newSettledBalance) {
            console.log("EXPECTED: Funds settled after window");
        }
    }

    function test_Vulnerability_TokenManagerImpersonation() public {
        console.log("=== Testing Token Manager Impersonation ===");
        
        address fakeTokenManager = makeAddr("fakeTokenManager");
        
        // Try to call mint from fake token manager
        vm.prank(fakeTokenManager);
        vm.expectRevert();
        iTEL.mint(attacker, 10 ether);
        
        console.log("EXPECTED: Fake token manager rejected");
        
        // Try to call burn from fake token manager
        vm.prank(fakeTokenManager);
        vm.expectRevert();
        iTEL.burn(attacker, 1 ether);
        
        console.log("EXPECTED: Fake burn call rejected");
        
        // Check if token manager address can be manipulated
        address calculatedTM = iTEL.tokenManagerAddress();
        console.log("Calculated token manager: %s", calculatedTM);
        
        // Test if the calculation is deterministic
        address calculatedTM2 = iTEL.tokenManagerAddress();
        if (calculatedTM == calculatedTM2) {
            console.log("EXPECTED: Token manager address is deterministic");
        } else {
            console.log("CRITICAL: Token manager address is not deterministic!");
        }
    }

    function test_Vulnerability_DoubleWrapReentrancy() public {
        console.log("=== Testing Double Wrap Reentrancy ===");
        
        // Deploy malicious contract
        MaliciousDoubleWrapper malicious = new MaliciousDoubleWrapper(address(iTEL), address(wTEL));
        vm.deal(address(malicious), 100 ether);
        
        // Try reentrancy attack
        try malicious.attack{value: 10 ether}() {
            console.log("CRITICAL: Reentrancy attack succeeded!");
            
            uint256 maliciousBalance = iTEL.balanceOf(address(malicious));
            console.log("Malicious contract iTEL balance: %s", maliciousBalance);
            
            if (maliciousBalance > 10 ether) {
                console.log("CONFIRMED: Extra iTEL minted through reentrancy");
            }
        } catch {
            console.log("EXPECTED: Reentrancy attack failed");
        }
    }

    function test_Vulnerability_FrozenFundsManipulation() public {
        console.log("=== Testing Frozen Funds Manipulation ===");
        
        // Attacker wraps funds
        vm.prank(attacker);
        iTEL.doubleWrap{value: 10 ether}();
        
        // Check frozen amount
        uint256 frozenAmount = iTEL.frozen(attacker);
        console.log("Frozen amount: %s", frozenAmount);
        
        // Try to manipulate frozen state through transfers
        vm.prank(attacker);
        iTEL.transfer(victim, 1 ether, true);
        
        uint256 newFrozenAmount = iTEL.frozen(attacker);
        console.log("Frozen amount after transfer: %s", newFrozenAmount);
        
        // Check if frozen accounting is correct
        if (newFrozenAmount != frozenAmount) {
            console.log("Frozen amount changed after transfer");
        }
    }

    function test_Vulnerability_UnwrapDisabledBypass() public {
        console.log("=== Testing Unwrap Disabled Bypass ===");
        
        // Attacker wraps funds
        vm.prank(attacker);
        iTEL.doubleWrap{value: 10 ether}();
        
        // Fast forward to settlement
        vm.warp(block.timestamp + 7 days + 1);
        
        // Check if unwrap is disabled (it's a public mapping)
        bool isUnwrapDisabled = iTEL.unwrapDisabled(attacker);
        console.log("Unwrap disabled for attacker: %s", isUnwrapDisabled);
        
        // Try to unwrap (should fail)
        vm.prank(attacker);
        vm.expectRevert();
        iTEL.unwrap(1 ether);
        
        console.log("EXPECTED: Direct unwrap failed");
        
        // Try to unwrap through transfer to another account
        vm.prank(attacker);
        iTEL.transfer(victim, 5 ether, false);
        
        // Victim tries to unwrap
        vm.prank(victim);
        try iTEL.unwrap(1 ether) {
            console.log("POTENTIAL BYPASS: Victim can unwrap transferred funds");
        } catch {
            console.log("EXPECTED: Victim cannot unwrap either");
        }
    }
}

contract MaliciousDoubleWrapper {
    InterchainTEL public iTEL;
    WTEL public wTEL;
    bool public attacked = false;
    
    constructor(address _iTEL, address _wTEL) {
        iTEL = InterchainTEL(payable(_iTEL));
        wTEL = WTEL(payable(_wTEL));
    }
    
    function attack() external payable {
        iTEL.doubleWrap{value: msg.value}();
    }
    
    // This could be triggered during WTEL deposit
    receive() external payable {
        if (!attacked && msg.sender == address(wTEL)) {
            attacked = true;
            // Try to call doubleWrap again
            if (address(this).balance >= 1 ether) {
                iTEL.doubleWrap{value: 1 ether}();
            }
        }
    }
}
