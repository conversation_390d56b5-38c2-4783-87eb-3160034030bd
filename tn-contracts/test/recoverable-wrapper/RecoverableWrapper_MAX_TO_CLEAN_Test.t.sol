// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ITSTestHelper } from "../ITS/ITSTestHelper.sol";

/**
 * @title Test: MAX_TO_CLEAN Behavior Analysis
 * @notice This test analyzes how MAX_TO_CLEAN affects the _clean function behavior
 */
contract RecoverableWrapper_MAX_TO_CLEAN_Test is ITSTestHelper {
    address user = makeAddr("user");
    address admin = makeAddr("admin");

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
    }

    function test_MAX_TO_CLEAN_Behavior() public {
        uint256 recordCount = 400; // More than MAX_TO_CLEAN (300)
        uint256 dustAmount = 1;

        console.log("MAX_TO_CLEAN is set to 300 in devnet config");
        console.log("Creating %s records (more than MAX_TO_CLEAN)...", recordCount);

        // Create many records
        vm.deal(user, recordCount * dustAmount);
        vm.startPrank(user);
        for (uint256 i = 0; i < recordCount; i++) {
            iTEL.doubleWrap{value: dustAmount}();
        }
        vm.stopPrank();

        // Let all records expire
        vm.warp(block.timestamp + iTEL.recoverableWindow() + 1);

        console.log("All records have expired. Now triggering _clean...");

        // Trigger _clean - this should only process MAX_TO_CLEAN (300) records
        vm.deal(user, dustAmount);
        vm.prank(user);
        iTEL.doubleWrap{value: dustAmount}();

        console.log("First _clean call completed. Some records should remain.");

        // Check if there are still unsettled records
        uint256 unsettledAfterFirstClean = iTEL.balanceOf(user, true) - iTEL.balanceOf(user, false);
        console.log("Unsettled balance after first clean: %s", unsettledAfterFirstClean);

        // Trigger another _clean
        vm.deal(user, dustAmount);
        vm.prank(user);
        iTEL.doubleWrap{value: dustAmount}();

        uint256 unsettledAfterSecondClean = iTEL.balanceOf(user, true) - iTEL.balanceOf(user, false);
        console.log("Unsettled balance after second clean: %s", unsettledAfterSecondClean);

        console.log("Test completed. Check gas consumption in both calls.");
    }

    function test_Gas_Consumption_With_Different_Record_Counts() public {
        uint256[] memory recordCounts = new uint256[](4);
        recordCounts[0] = 100;
        recordCounts[1] = 200;
        recordCounts[2] = 300; // Exactly MAX_TO_CLEAN
        recordCounts[3] = 500; // More than MAX_TO_CLEAN

        for (uint256 j = 0; j < recordCounts.length; j++) {
            address testUser = makeAddr(string(abi.encodePacked("user", j)));
            uint256 recordCount = recordCounts[j];
            uint256 dustAmount = 1;

            console.log("\n--- Testing with %s records ---", recordCount);

            // Create records
            vm.deal(testUser, recordCount * dustAmount);
            vm.startPrank(testUser);
            for (uint256 i = 0; i < recordCount; i++) {
                iTEL.doubleWrap{value: dustAmount}();
            }
            vm.stopPrank();

            // Let records expire
            vm.warp(block.timestamp + iTEL.recoverableWindow() + 1);

            // Measure gas for _clean operation
            uint256 gasBefore = gasleft();
            vm.deal(testUser, dustAmount);
            vm.prank(testUser);
            iTEL.doubleWrap{value: dustAmount}();
            uint256 gasAfter = gasleft();

            console.log("Gas consumed for %s records: %s", recordCount, gasBefore - gasAfter);
        }
    }
}
