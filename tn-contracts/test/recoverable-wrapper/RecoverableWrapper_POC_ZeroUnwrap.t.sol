
// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ITSTestHelper } from "../ITS/ITSTestHelper.sol";
import { IRecoverableWrapper } from "src/interfaces/IRecoverableWrapper.sol";

/**
 * @title PoC: Zero-Value Unwraps Are Allowed
 * @notice This PoC demonstrates that the unwrap() function can be successfully
 * called with an amount of 0, which emits an event for a no-op action.
 */
contract RecoverableWrapper_ZeroUnwrap_POC is ITSTestHelper {
    address user = makeAddr("user");
    address admin = makeAddr("admin");

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
    }

    function test_POC_CanUnwrapZeroAmount() public {
        // The user has 0 iTEL and 0 wTEL, but can still call unwrap(0).
        uint256 user_iTEL_Balance = iTEL.balanceOf(user);
        assertEq(user_iTEL_Balance, 0);

        // We expect an Unwrap event to be emitted with the amount of 0.
        vm.expectEmit(true, true, true, true);
        emit IRecoverableWrapper.Unwrap(user, user, 0);

        // The call to unwrap(0) should succeed.
        vm.prank(user);
        iTEL.unwrap(0);

        console.log("SUCCESS: unwrap(0) was called successfully and emitted an event.");
    }
}
