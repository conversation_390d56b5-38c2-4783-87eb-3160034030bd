// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ITSTestHelper } from "../ITS/ITSTestHelper.sol";
import { IRecoverableWrapper } from "src/interfaces/IRecoverableWrapper.sol";

contract RecoverableWrapper_StateCorruption_POC is ITSTestHelper {
    address owner = makeAddr("owner");
    address user = makeAddr("user");
    address recipient = makeAddr("recipient");

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(owner, MAINNET_TEL);
        
        // Give user some WTEL to work with
        vm.deal(user, 1000 ether);
        vm.prank(user);
        wTEL.deposit{value: 1000 ether}();
        
        vm.prank(user);
        wTEL.approve(address(iTEL), type(uint256).max);
    }

    function test_POC_StateCorruptionLocksFunds() public {
        console.log("--- PoC: State Corruption in _spendUnsettled leads to locked funds ---");

        // Step 1: User wraps 100 tokens. This creates an unsettled record.
        vm.prank(user);
        iTEL.wrap(100e18);
        assertEq(iTEL.balanceOf(user, true), 100e18);
        assertEq(iTEL.balanceOf(user, false), 0);
        console.log("Step 1: User wrapped 100 tokens. Unsettled balance: 100.");

        // Step 2: Time passes, the first record becomes settled.
        vm.warp(block.timestamp + iTEL.recoverableWindow() + 1);
        assertEq(iTEL.balanceOf(user, true), 100e18);
        assertEq(iTEL.balanceOf(user, false), 100e18, "First wrap should be settled");
        console.log("Step 2: Time elapsed. Settled balance: 100.");

        // Step 3: User wraps another 50 tokens. This creates a new unsettled record.
        vm.prank(user);
        iTEL.wrap(50e18);
        assertEq(iTEL.balanceOf(user, true), 150e18, "Total balance should be 150");
        assertEq(iTEL.balanceOf(user, false), 100e18, "Settled balance should still be 100");
        console.log("Step 3: User wrapped another 50. Total: 150, Settled: 100, Unsettled: 50.");

        // Step 4: User spends EXACTLY the unsettled amount.
        // This triggers the bug: the record is deleted, but `cachedUnsettled` is not updated.
        vm.prank(user);
        iTEL.transfer(recipient, 50e18, true); // `includeUnsettled` is true
        console.log("Step 4: User spent the 50 unsettled tokens.");

        // Let's inspect the internal state
        // Expected correct state: Total=100, Settled=100, Unsettled=0
        assertEq(iTEL.balanceOf(user, true), 100e18, "Total balance should now be 100.");
        
        // Check what the unsettled balance reports
        uint256 unsettledBalance = iTEL.balanceOf(user, true) - iTEL.balanceOf(user, false);
        console.log("Unsettled balance after spending: %s", unsettledBalance);
        
        // --- THE FAILURE ---
        // Now, the user tries to unwrap their 100 fully settled tokens.
        console.log("\n--- The Failure ---");
        console.log("User attempts to unwrap their 100 settled tokens...");
        
        // Check spendable settled balance
        uint256 spendableSettled = iTEL.spendableBalanceOf(user, false);
        console.log("Spendable settled balance: %s", spendableSettled);
        
        // The call might fail if there's state corruption
        vm.prank(user);
        try iTEL.unwrap(100e18) {
            console.log("SUCCESS: unwrap() succeeded. No state corruption detected.");
        } catch (bytes memory reason) {
            console.log("FAILURE: unwrap() reverted. State corruption confirmed.");
            console.logBytes(reason);
        }
    }

    function test_DetailedStateAnalysis() public {
        console.log("--- Detailed State Analysis ---");

        // Create the same scenario
        vm.prank(user);
        iTEL.wrap(100e18);

        vm.warp(block.timestamp + iTEL.recoverableWindow() + 1);

        vm.prank(user);
        iTEL.wrap(50e18);

        console.log("Before spending unsettled:");
        console.log("Total balance: %s", iTEL.balanceOf(user, true));
        console.log("Settled balance: %s", iTEL.balanceOf(user, false));
        console.log("Spendable (with unsettled): %s", iTEL.spendableBalanceOf(user, true));
        console.log("Spendable (settled only): %s", iTEL.spendableBalanceOf(user, false));

        // Spend exactly the unsettled amount
        vm.prank(user);
        iTEL.transfer(recipient, 50e18, true);

        console.log("\nAfter spending unsettled:");
        console.log("Total balance: %s", iTEL.balanceOf(user, true));
        console.log("Settled balance: %s", iTEL.balanceOf(user, false));
        console.log("Spendable (with unsettled): %s", iTEL.spendableBalanceOf(user, true));
        console.log("Spendable (settled only): %s", iTEL.spendableBalanceOf(user, false));

        // The key test: can we unwrap the settled amount?
        uint256 settledBalance = iTEL.balanceOf(user, false);
        console.log("\nAttempting to unwrap settled balance: %s", settledBalance);

        vm.prank(user);
        try iTEL.unwrap(settledBalance) {
            console.log("SUCCESS: Unwrap succeeded");
        } catch (bytes memory reason) {
            console.log("FAILURE: Unwrap failed");
            console.logBytes(reason);
        }
    }

    function test_ComplexCacheScenario() public {
        console.log("--- Complex Cache Scenario ---");

        // Create multiple unsettled records
        vm.prank(user);
        iTEL.wrap(30e18); // Record 1

        vm.prank(user);
        iTEL.wrap(40e18); // Record 2

        vm.prank(user);
        iTEL.wrap(50e18); // Record 3

        console.log("Created 3 unsettled records: 30, 40, 50");
        console.log("Total unsettled: %s", iTEL.balanceOf(user, true) - iTEL.balanceOf(user, false));

        // Spend from the newest records first (LIFO order)
        vm.prank(user);
        iTEL.transfer(recipient, 80e18, true); // Should spend 50 + 30 from records 3 and 2

        console.log("After spending 80 tokens:");
        console.log("Total balance: %s", iTEL.balanceOf(user, true));
        console.log("Settled balance: %s", iTEL.balanceOf(user, false));
        console.log("Remaining unsettled: %s", iTEL.balanceOf(user, true) - iTEL.balanceOf(user, false));

        // Should have 40 tokens left (30 from record 1)
        uint256 expectedRemaining = 40e18;
        uint256 actualRemaining = iTEL.balanceOf(user, true) - iTEL.balanceOf(user, false);
        assertEq(actualRemaining, expectedRemaining, "Remaining unsettled should be 40");
    }
}
