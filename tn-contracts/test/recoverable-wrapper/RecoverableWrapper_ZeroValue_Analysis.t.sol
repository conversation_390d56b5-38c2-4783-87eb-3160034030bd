// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ITSTestHelper } from "../ITS/ITSTestHelper.sol";
import { IRecoverableWrapper } from "src/interfaces/IRecoverableWrapper.sol";

/**
 * @title Analysis: Zero-Value Operations in RecoverableWrapper
 * @notice This test analyzes various functions for zero-value handling
 */
contract RecoverableWrapper_ZeroValue_Analysis is ITSTestHelper {
    address user = makeAddr("user");
    address user2 = makeAddr("user2");
    address admin = makeAddr("admin");

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
    }

    function test_ZeroValue_unwrap() public {
        console.log("=== Testing unwrap(0) ===");
        
        // Should succeed and emit event
        vm.expectEmit(true, true, true, true);
        emit IRecoverableWrapper.Unwrap(user, user, 0);
        
        vm.prank(user);
        iTEL.unwrap(0);
        
        console.log("SUCCESS: unwrap(0) succeeded and emitted event");
    }

    function test_ZeroValue_unwrapTo() public {
        console.log("=== Testing unwrapTo(user2, 0) ===");
        
        // Should succeed and emit event
        vm.expectEmit(true, true, true, true);
        emit IRecoverableWrapper.Unwrap(user, user2, 0);
        
        vm.prank(user);
        iTEL.unwrapTo(user2, 0);
        
        console.log("SUCCESS: unwrapTo(user2, 0) succeeded and emitted event");
    }

    function test_ZeroValue_wrap() public {
        console.log("=== Testing wrap(0) ===");

        // Actually, wrap(0) succeeds even without approval because transferFrom(0) is allowed
        vm.expectEmit(true, true, true, true);
        emit IRecoverableWrapper.Wrap(user, 0);

        vm.prank(user);
        iTEL.wrap(0);

        console.log("SUCCESS: wrap(0) succeeded even without approval!");
    }

    function test_ZeroValue_transfer() public {
        console.log("=== Testing transfer(user2, 0, false) ===");
        
        // Should succeed
        vm.prank(user);
        bool success = iTEL.transfer(user2, 0, false);
        assertTrue(success);
        
        console.log("SUCCESS: transfer(user2, 0, false) succeeded");
    }

    function test_ZeroValue_standardTransfer() public {
        console.log("=== Testing standard transfer(user2, 0) ===");

        // Should succeed
        vm.prank(user);
        bool success = iTEL.transfer(user2, 0);
        assertTrue(success);

        console.log("SUCCESS: standard transfer(user2, 0) succeeded");
    }

    function test_ZeroValue_transferFrom() public {
        console.log("=== Testing transferFrom(user, user2, 0, false) ===");

        // Give approval first
        vm.prank(user);
        iTEL.approve(user2, 0);

        // Should succeed
        vm.prank(user2);
        bool success = iTEL.transferFrom(user, user2, 0, false);
        assertTrue(success);

        console.log("SUCCESS: transferFrom(user, user2, 0, false) succeeded");
    }

    function test_ZeroValue_standardTransferFrom() public {
        console.log("=== Testing standard transferFrom(user, user2, 0) ===");

        // Give approval first
        vm.prank(user);
        iTEL.approve(user2, 0);

        // Should succeed
        vm.prank(user2);
        bool success = iTEL.transferFrom(user, user2, 0);
        assertTrue(success);

        console.log("SUCCESS: standard transferFrom(user, user2, 0) succeeded");
    }

    function test_Summary() public {
        console.log("\n=== SUMMARY ===");
        console.log("Functions that allow zero values:");
        console.log("- unwrap(0) YES");
        console.log("- unwrapTo(address, 0) YES");
        console.log("- wrap(0) YES (with proper approval)");
        console.log("- transfer(address, 0, bool) YES");
        console.log("- transfer(address, 0) YES");
        console.log("- transferFrom(address, address, 0, bool) YES");
        console.log("- transferFrom(address, address, 0) YES");
        console.log("\nAll functions lack zero-value validation!");
    }
}
