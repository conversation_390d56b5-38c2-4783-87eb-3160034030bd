// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { InterchainTEL } from "../../src/InterchainTEL.sol";
import { ITSTestHelper } from "./ITSTestHelper.sol";
import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title PoC: Bridged Assets are Permanently Trapped
 * @notice This PoC demonstrates that during an outbound bridge (`burn` call), the underlying
 * native TEL is withdrawn to the InterchainTEL contract itself and gets stuck there,
 * leading to a permanent loss of user funds.
 */
contract InterchainTEL_POC is ITSTestHelper {
    address user = address(0xDEADBEEF);
    address admin = address(0xABCD); // <-- تم إضافة هذا السطر

    function setUp() public {
        // Use the full ITS test helper setup to deploy all necessary contracts.
        // This will deploy InterchainTEL, WTEL, ITS, TokenManager, etc.
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
    }

    function test_POC_BridgedAssetsAreTrapped() public {
        // --- 1. Setup: Fund user and wrap native TEL to iTEL ---
        uint256 bridgeAmount = 100 * 1e18;
        vm.deal(user, bridgeAmount);

        vm.startPrank(user);
        iTEL.doubleWrap{ value: bridgeAmount }();
        vm.stopPrank();

        // --- 2. Settle the funds by elapsing the recovery window ---
        vm.warp(block.timestamp + iTEL.recoverableWindow() + 1);

        // --- 3. Check initial balances before burning ---
        uint256 user_iTEL_Balance_Before = iTEL.balanceOf(user);
        uint256 iTEL_wTEL_Balance_Before = wTEL.balanceOf(address(iTEL));
        uint256 iTEL_NativeTEL_Balance_Before = address(iTEL).balance;

        assertEq(user_iTEL_Balance_Before, bridgeAmount, "User should have settled iTEL");
        assertEq(iTEL_wTEL_Balance_Before, bridgeAmount, "iTEL contract should hold backing wTEL");
        // The iTEL contract is pre-seeded with total supply, so we record this initial balance.
        uint256 expectedInitialNativeBalance = telTotalSupply;
        assertEq(iTEL_NativeTEL_Balance_Before, expectedInitialNativeBalance, "iTEL contract starts with genesis supply");


        // --- 4. Simulate the ITS calling the burn function ---
        // The Token Manager is the only one authorized to call burn.
        vm.startPrank(iTEL.tokenManagerAddress());
        iTEL.burn(user, bridgeAmount);
        vm.stopPrank();

        // --- 5. Verify the final state ---
        uint256 user_iTEL_Balance_After = iTEL.balanceOf(user);
        uint256 iTEL_wTEL_Balance_After = wTEL.balanceOf(address(iTEL));
        uint256 iTEL_NativeTEL_Balance_After = address(iTEL).balance;

        console.log("--- Balance Analysis After Burn ---");
        console.log("User iTEL balance after burn: %s", user_iTEL_Balance_After);
        console.log("iTEL contract wTEL balance after burn: %s", iTEL_wTEL_Balance_After);
        console.log("iTEL contract native TEL balance after burn: %s", iTEL_NativeTEL_Balance_After);
        
        // Assertions that prove the vulnerability:
        assertEq(user_iTEL_Balance_After, 0, "User's iTEL should be burned");
        assertEq(iTEL_wTEL_Balance_After, 0, "Backing wTEL should be burned/withdrawn");
        
        // CRITICAL ASSERTION: The native TEL is now trapped in the InterchainTEL contract.
        uint256 expectedTrappedBalance = expectedInitialNativeBalance + bridgeAmount;
        assertEq(iTEL_NativeTEL_Balance_After, expectedTrappedBalance, "Native TEL is now TRAPPED in the iTEL contract");
    }
}