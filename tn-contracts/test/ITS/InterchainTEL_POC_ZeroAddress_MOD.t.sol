// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ITSTestHelper } from "./ITSTestHelper.sol";
import { IInterchainTEL } from "src/interfaces/IInterchainTEL.sol";
import { InterchainTEL } from "src/InterchainTEL.sol";

/**
 * @title PoC: Testing Zero Address Vulnerability Claims
 * @notice This test checks if the vulnerability described in the report actually exists
 */
contract InterchainTEL_ZeroAddress_POC is ITSTestHelper {
    address user = makeAddr("user");
    address admin = makeAddr("admin");

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
    }

    function test_POC_CheckDoubleWrapZeroAddressProtection() public {
        // Test the actual doubleWrap function that exists in the code
        uint256 wrapAmount = 100 * 1e18;
        vm.deal(user, wrapAmount);

        uint256 user_iTEL_Balance_Before = iTEL.balanceOf(user);
        uint256 zeroAddr_iTEL_Balance_Before = iTEL.balanceOf(address(0));
        uint256 iTEL_wTEL_Balance_Before = wTEL.balanceOf(address(iTEL));

        assertEq(user_iTEL_Balance_Before, 0);
        assertEq(zeroAddr_iTEL_Balance_Before, 0);
        assertEq(iTEL_wTEL_Balance_Before, 0);

        // The actual doubleWrap function only takes msg.value, no onBehalfOf parameter
        vm.prank(user);
        iTEL.doubleWrap{ value: wrapAmount }();

        uint256 user_iTEL_Balance_After = iTEL.balanceOf(user);
        uint256 zeroAddr_iTEL_Balance_After = iTEL.balanceOf(address(0));
        uint256 iTEL_wTEL_Balance_After = wTEL.balanceOf(address(iTEL));
        uint256 user_native_balance_after = user.balance;

        console.log("--- Balance Analysis After Normal doubleWrap ---");
        console.log("User's final native TEL balance: %s", user_native_balance_after);
        console.log("User's final iTEL balance: %s", user_iTEL_Balance_After);
        console.log("Zero address's final iTEL balance: %s", zeroAddr_iTEL_Balance_After);
        console.log("InterchainTEL's final wTEL balance: %s", iTEL_wTEL_Balance_After);

        // Normal behavior: user gets iTEL, zero address gets nothing
        assertEq(user_native_balance_after, 0, "User's native TEL should be taken");
        assertEq(zeroAddr_iTEL_Balance_After, 0, "Zero address should not receive any iTEL");
        assertEq(iTEL_wTEL_Balance_After, wrapAmount, "wTEL should be held by InterchainTEL contract");

        // Wait for settlement period
        vm.warp(block.timestamp + 7 days + 1);

        uint256 user_settled_balance = iTEL.balanceOf(user);
        assertEq(user_settled_balance, wrapAmount, "User should receive settled iTEL after recovery period");
    }

    function test_POC_CheckPermitWrapZeroAddressProtection() public {
        // Test the actual permitWrap function signature
        uint256 wrapAmount = 100 * 1e18;
        vm.deal(user, wrapAmount);

        // Fund user with wTEL first
        vm.prank(user);
        wTEL.deposit{ value: wrapAmount }();

        // The actual permitWrap function takes individual parameters, not a struct
        // permitWrap(address owner, uint256 amount, uint256 deadline, uint8 v, bytes32 r, bytes32 s)

        // Try to call permitWrap with address(0) as owner
        uint256 deadline = block.timestamp + 1 hours;

        // This should fail with InvalidPermit because signature is invalid
        // But let's see what actually happens
        vm.prank(user);
        vm.expectRevert(); // Just expect any revert
        iTEL.permitWrap(address(0), wrapAmount, deadline, 0, bytes32(0), bytes32(0));
    }
}