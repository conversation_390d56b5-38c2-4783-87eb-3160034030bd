{"abi": [{"type": "constructor", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}, {"name": "_data", "type": "bytes", "internalType": "bytes"}], "stateMutability": "payable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}], "bytecode": {"object": "0x60806040526040516103cd3803806103cd8339810160408190526100229161025e565b61002c8282610033565b5050610347565b61003c82610091565b6040516001600160a01b038316907fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b905f90a280511561008557610080828261010c565b505050565b61008d61017f565b5050565b806001600160a01b03163b5f036100cb57604051634c9c8ce360e01b81526001600160a01b03821660048201526024015b60405180910390fd5b7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc80546001600160a01b0319166001600160a01b0392909216919091179055565b60605f80846001600160a01b031684604051610128919061032c565b5f60405180830381855af49150503d805f8114610160576040519150601f19603f3d011682016040523d82523d5f602084013e610165565b606091505b5090925090506101768583836101a0565b95945050505050565b341561019e5760405163b398979f60e01b815260040160405180910390fd5b565b6060826101b5576101b0826101ff565b6101f8565b81511580156101cc57506001600160a01b0384163b155b156101f557604051639996b31560e01b81526001600160a01b03851660048201526024016100c2565b50805b9392505050565b80511561020f5780518082602001fd5b604051630a12f52160e11b815260040160405180910390fd5b634e487b7160e01b5f52604160045260245ffd5b5f5b8381101561025657818101518382015260200161023e565b50505f910152565b5f806040838503121561026f575f80fd5b82516001600160a01b0381168114610285575f80fd5b60208401519092506001600160401b038111156102a0575f80fd5b8301601f810185136102b0575f80fd5b80516001600160401b038111156102c9576102c9610228565b604051601f8201601f19908116603f011681016001600160401b03811182821017156102f7576102f7610228565b60405281815282820160200187101561030e575f80fd5b61031f82602083016020860161023c565b8093505050509250929050565b5f825161033d81846020870161023c565b9190910192915050565b607a806103535f395ff3fe6080604052600a600c565b005b60186014601a565b6050565b565b5f604b7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc546001600160a01b031690565b905090565b365f80375f80365f845af43d5f803e8080156069573d5ff35b3d5ffdfea164736f6c634300081a000a", "sourceMap": "599:1116:49:-:0;;;1080:133;;;;;;;;;;;;;;;;;;:::i;:::-;1154:52;1184:14;1200:5;1154:29;:52::i;:::-;1080:133;;599:1116;;2779:335:50;2870:37;2889:17;2870:18;:37::i;:::-;2922:27;;-1:-1:-1;;;;;2922:27:50;;;;;;;;2964:11;;:15;2960:148;;2995:53;3024:17;3043:4;2995:28;:53::i;:::-;;2779:335;;:::o;2960:148::-;3079:18;:16;:18::i;:::-;2779:335;;:::o;2186:281::-;2263:17;-1:-1:-1;;;;;2263:29:50;;2296:1;2263:34;2259:119;;2320:47;;-1:-1:-1;;;2320:47:50;;-1:-1:-1;;;;;1652:32:142;;2320:47:50;;;1634:51:142;1607:18;;2320:47:50;;;;;;;;2259:119;1327:66;2387:73;;-1:-1:-1;;;;;;2387:73:50;-1:-1:-1;;;;;2387:73:50;;;;;;;;;;2186:281::o;4106:253:61:-;4189:12;4214;4228:23;4255:6;-1:-1:-1;;;;;4255:19:61;4275:4;4255:25;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;4213:67:61;;-1:-1:-1;4213:67:61;-1:-1:-1;4297:55:61;4324:6;4213:67;;4297:26;:55::i;:::-;4290:62;4106:253;-1:-1:-1;;;;;4106:253:61:o;6598:122:50:-;6648:9;:13;6644:70;;6684:19;;-1:-1:-1;;;6684:19:50;;;;;;;;;;;6644:70;6598:122::o;4625:582:61:-;4769:12;4798:7;4793:408;;4821:19;4829:10;4821:7;:19::i;:::-;4793:408;;;5045:17;;:22;:49;;;;-1:-1:-1;;;;;;5071:18:61;;;:23;5045:49;5041:119;;;5121:24;;-1:-1:-1;;;5121:24:61;;-1:-1:-1;;;;;1652:32:142;;5121:24:61;;;1634:51:142;1607:18;;5121:24:61;1488:203:142;5041:119:61;-1:-1:-1;5180:10:61;4793:408;4625:582;;;;;:::o;5743:516::-;5874:17;;:21;5870:383;;6102:10;6096:17;6158:15;6145:10;6141:2;6137:19;6130:44;5870:383;6225:17;;-1:-1:-1;;;6225:17:61;;;;;;;;;;;14:127:142;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:250;231:1;241:113;255:6;252:1;249:13;241:113;;;331:11;;;325:18;312:11;;;305:39;277:2;270:10;241:113;;;-1:-1:-1;;388:1:142;370:16;;363:27;146:250::o;401:1082::-;489:6;497;550:2;538:9;529:7;525:23;521:32;518:52;;;566:1;563;556:12;518:52;592:16;;-1:-1:-1;;;;;637:31:142;;627:42;;617:70;;683:1;680;673:12;617:70;755:2;740:18;;734:25;706:5;;-1:-1:-1;;;;;;771:30:142;;768:50;;;814:1;811;804:12;768:50;837:22;;890:4;882:13;;878:27;-1:-1:-1;868:55:142;;919:1;916;909:12;868:55;946:9;;-1:-1:-1;;;;;967:30:142;;964:56;;;1000:18;;:::i;:::-;1049:2;1043:9;1141:2;1103:17;;-1:-1:-1;;1099:31:142;;;1132:2;1095:40;1091:54;1079:67;;-1:-1:-1;;;;;1161:34:142;;1197:22;;;1158:62;1155:88;;;1223:18;;:::i;:::-;1259:2;1252:22;1283;;;1324:15;;;1341:2;1320:24;1317:37;-1:-1:-1;1314:57:142;;;1367:1;1364;1357:12;1314:57;1380:72;1445:6;1440:2;1432:6;1428:15;1423:2;1419;1415:11;1380:72;:::i;:::-;1471:6;1461:16;;;;;401:1082;;;;;:::o;1696:287::-;1825:3;1863:6;1857:13;1879:66;1938:6;1933:3;1926:4;1918:6;1914:17;1879:66;:::i;:::-;1961:16;;;;;1696:287;-1:-1:-1;;1696:287:142:o;:::-;599:1116:49;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080604052600a600c565b005b60186014601a565b6050565b565b5f604b7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc546001600160a01b031690565b905090565b365f80375f80365f845af43d5f803e8080156069573d5ff35b3d5ffdfea164736f6c634300081a000a", "sourceMap": "599:1116:49:-:0;;;2649:11:51;:9;:11::i;:::-;599:1116:49;2323:83:51;2371:28;2381:17;:15;:17::i;:::-;2371:9;:28::i;:::-;2323:83::o;1581:132:49:-;1648:7;1674:32;1327:66:50;2035:53;-1:-1:-1;;;;;2035:53:50;;1957:138;1674:32:49;1667:39;;1581:132;:::o;949:895:51:-;1287:14;1284:1;1281;1268:34;1501:1;1498;1482:14;1479:1;1463:14;1456:5;1443:60;1577:16;1574:1;1571;1556:38;1615:6;1682:66;;;;1797:16;1794:1;1787:27;1682:66;1717:16;1714:1;1707:27", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"stateMutability\":\"payable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"ERC1967InvalidImplementation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ERC1967NonPayable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedInnerCall\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"stateMutability\":\"payable\",\"type\":\"fallback\"}],\"devdoc\":{\"details\":\"This contract implements an upgradeable proxy. It is upgradeable because calls are delegated to an implementation address that can be changed. This address is stored in storage in the location specified by https://eips.ethereum.org/EIPS/eip-1967[EIP1967], so that it doesn't conflict with the storage layout of the implementation behind the proxy.\",\"errors\":{\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"ERC1967InvalidImplementation(address)\":[{\"details\":\"The `implementation` of the proxy is invalid.\"}],\"ERC1967NonPayable()\":[{\"details\":\"An upgrade function sees `msg.value > 0` that may be lost.\"}],\"FailedInnerCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}]},\"events\":{\"Upgraded(address)\":{\"details\":\"Emitted when the implementation is upgraded.\"}},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"details\":\"Initializes the upgradeable proxy with an initial implementation specified by `implementation`. If `_data` is nonempty, it's used as data in a delegate call to `implementation`. This will typically be an encoded function call, and allows initializing the storage of the proxy like a Solidity constructor. Requirements: - If `data` is empty, `msg.value` must be zero.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol\":\"ERC1967Proxy\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@axelar-cgp-solidity/=node_modules/@axelar-network/axelar-cgp-solidity/\",\":@axelar-network/=node_modules/@axelar-network/\",\":@openzeppelin-contracts/=node_modules/recoverable-wrapper/node_modules/@openzeppelin/contracts/\",\":@openzeppelin/=node_modules/@openzeppelin/\",\":@uniswap/=node_modules/@uniswap/\",\":ds-test/=node_modules/recoverable-wrapper/lib/forge-std/lib/ds-test/src/\",\":forge-std/=node_modules/forge-std/src/\",\":recoverable-wrapper/contracts/=node_modules/recoverable-wrapper/contracts/\",\":solady/=node_modules/solady/src/\",\":telcoin-contracts/=node_modules/telcoin-contracts/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xbfb6695731de677140fbf76c772ab08c4233a122fb51ac28ac120fc49bbbc4ec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://68f8fded7cc318efa15874b7c6a983fe17a4a955d72d240353a9a4ca1e1b824c\",\"dweb:/ipfs/QmdcmBL9Qo4Tk3Dby4wFYabGyot9JNeLPxpSXZUgUm92BV\"]},\"node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x06a78f9b3ee3e6d0eb4e4cd635ba49960bea34cac1db8c0a27c75f2319f1fd65\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://547d21aa17f4f3f1a1a7edf7167beff8dd9496a0348d5588f15cc8a4b29d052a\",\"dweb:/ipfs/QmT16JtRQSWNpLo9W23jr6CzaMuTAcQcjJJcdRd8HLJ6cE\"]},\"node_modules/@openzeppelin/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"node_modules/@openzeppelin/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"node_modules/@openzeppelin/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"node_modules/@openzeppelin/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0x32ba59b4b7299237c8ba56319110989d7978a039faf754793064e967e5894418\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1ae50c8b562427df610cc4540c9bf104acca7ef8e2dcae567ae7e52272281e9c\",\"dweb:/ipfs/QmTHiadFCSJUPpRjNegc5SahmeU8bAoY8i9Aq6tVscbcKR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "type": "error", "name": "ERC1967InvalidImplementation"}, {"inputs": [], "type": "error", "name": "ERC1967Non<PERSON>ayable"}, {"inputs": [], "type": "error", "name": "FailedInnerCall"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": true}], "type": "event", "name": "Upgraded", "anonymous": false}, {"inputs": [], "stateMutability": "payable", "type": "fallback"}], "devdoc": {"kind": "dev", "methods": {"constructor": {"details": "Initializes the upgradeable proxy with an initial implementation specified by `implementation`. If `_data` is nonempty, it's used as data in a delegate call to `implementation`. This will typically be an encoded function call, and allows initializing the storage of the proxy like a Solidity constructor. Requirements: - If `data` is empty, `msg.value` must be zero."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@axelar-cgp-solidity/=node_modules/@axelar-network/axelar-cgp-solidity/", "@axelar-network/=node_modules/@axelar-network/", "@openzeppelin-contracts/=node_modules/recoverable-wrapper/node_modules/@openzeppelin/contracts/", "@openzeppelin/=node_modules/@openzeppelin/", "@uniswap/=node_modules/@uniswap/", "ds-test/=node_modules/recoverable-wrapper/lib/forge-std/lib/ds-test/src/", "forge-std/=node_modules/forge-std/src/", "recoverable-wrapper/contracts/=node_modules/recoverable-wrapper/contracts/", "solady/=node_modules/solady/src/", "telcoin-contracts/=node_modules/telcoin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol": "ERC1967Proxy"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xbfb6695731de677140fbf76c772ab08c4233a122fb51ac28ac120fc49bbbc4ec", "urls": ["bzz-raw://68f8fded7cc318efa15874b7c6a983fe17a4a955d72d240353a9a4ca1e1b824c", "dweb:/ipfs/QmdcmBL9Qo4Tk3Dby4wFYabGyot9JNeLPxpSXZUgUm92BV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x06a78f9b3ee3e6d0eb4e4cd635ba49960bea34cac1db8c0a27c75f2319f1fd65", "urls": ["bzz-raw://547d21aa17f4f3f1a1a7edf7167beff8dd9496a0348d5588f15cc8a4b29d052a", "dweb:/ipfs/QmT16JtRQSWNpLo9W23jr6CzaMuTAcQcjJJcdRd8HLJ6cE"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/StorageSlot.sol": {"keccak256": "0x32ba59b4b7299237c8ba56319110989d7978a039faf754793064e967e5894418", "urls": ["bzz-raw://1ae50c8b562427df610cc4540c9bf104acca7ef8e2dcae567ae7e52272281e9c", "dweb:/ipfs/QmTHiadFCSJUPpRjNegc5SahmeU8bAoY8i9Aq6tVscbcKR"], "license": "MIT"}}, "version": 1}, "id": 49}