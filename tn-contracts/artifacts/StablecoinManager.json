{"abi": [{"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "FAUCET_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "MAINTAINER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "NATIVE_TOKEN_POINTER", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "PAUSER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SWAPPER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "UpdateXYZ", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "validity", "type": "bool", "internalType": "bool"}, {"name": "maxLimit", "type": "uint256", "internalType": "uint256"}, {"name": "minLimit", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "__StablecoinHandler_init", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "__StablecoinHandler_init_unchained", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "convertFromEXYZ", "inputs": [{"name": "wallet", "type": "address", "internalType": "address"}, {"name": "safe", "type": "address", "internalType": "address"}, {"name": "ss", "type": "tuple", "internalType": "struct StablecoinHandler.StablecoinSwap", "components": [{"name": "destination", "type": "address", "internalType": "address"}, {"name": "origin", "type": "address", "internalType": "address"}, {"name": "oAmount", "type": "uint256", "internalType": "uint256"}, {"name": "target", "type": "address", "internalType": "address"}, {"name": "tAmount", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "convertToEXYZ", "inputs": [{"name": "wallet", "type": "address", "internalType": "address"}, {"name": "safe", "type": "address", "internalType": "address"}, {"name": "ss", "type": "tuple", "internalType": "struct StablecoinHandler.StablecoinSwap", "components": [{"name": "destination", "type": "address", "internalType": "address"}, {"name": "origin", "type": "address", "internalType": "address"}, {"name": "oAmount", "type": "uint256", "internalType": "uint256"}, {"name": "target", "type": "address", "internalType": "address"}, {"name": "tAmount", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "drip", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getDripAmount", "inputs": [], "outputs": [{"name": "dripAmount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getEnabledXYZs", "inputs": [], "outputs": [{"name": "enabledXYZs", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getEnabledXYZsWithMetadata", "inputs": [], "outputs": [{"name": "enabledXYZMetadatas", "type": "tuple[]", "internalType": "struct StablecoinManager.XYZMetadata[]", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "view"}, {"type": "function", "name": "getLastFulfilledDripTimestamp", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [{"name": "timestamp", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getMaxLimit", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getMinLimit", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getNativeDripAmount", "inputs": [], "outputs": [{"name": "nativeDripAmount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleAdmin", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "initParams", "type": "tuple", "internalType": "struct StablecoinManager.StablecoinManagerInitParams", "components": [{"name": "admin_", "type": "address", "internalType": "address"}, {"name": "maintainer_", "type": "address", "internalType": "address"}, {"name": "tokens_", "type": "address[]", "internalType": "address[]"}, {"name": "initMaxLimit", "type": "uint256", "internalType": "uint256"}, {"name": "initMinLimit", "type": "uint256", "internalType": "uint256"}, {"name": "authorizedFaucets_", "type": "address[]", "internalType": "address[]"}, {"name": "dripAmount_", "type": "uint256", "internalType": "uint256"}, {"name": "nativeDripAmount_", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isEnabledXYZ", "inputs": [{"name": "eXYZ", "type": "address", "internalType": "address"}], "outputs": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isXYZ", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "renounceRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "callerConfirmation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "rescueCrypto", "inputs": [{"name": "token", "type": "address", "internalType": "contract IERC20"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setDripAmount", "inputs": [{"name": "newDripAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setLowBalanceThreshold", "inputs": [{"name": "newThreshold", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setNativeDripAmount", "inputs": [{"name": "newNativeDripAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "swapAndSend", "inputs": [{"name": "wallet", "type": "address", "internalType": "address"}, {"name": "ss", "type": "tuple", "internalType": "struct StablecoinHandler.StablecoinSwap", "components": [{"name": "destination", "type": "address", "internalType": "address"}, {"name": "origin", "type": "address", "internalType": "address"}, {"name": "oAmount", "type": "uint256", "internalType": "uint256"}, {"name": "target", "type": "address", "internalType": "address"}, {"name": "tAmount", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "event", "name": "<PERSON><PERSON>", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "DripAmountUpdated", "inputs": [{"name": "newDripAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "FaucetLowNativeBalance", "inputs": [], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "NativeDripAmountUpdated", "inputs": [{"name": "newNativeDripAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Paused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleAdminChanged", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "previousAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "newAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Unpaused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "XYZAdded", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "XYZRemoved", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "XYZUpdated", "inputs": [{"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "validity", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "max", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "min", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []}, {"type": "error", "name": "AccessControlUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AddressInsufficientBalance", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AlreadyEnabled", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "EnforcedPause", "inputs": []}, {"type": "error", "name": "ExpectedPause", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "InvalidDripAmount", "inputs": [{"name": "dripAmount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidMintBurnBoundry", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "InvalidOrDisabled", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "LowLevelCallFailure", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "RequestIneligibleUntil", "inputs": [{"name": "unixTimestamp", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ZeroValueInput", "inputs": [{"name": "value", "type": "string", "internalType": "string"}]}], "bytecode": {"object": "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", "sourceMap": "762:12607:127:-:0;;;1060:4:53;1017:48;;762:12607:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "762:12607:127:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3443:202:32;;;;;;;;;;-1:-1:-1;3443:202:32;;;;;:::i;:::-;;:::i;:::-;;;470:14:142;;463:22;445:41;;433:2;418:18;3443:202:32;;;;;;;;2466:829:127;;;;;;;;;;-1:-1:-1;2466:829:127;;;;;:::i;:::-;;:::i;:::-;;5087:647;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;6945:190:113:-;;;;;;;;;;-1:-1:-1;6945:190:113;;;;;:::i;:::-;-1:-1:-1;;;;;7104:15:113;7004:4;7104:15;;;-1:-1:-1;;;;;;;;;;;7104:15:113;;;;;:24;;;;6945:190;2359:59:127;;;;;;;;;;;;2414:3;2359:59;;;;;-1:-1:-1;;;;;3294:32:142;;;3276:51;;3264:2;3249:18;2359:59:127;3130:203:142;4759:191:32;;;;;;;;;;-1:-1:-1;4759:191:32;;;;;:::i;:::-;;:::i;:::-;;;3715:25:142;;;3703:2;3688:18;4759:191:32;3569:177:142;5246:136:32;;;;;;;;;;-1:-1:-1;5246:136:32;;;;;:::i;:::-;;:::i;2670:104:113:-;;;;;;;;;;;;;:::i;6348:245:32:-;;;;;;;;;;-1:-1:-1;6348:245:32;;;;;:::i;:::-;;:::i;10865:77:113:-;;;;;;;;;;;;;:::i;9541:383:127:-;;;;;;;;;;-1:-1:-1;9541:383:127;;;;;:::i;:::-;;:::i;2290:62::-;;;;;;;;;;;;2328:24;2290:62;;3892:214:53;;;;;;:::i;:::-;;:::i;7799:200:113:-;;;;;;;;;;-1:-1:-1;7799:200:113;;;;;:::i;:::-;;:::i;3439:134:53:-;;;;;;;;;;;;;:::i;7990:210:127:-;;;;;;;;;;-1:-1:-1;7990:210:127;;;;;:::i;:::-;;:::i;2692:145:41:-;;;;;;;;;;-1:-1:-1;;;;;;;;;;;;2821:9:41;;;2692:145;;3898:1008:127;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;2791:173:134:-;;;;;;;;;;-1:-1:-1;;;;;;;;;;;;2938:19:134;2791:173;;2556:155;;;;;;;;;;-1:-1:-1;;;;;;;;;;;;2691:13:134;2556:155;;10576:73:113;;;;;;;;;;;;;:::i;4935:542::-;;;;;;;;;;-1:-1:-1;4935:542:113;;;;;:::i;:::-;;:::i;3732:207:32:-;;;;;;;;;;-1:-1:-1;3732:207:32;;;;;:::i;:::-;;:::i;2551:113:113:-;;;;;;;;;;;;;:::i;8726:200::-;;;;;;;;;;-1:-1:-1;8726:200:113;;;;;:::i;:::-;;:::i;8417:245:127:-;;;;;;;;;;-1:-1:-1;8417:245:127;;;;;:::i;:::-;;:::i;5916:546:113:-;;;;;;;;;;-1:-1:-1;5916:546:113;;;;;:::i;:::-;;:::i;8803:160:127:-;;;;;;;;;;-1:-1:-1;8803:160:127;;;;;:::i;:::-;;:::i;2317:49:32:-;;;;;;;;;;-1:-1:-1;2317:49:32;2362:4;2317:49;;1708:58:53;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;1708:58:53;;;;;;;;;;;;:::i;5831:350:127:-;;;;;;;;;;-1:-1:-1;5831:350:127;;;;;:::i;:::-;;:::i;3896:610:113:-;;;;;;;;;;-1:-1:-1;3896:610:113;;;;;:::i;:::-;;:::i;3075:232:134:-;;;;;;;;;;-1:-1:-1;3075:232:134;;;;;:::i;:::-;;:::i;5662:138:32:-;;;;;;;;;;-1:-1:-1;5662:138:32;;;;;:::i;:::-;;:::i;1991:64:113:-;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;1991:64:113;;1923:62;;;;;;;;;;;;1961:24;1923:62;;3301:464:127;;;;;;;;;;-1:-1:-1;3301:464:127;;;;;:::i;:::-;;:::i;6620:139::-;;;;;;;;;;-1:-1:-1;6620:139:127;;;;;:::i;:::-;;:::i;2061:70:113:-;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;2061:70:113;;3443:202:32;3528:4;-1:-1:-1;;;;;;3551:47:32;;-1:-1:-1;;;3551:47:32;;:87;;-1:-1:-1;;;;;;;;;;1133:40:43;;;3602:36:32;3544:94;3443:202;-1:-1:-1;;3443:202:32:o;2466:829:127:-;-1:-1:-1;;;;;;;;;;;4302:15:34;;-1:-1:-1;;;4302:15:34;;;;4301:16;;-1:-1:-1;;;;;4348:14:34;4158:30;4726:16;;:34;;;;;4746:14;4726:34;4706:54;;4770:17;4790:11;-1:-1:-1;;;;;4790:16:34;4805:1;4790:16;:50;;;;-1:-1:-1;4818:4:34;4810:25;:30;4790:50;4770:70;;4856:12;4855:13;:30;;;;;4873:12;4872:13;4855:30;4851:91;;;4908:23;;-1:-1:-1;;;4908:23:34;;;;;;;;;;;4851:91;4951:18;;-1:-1:-1;;4951:18:34;4968:1;4951:18;;;4979:67;;;;5013:22;;-1:-1:-1;;;;5013:22:34;-1:-1:-1;;;5013:22:34;;;4979:67;2564:26:127::1;:24;:26::i;:::-;2600:67;2614:10;:22;;;2638:10;:28;;;2600:13;:67::i;:::-;2677:31;2701:6;5060:22:134::0;:37;4934:170;2677:31:127::1;2779:59;2414:3;2811:4;-1:-1:-1::0;;2836:1:127::1;2779:9;:59::i;:::-;2853:9;2848:165;2868:18;;::::0;::::1;:10:::0;:18:::1;:::i;:::-;:25;;2864:1;:29;2848:165;;;2914:88;2924:18;;::::0;::::1;:10:::0;:18:::1;:::i;:::-;2943:1;2924:21;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;2947:4;2953:10;:23;;;2978:10;:23;;;2914:9;:88::i;:::-;2895:3;;2848:165;;;-1:-1:-1::0;3023:49:127::1;2362:4:32;3054:17:127;;::::0;::::1;:10:::0;:17:::1;:::i;:::-;3023:10;:49::i;:::-;-1:-1:-1::0;3082:51:127::1;-1:-1:-1::0;;;;;;;;;;;3110:22:127::1;::::0;;;::::1;::::0;::::1;;:::i;3082:51::-;;3149:9;3144:145;3164:29;;::::0;::::1;:10:::0;:29:::1;:::i;:::-;:36;;3160:1;:40;3144:145;;;3221:57;2328:24;3245:29;;::::0;::::1;:10:::0;:29:::1;:::i;:::-;3275:1;3245:32;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;3221:57::-;-1:-1:-1::0;3202:3:127::1;;3144:145;;;;5070:14:34::0;5066:101;;;5100:23;;-1:-1:-1;;;;5100:23:34;;;5142:14;;-1:-1:-1;11141:50:142;;5142:14:34;;11129:2:142;11114:18;5142:14:34;;;;;;;;5066:101;4092:1081;;;;;2466:829:127;:::o;5087:647::-;5146:40;5241:28;5272:16;:14;:16::i;:::-;5241:47;;5339:11;:18;-1:-1:-1;;;;;5321:37:127;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5321:37:127;;;;;;;;;;;;;;;;;5299:59;;5373:9;5368:360;5388:11;:18;5384:1;:22;5368:360;;;5427:18;5460:11;5472:1;5460:14;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;5448:32:127;;:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;5448:34:127;;;;;;;;;;;;:::i;:::-;5427:55;;5496:20;5531:11;5543:1;5531:14;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;5519:34:127;;:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;5519:36:127;;;;;;;;;;;;:::i;:::-;5496:59;;5569:16;5600:11;5612:1;5600:14;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;5588:36:127;;:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5569:57;;5666:51;;;;;;;;5678:11;5690:1;5678:14;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;5666:51:127;;;;;5694:4;5666:51;;;;5700:6;5666:51;;;;5708:8;5666:51;;;5641:19;5661:1;5641:22;;;;;;;;:::i;:::-;;;;;;:76;;;;5413:315;;;5408:3;;;;;5368:360;;;;5188:546;5087:647;:::o;4759:191:32:-;4824:7;4919:14;;;-1:-1:-1;;;;;;;;;;;4919:14:32;;;;;:24;;;;4759:191::o;5246:136::-;5320:18;5333:4;5320:12;:18::i;:::-;3191:16;3202:4;3191:10;:16::i;:::-;5350:25:::1;5361:4;5367:7;5350:10;:25::i;:::-;;5246:136:::0;;;:::o;2670:104:113:-;6931:20:34;:18;:20::i;:::-;2750:17:113::1;:15;:17::i;:::-;2670:104::o:0;6348:245:32:-;-1:-1:-1;;;;;6441:34:32;;966:10:39;6441:34:32;6437:102;;6498:30;;-1:-1:-1;;;6498:30:32;;;;;;;;;;;6437:102;6549:37;6561:4;6567:18;6549:11;:37::i;:::-;;6348:245;;:::o;10865:77:113:-;1961:24;3191:16:32;3202:4;3191:10;:16::i;:::-;10925:10:113::1;:8;:10::i;:::-;10865:77:::0;:::o;9541:383:127:-;-1:-1:-1;;;;;;;;;;;3191:16:32;3202:4;3191:10;:16::i;:::-;-1:-1:-1;;;;;9640:30:127;::::1;9636:282;;9726:38;::::0;9715:6:::1;::::0;966:10:39;;9752:6:127;;9715;9726:38;9715:6;9726:38;9752:6;966:10:39;9726:38:127::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;9714:50;;;9783:1;9778:36;;9793:21;;-1:-1:-1::0;;;9793:21:127::1;;;;;;;;;;;9636:282;9867:40;-1:-1:-1::0;;;;;9867:18:127;::::1;966:10:39::0;9900:6:127;9867:18:::1;:40::i;3892:214:53:-:0;2542:13;:11;:13::i;:::-;4007:36:::1;4025:17;4007;:36::i;:::-;4053:46;4075:17;4094:4;4053:21;:46::i;:::-;3892:214:::0;;:::o;7799:200:113:-;-1:-1:-1;;;;;7967:15:113;7864:7;7967:15;;;-1:-1:-1;;;;;;;;;;;7967:15:113;;;;;:25;;;;7799:200::o;3439:134:53:-;3508:7;2813:20;:18;:20::i;:::-;-1:-1:-1;;;;;;;;;;;;3439:134:53;:::o;7990:210:127:-;-1:-1:-1;;;;;;;;;;;3191:16:32;3202:4;3191:10;:16::i;:::-;8094:13:127::1;8111:1;8094:18:::0;8090:63:::1;;8121:32;::::0;-1:-1:-1;;;8121:32:127;;::::1;::::0;::::1;3715:25:142::0;;;3688:18;;8121:32:127::1;;;;;;;;8090:63;8164:29;8179:13;8164:14;:29::i;3898:1008::-:0;-1:-1:-1;;;;;;;;;;;4059:55:127;;;;;;;;;;;;;;;;;;;3945:28;;12215;3985:34;;12215:28;;4059:55;;;12215:28;4059:55;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;4059:55:127;;;;;;;;;;;;;;;;;;;;;;;4213:21;:28;4245:1;4213:33;4209:62;;-1:-1:-1;;4255:16:127;;;4269:1;4255:16;;;;;;;;;3898:1008;-1:-1:-1;3898:1008:127:o;4209:62::-;4283:16;4301:19;4324:44;4346:21;4324;:44::i;:::-;4282:86;;;;4383:11;4378:522;;-1:-1:-1;4417:21:127;;3898:1008;-1:-1:-1;;;3898:1008:127:o;4378:522::-;4518:19;4571:1;4540:21;:28;:32;;;;:::i;:::-;4518:54;;4614:11;-1:-1:-1;;;;;4600:26:127;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;4600:26:127;;4586:40;;4641:18;4678:9;4673:217;4693:21;:28;4689:1;:32;4673:217;;;4746:30;;;4768:8;4746:30;4821:21;4843:1;4821:24;;;;;;;;:::i;:::-;;;;;;;4795:11;4807:10;4795:23;;;;;;;;:::i;:::-;-1:-1:-1;;;;;4795:50:127;;;:23;;;;;;;;;;;:50;4863:12;;;:::i;:::-;;;4673:217;4723:3;;4673:217;;;;4455:445;;3975:931;;;;3898:1008;:::o;10576:73:113:-;1961:24;3191:16:32;3202:4;3191:10;:16::i;:::-;10634:8:113::1;:6;:8::i;4935:542::-:0;2316:19:41;:17;:19::i;:::-;3147:14:113;;5081:2;;-1:-1:-1;;;;;3147:28:113::1;::::0;;:67:::1;;-1:-1:-1::0;3191:9:113::1;::::0;::::1;::::0;-1:-1:-1;;;;;3191:23:113::1;::::0;3147:67:::1;:98;;;-1:-1:-1::0;3230:10:113::1;::::0;::::1;::::0;:15;3147:98:::1;:137;;;-1:-1:-1::0;3261:9:113::1;::::0;::::1;::::0;-1:-1:-1;;;;;3261:23:113::1;::::0;3147:137:::1;:168;;;-1:-1:-1::0;3300:10:113::1;::::0;::::1;::::0;:15;3147:168:::1;3130:223;;;3333:20;;-1:-1:-1::0;;;3333:20:113::1;;;;;;;:::i;3130:223::-;-1:-1:-1::0;;;;;;;;;;;3191:16:32::2;3202:4;3191:10;:16::i;:::-;5198:22:113::3;5210:2;:9;;;5198:11;:22::i;:::-;5173:2;:10;;;5146:2;:9;;;-1:-1:-1::0;;;;;5135:33:113::3;;:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:48;;;;:::i;:::-;:85;5118:153;;;5261:9;::::0;::::3;::::0;5238:33:::3;::::0;-1:-1:-1;;;5238:33:113;;-1:-1:-1;;;;;3294:32:142;;;5238:33:113::3;::::0;::::3;3276:51:142::0;3249:18;;5238:33:113::3;3130:203:142::0;5118:153:113::3;5282:122;5346:6;5366:4;5384:2;:10;;;5305:2;:9;;;-1:-1:-1::0;;;;;5282:50:113::3;;;:122;;;;;;:::i;:::-;5425:9;::::0;::::3;::::0;5443:14;;5459:10:::3;::::0;::::3;::::0;5414:56:::3;::::0;-1:-1:-1;;;5414:56:113;;-1:-1:-1;;;;;13331:32:142;;;5414:56:113::3;::::0;::::3;13313:51:142::0;13380:18;;;13373:34;;;;5414:28:113;::::3;::::0;::::3;::::0;13286:18:142;;5414:56:113::3;;;;;;;;;;;;;;;;;::::0;::::3;;;;;;;;;;;;::::0;::::3;;;;;;;;;3363:1:::2;2345::41::1;4935:542:113::0;;;:::o;3732:207:32:-;3809:4;3901:14;;;-1:-1:-1;;;;;;;;;;;3901:14:32;;;;;;;;-1:-1:-1;;;;;3901:31:32;;;;;;;;;;;;;;;3732:207::o;2551:113:113:-;6931:20:34;:18;:20::i;:::-;2621:36:113::1;:34;:36::i;8726:200::-:0;-1:-1:-1;;;;;8894:15:113;8791:7;8894:15;;;-1:-1:-1;;;;;;;;;;;8894:15:113;;;;;:25;;;;8726:200::o;8417:245:127:-;-1:-1:-1;;;;;;;;;;;3191:16:32;3202:4;3191:10;:16::i;:::-;8533:19:127::1;8556:1;8533:24:::0;8529:75:::1;;8566:38;::::0;-1:-1:-1;;;8566:38:127;;::::1;::::0;::::1;3715:25:142::0;;;3688:18;;8566:38:127::1;3569:177:142::0;8529:75:127::1;8614:41;8635:19;8614:20;:41::i;5916:546:113:-:0;2316:19:41;:17;:19::i;:::-;3147:14:113;;6064:2;;-1:-1:-1;;;;;3147:28:113::1;::::0;;:67:::1;;-1:-1:-1::0;3191:9:113::1;::::0;::::1;::::0;-1:-1:-1;;;;;3191:23:113::1;::::0;3147:67:::1;:98;;;-1:-1:-1::0;3230:10:113::1;::::0;::::1;::::0;:15;3147:98:::1;:137;;;-1:-1:-1::0;3261:9:113::1;::::0;::::1;::::0;-1:-1:-1;;;;;3261:23:113::1;::::0;3147:137:::1;:168;;;-1:-1:-1::0;3300:10:113::1;::::0;::::1;::::0;:15;3147:168:::1;3130:223;;;3333:20;;-1:-1:-1::0;;;3333:20:113::1;;;;;;;:::i;3130:223::-;-1:-1:-1::0;;;;;;;;;;;3191:16:32::2;3202:4;3191:10;:16::i;:::-;6181:22:113::3;6193:2;:9;;;6181:11;:22::i;:::-;6156:2;:10;;;6129:2;:9;;;-1:-1:-1::0;;;;;6118:33:113::3;;:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:48;;;;:::i;:::-;:85;6101:153;;;6244:9;::::0;::::3;::::0;6221:33:::3;::::0;-1:-1:-1;;;6221:33:113;;-1:-1:-1;;;;;3294:32:142;;;6221:33:113::3;::::0;::::3;3276:51:142::0;3249:18;;6221:33:113::3;3130:203:142::0;6101:153:113::3;6276:9;::::0;::::3;::::0;6304:10:::3;::::0;;::::3;::::0;6265:50;;-1:-1:-1;;;6265:50:113;;-1:-1:-1;;;;;13331:32:142;;;6265:50:113::3;::::0;::::3;13313:51:142::0;13380:18;;;13373:34;;;;6265:30:113;::::3;::::0;::::3;::::0;13286:18:142;;6265:50:113::3;;;;;;;;;;;;;;;;;::::0;::::3;;;;;;;;;;;;::::0;::::3;;;;;-1:-1:-1::0;;6407:14:113;;6435:10:::3;::::0;::::3;::::0;6348:9:::3;::::0;::::3;::::0;6325:130:::3;::::0;-1:-1:-1;;;;;;6325:50:113::3;::::0;-1:-1:-1;6389:4:113;;6407:14;6325:50:::3;:130::i;:::-;3363:1:::2;2345::41::1;5916:546:113::0;;;:::o;8803:160:127:-;-1:-1:-1;;;;;;;;;;;3191:16:32;3202:4;3191:10;:16::i;:::-;8919:37:127::1;8943:12;5060:22:134::0;:37;4934:170;5831:350:127;-1:-1:-1;;;;;;;;;;;5988:45:127;;;;;;;;;;;;;;;;;;;5888:14;;12215:28;5888:14;;12215:28;;5988:45;;;12215:28;5988:45;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;5988:45:127;;;;;;;;;;;;;;;;;;;;;;;6048:9;6043:109;6063:11;:18;6059:1;:22;6043:109;;;6124:4;-1:-1:-1;;;;;6106:22:127;:11;6118:1;6106:14;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;6106:22:127;;6102:39;;-1:-1:-1;6137:4:127;;5831:350;-1:-1:-1;;;;5831:350:127:o;6102:39::-;6083:3;;6043:109;;;-1:-1:-1;6169:5:127;;5831:350;-1:-1:-1;;;;5831:350:127:o;3896:610:113:-;2316:19:41;:17;:19::i;:::-;3147:14:113;;4018:2;;-1:-1:-1;;;;;3147:28:113::1;::::0;;:67:::1;;-1:-1:-1::0;3191:9:113::1;::::0;::::1;::::0;-1:-1:-1;;;;;3191:23:113::1;::::0;3147:67:::1;:98;;;-1:-1:-1::0;3230:10:113::1;::::0;::::1;::::0;:15;3147:98:::1;:137;;;-1:-1:-1::0;3261:9:113::1;::::0;::::1;::::0;-1:-1:-1;;;;;3261:23:113::1;::::0;3147:137:::1;:168;;;-1:-1:-1::0;3300:10:113::1;::::0;::::1;::::0;:15;3147:168:::1;3130:223;;;3333:20;;-1:-1:-1::0;;;3333:20:113::1;;;;;;;:::i;3130:223::-;-1:-1:-1::0;;;;;;;;;;;3191:16:32::2;3202:4;3191:10;:16::i;:::-;4135:22:113::3;4147:2;:9;;;4135:11;:22::i;:::-;4110:2;:10;;;4083:2;:9;;;-1:-1:-1::0;;;;;4072:33:113::3;;:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:48;;;;:::i;:::-;:85;4055:153;;;4198:9;::::0;::::3;::::0;4175:33:::3;::::0;-1:-1:-1;;;4175:33:113;;-1:-1:-1;;;;;3294:32:142;;;4175:33:113::3;::::0;::::3;3276:51:142::0;3249:18;;4175:33:113::3;3130:203:142::0;4055:153:113::3;4299:22;4311:2;:9;;;4299:11;:22::i;:::-;4274:2;:10;;;4247:2;:9;;;-1:-1:-1::0;;;;;4236:33:113::3;;:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:48;;;;:::i;:::-;:85;4219:153;;;4362:9;::::0;::::3;::::0;4339:33:::3;::::0;-1:-1:-1;;;4339:33:113;;-1:-1:-1;;;;;3294:32:142;;;4339:33:113::3;::::0;::::3;3276:51:142::0;3249:18;;4339:33:113::3;3130:203:142::0;4219:153:113::3;4394:9;::::0;::::3;::::0;4422:10:::3;::::0;;::::3;::::0;4383:50;;-1:-1:-1;;;4383:50:113;;-1:-1:-1;;;;;13331:32:142;;;4383:50:113::3;::::0;::::3;13313:51:142::0;13380:18;;;13373:34;;;;4383:30:113;::::3;::::0;::::3;::::0;13286:18:142;;4383:50:113::3;;;;;;;;;;;;;;;;;::::0;::::3;;;;;;;;;;;;::::0;::::3;;;;;-1:-1:-1::0;;;;4454:9:113::3;::::0;::::3;::::0;4472:14;;4488:10:::3;::::0;::::3;::::0;4443:56:::3;::::0;-1:-1:-1;;;4443:56:113;;-1:-1:-1;;;;;13331:32:142;;;4443:56:113::3;::::0;::::3;13313:51:142::0;13380:18;;;13373:34;;;;4443:28:113;::::3;::::0;::::3;::::0;13286:18:142;;4443:56:113::3;;;;;;;;;;;;;;;;;::::0;::::3;;;;;;;;;;;;::::0;::::3;;;;;;;;;3363:1:::2;2345::41::1;3896:610:113::0;;:::o;3075:232:134:-;-1:-1:-1;;;;;3262:31:134;;;3169:17;3262:31;;;:20;:31;;;;;;;;:38;;;;;;;;;;;;;;3075:232::o;5662:138:32:-;5737:18;5750:4;5737:12;:18::i;:::-;3191:16;3202:4;3191:10;:16::i;:::-;5767:26:::1;5779:4;5785:7;5767:11;:26::i;3301:464:127:-:0;-1:-1:-1;;;;;;;;;;;3191:16:32;3202:4;3191:10;:16::i;:::-;3597:8:127::1;:31;;;;;3609:19;3622:5;3609:12;:19::i;:::-;3593:65;;;3637:21;::::0;-1:-1:-1;;;3637:21:127;;-1:-1:-1;;;;;3294:32:142;;3637:21:127::1;::::0;::::1;3276:51:142::0;3249:18;;3637:21:127::1;3130:203:142::0;3593:65:127::1;3669:27;3680:5;3687:8;3669:10;:27::i;:::-;3706:52;3722:5;3729:8;3739;3749;3706:15;:52::i;6620:139::-:0;2328:24;3191:16:32;3202:4;3191:10;:16::i;:::-;6724:28:127::1;6735:5;6742:9;6724:10;:28::i;8969:202::-:0;-1:-1:-1;;;;;;;;;;;4302:15:34;;-1:-1:-1;;;4302:15:34;;;;4301:16;;-1:-1:-1;;;;;4348:14:34;4158:30;4726:16;;:34;;;;;4746:14;4726:34;4706:54;;4770:17;4790:11;-1:-1:-1;;;;;4790:16:34;4805:1;4790:16;:50;;;;-1:-1:-1;4818:4:34;4810:25;:30;4790:50;4770:70;;4856:12;4855:13;:30;;;;;4873:12;4872:13;4855:30;4851:91;;;4908:23;;-1:-1:-1;;;4908:23:34;;;;;;;;;;;4851:91;4951:18;;-1:-1:-1;;4951:18:34;4968:1;4951:18;;;4979:67;;;;5013:22;;-1:-1:-1;;;;5013:22:34;-1:-1:-1;;;5013:22:34;;;4979:67;9088:27:127::1;9103:11;9088:14;:27::i;:::-;9125:39;9146:17;9125:20;:39::i;:::-;5070:14:34::0;5066:101;;;5100:23;;-1:-1:-1;;;;5100:23:34;;;5142:14;;-1:-1:-1;11141:50:142;;5142:14:34;;11129:2:142;11114:18;5142:14:34;;;;;;;5066:101;4092:1081;;;;;8969:202:127;;:::o;7270:387:32:-;7347:4;-1:-1:-1;;;;;;;;;;;7437:22:32;7445:4;7451:7;7437;:22::i;:::-;7432:219;;7475:8;:14;;;;;;;;;;;-1:-1:-1;;;;;7475:31:32;;;;;;;;;:38;;-1:-1:-1;;7475:38:32;7509:4;7475:38;;;7559:12;966:10:39;;887:96;7559:12:32;-1:-1:-1;;;;;7532:40:32;7550:7;-1:-1:-1;;;;;7532:40:32;7544:4;7532:40;;;;;;;;;;7593:4;7586:11;;;;;7432:219;7635:5;7628:12;;;;;12800:283:127;966:10:39;12875:14:127;12943:21;12951:4;966:10:39;12943:7:127;:21::i;:::-;:42;;;-1:-1:-1;;;;;;;;;;;;8560:40:34;-1:-1:-1;;;8560:40:34;;;;12968:17:127;12914:71;;13000:21;12995:81;;13030:46;;-1:-1:-1;;;13030:46:127;;-1:-1:-1;;;;;13331:32:142;;13030:46:127;;;13313:51:142;13380:18;;;13373:34;;;13286:18;;13030:46:127;13139:274:142;7084:141:34;-1:-1:-1;;;;;;;;;;;8560:40:34;-1:-1:-1;;;8560:40:34;;;;7146:73;;7191:17;;-1:-1:-1;;;7191:17:34;;;;;;;;;;;1836:97:41;6931:20:34;:18;:20::i;:::-;1899:27:41::1;:25;:27::i;7892:388:32:-:0;7970:4;-1:-1:-1;;;;;;;;;;;8059:22:32;8067:4;8073:7;8059;:22::i;:::-;8055:219;;;8131:5;8097:14;;;;;;;;;;;-1:-1:-1;;;;;8097:31:32;;;;;;;;;;:39;;-1:-1:-1;;8097:39:32;;;8155:40;966:10:39;;8097:14:32;;8155:40;;8131:5;8155:40;8216:4;8209:11;;;;;3674:178:41;2563:16;:14;:16::i;:::-;-1:-1:-1;;;;;;;;;;;3791:17:41;;-1:-1:-1;;3791:17:41::1;::::0;;3823:22:::1;966:10:39::0;3832:12:41::1;3823:22;::::0;-1:-1:-1;;;;;3294:32:142;;;3276:51;;3264:2;3249:18;3823:22:41::1;;;;;;;3722:130;3674:178::o:0;1303:160:57:-;1412:43;;-1:-1:-1;;;;;13331:32:142;;;1412:43:57;;;13313:51:142;13380:18;;;13373:34;;;1385:71:57;;1405:5;;1427:14;;;;;13286:18:142;;1412:43:57;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1412:43:57;;;;;;;;;;;1385:19;:71::i;4333:312:53:-;4413:4;-1:-1:-1;;;;;4422:6:53;4405:23;;;:120;;;4519:6;-1:-1:-1;;;;;4483:42:53;:32;-1:-1:-1;;;;;;;;;;;2035:53:50;-1:-1:-1;;;;;2035:53:50;;1957:138;4483:32:53;-1:-1:-1;;;;;4483:42:53;;;4405:120;4388:251;;;4599:29;;-1:-1:-1;;;4599:29:53;;;;;;;;;;;13143:112:127;2362:4:32;3191:16;2362:4;3191:10;:16::i;5786:538:53:-;5903:17;-1:-1:-1;;;;;5885:50:53;;:52;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;5885:52:53;;;;;;;;-1:-1:-1;;5885:52:53;;;;;;;;;;;;:::i;:::-;;;5881:437;;6247:60;;-1:-1:-1;;;6247:60:53;;-1:-1:-1;;;;;3294:32:142;;6247:60:53;;;3276:51:142;3249:18;;6247:60:53;3130:203:142;5881:437:53;-1:-1:-1;;;;;;;;;;;5979:40:53;;5975:120;;6046:34;;-1:-1:-1;;;6046:34:53;;;;;3715:25:142;;;3688:18;;6046:34:53;3569:177:142;5975:120:53;6108:54;6138:17;6157:4;6108:29;:54::i;4762:213::-;4836:4;-1:-1:-1;;;;;4845:6:53;4828:23;;4824:145;;4929:29;;-1:-1:-1;;;4929:29:53;;;;;;;;;;;3694:202:134;-1:-1:-1;;;;;;;;;;;3812:29:134;;;3857:32;;3715:25:142;;;3857:32:134;;3703:2:142;3688:18;3857:32:134;;;;;;;;3750:146;3694:202;:::o;11514:450:127:-;11623:10;11635:25;11681:9;11676:222;11696:12;:19;11692:1;:23;11676:222;;;2414:3;-1:-1:-1;;;;;11740:39:127;:12;11753:1;11740:15;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;11740:39:127;;11736:152;;11807:4;11799:12;;11849:1;11829:21;;11868:5;;11736:152;11717:3;;11676:222;;;;11913:5;11908:49;;-1:-1:-1;;;11908:49:127;11514:450;;;:::o;3366:176:41:-;2316:19;:17;:19::i;:::-;-1:-1:-1;;;;;;;;;;;3484:16:41;;-1:-1:-1;;3484:16:41::1;3496:4;3484:16;::::0;;3515:20:::1;966:10:39::0;3522:12:41::1;887:96:39::0;2905:128:41;-1:-1:-1;;;;;;;;;;;2821:9:41;;;2966:61;;;3001:15;;-1:-1:-1;;;3001:15:41;;;;;;;;;;;1702:188:57;1829:53;;-1:-1:-1;;;;;14106:32:142;;;1829:53:57;;;14088:51:142;14175:32;;;14155:18;;;14148:60;14224:18;;;14217:34;;;1802:81:57;;1822:5;;1844:18;;;;;14061::142;;1829:53:57;13886:371:142;3902:238:134;-1:-1:-1;;;;;;;;;;;4032:41:134;;;4089:44;;3715:25:142;;;-1:-1:-1;;;;;;;;;;;5227:17:134;4089:44;;3703:2:142;3688:18;4089:44:134;3569:177:142;9979:206:127;10060:16;;;10072:4;10060:16;10056:123;;10092:21;10107:5;10092:14;:21::i;10056:123::-;10144:24;10162:5;10144:17;:24::i;9796:585:113:-;-1:-1:-1;;;;;;;;;;;3191:16:32;3202:4;3191:10;:16::i;:::-;10002:8:113::1;9991;:19;9970:127;;;::::0;-1:-1:-1;;;9970:127:113;;14464:2:142;9970:127:113::1;::::0;::::1;14446:21:142::0;14503:2;14483:18;;;14476:30;14542:34;14522:18;;;14515:62;14613:31;14593:18;;;14586:59;14662:19;;9970:127:113::1;14262:425:142::0;9970:127:113::1;-1:-1:-1::0;;;;;10185:15:113;::::1;10108:34;10185:15:::0;;;-1:-1:-1;;;;;;;;;;;10185:15:113::1;::::0;;;;;;;;:35;;-1:-1:-1;;10185:35:113::1;::::0;::::1;;::::0;;::::1;::::0;;-1:-1:-1;10230:25:113;::::1;:36:::0;;;10276:25:::1;::::0;;::::1;:36:::0;;;10327:47;;14917:51:142;;;14984:18;;;14977:50;15043:18;;;15036:34;;;15101:2;15086:18;;15079:34;;;1845:32:113;10327:47:::1;::::0;14904:3:142;14889:19;10327:47:113::1;14692:427:142::0;1844:216:134;1917:28;1928:5;1935:9;1917:10;:28::i;:::-;-1:-1:-1;;;;;4310:31:134;;;4258:23;4310:31;;;:20;:31;;;;;;;;:38;;;;;;;;;2004:15;4310:50;;2030:23;2036:5;2043:9;2030:5;:23::i;1939:156:41:-;6931:20:34;:18;:20::i;:::-;-1:-1:-1;;;;;;;;;;;2071:17:41;;-1:-1:-1;;2071:17:41::1;::::0;;1939:156::o;3105:126::-;-1:-1:-1;;;;;;;;;;;2821:9:41;;;3163:62;;3199:15;;-1:-1:-1;;;3199:15:41;;;;;;;;;;;4059:629:57;4478:23;4504:33;-1:-1:-1;;;;;4504:27:57;;4532:4;4504:27;:33::i;:::-;4478:59;;4551:10;:17;4572:1;4551:22;;:57;;;;;4589:10;4578:30;;;;;;;;;;;;:::i;:::-;4577:31;4551:57;4547:135;;;4631:40;;-1:-1:-1;;;4631:40:57;;-1:-1:-1;;;;;3294:32:142;;4631:40:57;;;3276:51:142;3249:18;;4631:40:57;3130:203:142;2779:335:50;2870:37;2889:17;2870:18;:37::i;:::-;2922:27;;-1:-1:-1;;;;;2922:27:50;;;;;;;;2964:11;;:15;2960:148;;2995:53;3024:17;3043:4;2995:28;:53::i;2960:148::-;3079:18;:16;:18::i;10191:196:127:-;-1:-1:-1;;;;;;;;;;;10323:26:127;;;;;;;-1:-1:-1;10323:26:127;;;;;;;-1:-1:-1;;;;;;10323:26:127;-1:-1:-1;;;;;10323:26:127;;;;;;;;10365:15;;3276:51:142;;;10365:15:127;;10323:26;3249:18:142;10365:15:127;3130:203:142;10393:1001:127;-1:-1:-1;;;;;;;;;;;10556:45:127;;;;;;;;;;;;;;;;;;;10454:34;;10556:45;;12215:28;;10556:45;;;12215:28;10556:45;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;10556:45:127;;;;;;;;;;;;;;;;;;;;;;;10652:21;-1:-1:-1;;10652:41:127;;10708:9;10703:116;10723:11;:18;10719:1;:22;10703:116;;;10784:5;-1:-1:-1;;;;;10766:23:127;:11;10778:1;10766:14;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;10766:23:127;;10762:46;;10807:1;10791:17;;10762:46;10743:3;;10703:116;;;;-1:-1:-1;;10917:13:127;:34;10913:71;;10960:24;;-1:-1:-1;;;10960:24:127;;-1:-1:-1;;;;;3294:32:142;;10960:24:127;;;3276:51:142;3249:18;;10960:24:127;3130:203:142;10913:71:127;11095:17;11136:1;11115:11;:18;:22;;;;:::i;:::-;11095:42;;11168:9;11151:13;:26;11147:111;;11225:11;11237:9;11225:22;;;;;;;;:::i;:::-;;;;;;;11193:1;:14;;11208:13;11193:29;;;;;;;;:::i;:::-;;;;;;;;;:54;;;;;-1:-1:-1;;;;;11193:54:127;;;;;-1:-1:-1;;;;;11193:54:127;;;;;;11147:111;11334:20;;:1;;:20;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;11334:20:127;;;;;;;-1:-1:-1;;;;;;11334:20:127;;;;;;;;;11370:17;;-1:-1:-1;;;;;3294:32:142;;3276:51;;11370:17:127;;3249:18:142;11370:17:127;;;;;;;10444:950;;;;10393:1001;:::o;7406:381::-;7501:19;7514:5;7501:12;:19::i;:::-;7496:57;;7529:24;;-1:-1:-1;;;7529:24:127;;-1:-1:-1;;;;;3294:32:142;;7529:24:127;;;3276:51:142;3249:18;;7529:24:127;3130:203:142;7496:57:127;7564:25;7592:47;7622:5;7629:9;7592:29;:47::i;:::-;7564:75;-1:-1:-1;7671:26:127;7564:75;7691:6;7671:26;:::i;:::-;7653:15;:44;7649:132;;;7743:26;:17;7763:6;7743:26;:::i;:::-;7720:50;;-1:-1:-1;;;7720:50:127;;;;;;3715:25:142;;3703:2;3688:18;;3569:177;6794:577:127;6879:14;-1:-1:-1;;;;;6907:29:127;;6903:416;;-1:-1:-1;;;;;;;;;;;;2938:19:134;7009:35:127;;6998:6;;-1:-1:-1;;;;;7009:14:127;;;2938:19:134;;6998:6:127;7009:35;6998:6;7009:35;2938:19:134;7009:14:127;:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6997:47;;;7063:1;7058:36;;7073:21;;-1:-1:-1;;;7073:21:127;;;;;;;;;;;7058:36;7171:24;:22;:24::i;:::-;6938:268;6903:416;;;-1:-1:-1;;;;;;;;;;;2691:13:134;7264:44:127;;-1:-1:-1;;;7264:44:127;;-1:-1:-1;;;;;13331:32:142;;;7264:44:127;;;13313:51:142;13380:18;;;13373:34;;;7226:24:127;;-1:-1:-1;7264:25:127;;;;;;13286:18:142;;7264:44:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6903:416;7334:30;;;-1:-1:-1;;;;;14106:32:142;;;14088:51;;14175:32;;14170:2;14155:18;;14148:60;14224:18;;;14217:34;;;7334:30:127;;14076:2:142;14061:18;7334:30:127;;;;;;;6869:502;6794:577;;:::o;2705:151:61:-;2780:12;2811:38;2833:6;2841:4;2847:1;2811:21;:38::i;:::-;2804:45;2705:151;-1:-1:-1;;;2705:151:61:o;2186:281:50:-;2263:17;-1:-1:-1;;;;;2263:29:50;;2296:1;2263:34;2259:119;;2320:47;;-1:-1:-1;;;2320:47:50;;-1:-1:-1;;;;;3294:32:142;;2320:47:50;;;3276:51:142;3249:18;;2320:47:50;3130:203:142;2259:119:50;-1:-1:-1;;;;;;;;;;;2387:73:50;;-1:-1:-1;;;;;;2387:73:50;-1:-1:-1;;;;;2387:73:50;;;;;;;;;;2186:281::o;4106:253:61:-;4189:12;4214;4228:23;4255:6;-1:-1:-1;;;;;4255:19:61;4275:4;4255:25;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4213:67;;;;4297:55;4324:6;4332:7;4341:10;4297:26;:55::i;:::-;4290:62;4106:253;-1:-1:-1;;;;;4106:253:61:o;6598:122:50:-;6648:9;:13;6644:70;;6684:19;;-1:-1:-1;;;6684:19:50;;;;;;;;;;;4474:250:134;4527:26;4556:25;4899:22;;;4730:198;4556:25;4527:54;;4644:18;4620:21;-1:-1:-1;;;;;;;;;;;2938:19:134;;2791:173;4620:21;:42;;;;:::i;:::-;4595:21;:67;4591:127;;4683:24;;;;;;;4517:207;4474:250::o;3180:392:61:-;3279:12;3331:5;3307:21;:29;3303:108;;;3359:41;;-1:-1:-1;;;3359:41:61;;3394:4;3359:41;;;3276:51:142;3249:18;;3359:41:61;3130:203:142;3303:108:61;3421:12;3435:23;3462:6;-1:-1:-1;;;;;3462:11:61;3481:5;3488:4;3462:31;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3420:73;;;;3510:55;3537:6;3545:7;3554:10;3510:26;:55::i;:::-;3503:62;3180:392;-1:-1:-1;;;;;;3180:392:61:o;4625:582::-;4769:12;4798:7;4793:408;;4821:19;4829:10;4821:7;:19::i;:::-;4793:408;;;5045:17;;:22;:49;;;;-1:-1:-1;;;;;;5071:18:61;;;:23;5045:49;5041:119;;;5121:24;;-1:-1:-1;;;5121:24:61;;-1:-1:-1;;;;;3294:32:142;;5121:24:61;;;3276:51:142;3249:18;;5121:24:61;3130:203:142;5041:119:61;-1:-1:-1;5180:10:61;5173:17;;5743:516;5874:17;;:21;5870:383;;6102:10;6096:17;6158:15;6145:10;6141:2;6137:19;6130:44;5870:383;6225:17;;-1:-1:-1;;;6225:17:61;;;;;;;;;;;14:286:142;72:6;125:2;113:9;104:7;100:23;96:32;93:52;;;141:1;138;131:12;93:52;167:23;;-1:-1:-1;;;;;;219:32:142;;209:43;;199:71;;266:1;263;256:12;497:408;604:6;657:2;645:9;636:7;632:23;628:32;625:52;;;673:1;670;663:12;625:52;713:9;700:23;-1:-1:-1;;;;;738:6:142;735:30;732:50;;;778:1;775;768:12;732:50;801:22;;857:3;839:16;;;835:26;832:46;;;874:1;871;864:12;910:250;995:1;1005:113;1019:6;1016:1;1013:13;1005:113;;;1095:11;;;1089:18;1076:11;;;1069:39;1041:2;1034:10;1005:113;;;-1:-1:-1;;1152:1:142;1134:16;;1127:27;910:250::o;1165:271::-;1207:3;1245:5;1239:12;1272:6;1267:3;1260:19;1288:76;1357:6;1350:4;1345:3;1341:14;1334:4;1327:5;1323:16;1288:76;:::i;:::-;1418:2;1397:15;-1:-1:-1;;1393:29:142;1384:39;;;;1425:4;1380:50;;1165:271;-1:-1:-1;;1165:271:142:o;1441:1296::-;1643:4;1691:2;1680:9;1676:18;1721:2;1710:9;1703:21;1744:6;1779;1773:13;1810:6;1802;1795:22;1848:2;1837:9;1833:18;1826:25;;1910:2;1900:6;1897:1;1893:14;1882:9;1878:30;1874:39;1860:53;;1948:2;1940:6;1936:15;1969:1;1979:729;1993:6;1990:1;1987:13;1979:729;;;2058:22;;;-1:-1:-1;;2054:36:142;2042:49;;2114:13;;2159:9;;-1:-1:-1;;;;;2155:35:142;2140:51;;2238:2;2230:11;;;2224:18;2279:4;2262:15;;;2255:29;;;2224:18;2311:50;;2343:17;;2224:18;2311:50;:::i;:::-;2297:64;;2410:2;2406;2402:11;2396:18;2463:6;2455;2451:19;2446:2;2438:6;2434:15;2427:44;2498:41;2532:6;2516:14;2498:41;:::i;:::-;2592:4;2584:13;;;2578:20;2559:17;;;;2552:47;;;;-1:-1:-1;2484:55:142;-1:-1:-1;2663:2:142;2686:12;;;;2651:15;;;;;2015:1;2008:9;1979:729;;;-1:-1:-1;2725:6:142;;1441:1296;-1:-1:-1;;;;;;1441:1296:142:o;2742:131::-;-1:-1:-1;;;;;2817:31:142;;2807:42;;2797:70;;2863:1;2860;2853:12;2878:247;2937:6;2990:2;2978:9;2969:7;2965:23;2961:32;2958:52;;;3006:1;3003;2996:12;2958:52;3045:9;3032:23;3064:31;3089:5;3064:31;:::i;3338:226::-;3397:6;3450:2;3438:9;3429:7;3425:23;3421:32;3418:52;;;3466:1;3463;3456:12;3418:52;-1:-1:-1;3511:23:142;;3338:226;-1:-1:-1;3338:226:142:o;3751:367::-;3819:6;3827;3880:2;3868:9;3859:7;3855:23;3851:32;3848:52;;;3896:1;3893;3886:12;3848:52;3941:23;;;-1:-1:-1;4040:2:142;4025:18;;4012:32;4053:33;4012:32;4053:33;:::i;:::-;4105:7;4095:17;;;3751:367;;;;;:::o;4123:382::-;4206:6;4214;4267:2;4255:9;4246:7;4242:23;4238:32;4235:52;;;4283:1;4280;4273:12;4235:52;4322:9;4309:23;4341:31;4366:5;4341:31;:::i;:::-;4391:5;4469:2;4454:18;;;;4441:32;;-1:-1:-1;;;4123:382:142:o;4510:127::-;4571:10;4566:3;4562:20;4559:1;4552:31;4602:4;4599:1;4592:15;4626:4;4623:1;4616:15;4642:275;4713:2;4707:9;4778:2;4759:13;;-1:-1:-1;;4755:27:142;4743:40;;-1:-1:-1;;;;;4798:34:142;;4834:22;;;4795:62;4792:88;;;4860:18;;:::i;:::-;4896:2;4889:22;4642:275;;-1:-1:-1;4642:275:142:o;4922:186::-;4970:4;-1:-1:-1;;;;;4995:6:142;4992:30;4989:56;;;5025:18;;:::i;:::-;-1:-1:-1;5091:2:142;5070:15;-1:-1:-1;;5066:29:142;5097:4;5062:40;;4922:186::o;5113:830::-;5190:6;5198;5251:2;5239:9;5230:7;5226:23;5222:32;5219:52;;;5267:1;5264;5257:12;5219:52;5306:9;5293:23;5325:31;5350:5;5325:31;:::i;:::-;5375:5;-1:-1:-1;5431:2:142;5416:18;;5403:32;-1:-1:-1;;;;;5447:30:142;;5444:50;;;5490:1;5487;5480:12;5444:50;5513:22;;5566:4;5558:13;;5554:27;-1:-1:-1;5544:55:142;;5595:1;5592;5585:12;5544:55;5635:2;5622:16;5660:52;5676:35;5704:6;5676:35;:::i;:::-;5660:52;:::i;:::-;5735:6;5728:5;5721:21;5783:7;5778:2;5769:6;5765:2;5761:15;5757:24;5754:37;5751:57;;;5804:1;5801;5794:12;5751:57;5859:6;5854:2;5850;5846:11;5841:2;5834:5;5830:14;5817:49;5911:1;5906:2;5897:6;5890:5;5886:18;5882:27;5875:38;5932:5;5922:15;;;;;5113:830;;;;;:::o;6361:637::-;6551:2;6563:21;;;6633:13;;6536:18;;;6655:22;;;6503:4;;6734:15;;;6708:2;6693:18;;;6503:4;6777:195;6791:6;6788:1;6785:13;6777:195;;;6856:13;;-1:-1:-1;;;;;6852:39:142;6840:52;;6921:2;6947:15;;;;6912:12;;;;6888:1;6806:9;6777:195;;;-1:-1:-1;6989:3:142;;6361:637;-1:-1:-1;;;;;6361:637:142:o;7003:1022::-;7064:5;7112:4;7100:9;7095:3;7091:19;7087:30;7084:50;;;7130:1;7127;7120:12;7084:50;7183:2;7177:9;7225:4;7213:17;;-1:-1:-1;;;;;7245:34:142;;7281:22;;;7242:62;7239:88;;;7307:18;;:::i;:::-;7343:2;7336:22;7376:6;-1:-1:-1;7376:6:142;7406:23;;7438:33;7406:23;7438:33;:::i;:::-;7480:23;;7555:2;7540:18;;7527:32;7568:33;7527:32;7568:33;:::i;:::-;7629:2;7617:15;;7610:32;7715:2;7700:18;;;7687:32;7735:15;;;7728:32;7812:2;7797:18;;7784:32;7825:33;7784:32;7825:33;:::i;:::-;7886:2;7874:15;;7867:32;7972:3;7957:19;;;7944:33;7993:16;;7986:33;;;;7003:1022;;-1:-1:-1;7003:1022:142:o;8030:519::-;8140:6;8148;8156;8209:3;8197:9;8188:7;8184:23;8180:33;8177:53;;;8226:1;8223;8216:12;8177:53;8265:9;8252:23;8284:31;8309:5;8284:31;:::i;:::-;8334:5;-1:-1:-1;8391:2:142;8376:18;;8363:32;8404:33;8363:32;8404:33;:::i;:::-;8456:7;-1:-1:-1;8482:61:142;8535:7;8530:2;8515:18;;8482:61;:::i;:::-;8472:71;;8030:519;;;;;:::o;8554:220::-;8703:2;8692:9;8685:21;8666:4;8723:45;8764:2;8753:9;8749:18;8741:6;8723:45;:::i;8779:378::-;8880:6;8888;8941:3;8929:9;8920:7;8916:23;8912:33;8909:53;;;8958:1;8955;8948:12;8909:53;8997:9;8984:23;9016:31;9041:5;9016:31;:::i;:::-;9066:5;-1:-1:-1;9090:61:142;9143:7;9138:2;9123:18;;9090:61;:::i;:::-;9080:71;;8779:378;;;;;:::o;9162:388::-;9230:6;9238;9291:2;9279:9;9270:7;9266:23;9262:32;9259:52;;;9307:1;9304;9297:12;9259:52;9346:9;9333:23;9365:31;9390:5;9365:31;:::i;:::-;9415:5;-1:-1:-1;9472:2:142;9457:18;;9444:32;9485:33;9444:32;9485:33;:::i;9555:118::-;9641:5;9634:13;9627:21;9620:5;9617:32;9607:60;;9663:1;9660;9653:12;9678:623;9761:6;9769;9777;9785;9838:3;9826:9;9817:7;9813:23;9809:33;9806:53;;;9855:1;9852;9845:12;9806:53;9894:9;9881:23;9913:31;9938:5;9913:31;:::i;:::-;9963:5;-1:-1:-1;10020:2:142;10005:18;;9992:32;10033:30;9992:32;10033:30;:::i;:::-;9678:623;;10082:7;;-1:-1:-1;;;;10162:2:142;10147:18;;10134:32;;10265:2;10250:18;10237:32;;9678:623::o;10306:545::-;10399:4;10405:6;10465:11;10452:25;10559:2;10555:7;10544:8;10528:14;10524:29;10520:43;10500:18;10496:68;10486:96;;10578:1;10575;10568:12;10486:96;10605:33;;10657:20;;;-1:-1:-1;;;;;;10689:30:142;;10686:50;;;10732:1;10729;10722:12;10686:50;10765:4;10753:17;;-1:-1:-1;10816:1:142;10812:14;;;10796;10792:35;10782:46;;10779:66;;;10841:1;10838;10831:12;10779:66;10306:545;;;;;:::o;10856:127::-;10917:10;10912:3;10908:20;10905:1;10898:31;10948:4;10945:1;10938:15;10972:4;10969:1;10962:15;11202:668;11282:6;11335:2;11323:9;11314:7;11310:23;11306:32;11303:52;;;11351:1;11348;11341:12;11303:52;11384:9;11378:16;-1:-1:-1;;;;;11409:6:142;11406:30;11403:50;;;11449:1;11446;11439:12;11403:50;11472:22;;11525:4;11517:13;;11513:27;-1:-1:-1;11503:55:142;;11554:1;11551;11544:12;11503:55;11587:2;11581:9;11612:52;11628:35;11656:6;11628:35;:::i;11612:52::-;11687:6;11680:5;11673:21;11735:7;11730:2;11721:6;11717:2;11713:15;11709:24;11706:37;11703:57;;;11756:1;11753;11746:12;11703:57;11769:71;11833:6;11828:2;11821:5;11817:14;11812:2;11808;11804:11;11769:71;:::i;11875:184::-;11945:6;11998:2;11986:9;11977:7;11973:23;11969:32;11966:52;;;12014:1;12011;12004:12;11966:52;-1:-1:-1;12037:16:142;;11875:184;-1:-1:-1;11875:184:142:o;12274:127::-;12335:10;12330:3;12326:20;12323:1;12316:31;12366:4;12363:1;12356:15;12390:4;12387:1;12380:15;12406:128;12473:9;;;12494:11;;;12491:37;;;12508:18;;:::i;12539:135::-;12578:3;12599:17;;;12596:43;;12619:18;;:::i;:::-;-1:-1:-1;12666:1:142;12655:13;;12539:135::o;12679:325::-;12881:2;12863:21;;;12920:1;12900:18;;;12893:29;-1:-1:-1;;;12953:2:142;12938:18;;12931:32;12995:2;12980:18;;12679:325::o;13009:125::-;13074:9;;;13095:10;;;13092:36;;;13108:18;;:::i;15124:245::-;15191:6;15244:2;15232:9;15223:7;15219:23;15215:32;15212:52;;;15260:1;15257;15250:12;15212:52;15292:9;15286:16;15311:28;15333:5;15311:28;:::i;15374:127::-;15435:10;15430:3;15426:20;15423:1;15416:31;15466:4;15463:1;15456:15;15490:4;15487:1;15480:15;15506:287;15635:3;15673:6;15667:13;15689:66;15748:6;15743:3;15736:4;15728:6;15724:17;15689:66;:::i;:::-;15771:16;;;;;15506:287;-1:-1:-1;;15506:287:142:o;15798:168::-;15871:9;;;15902;;15919:15;;;15913:22;;15899:37;15889:71;;15940:18;;:::i", "linkReferences": {}, "immutableReferences": {"8075": [{"start": 7354, "length": 32}, {"start": 7395, "length": 32}, {"start": 7716, "length": 32}]}}, "methodIdentifiers": {"DEFAULT_ADMIN_ROLE()": "a217fddf", "FAUCET_ROLE()": "45c7c793", "MAINTAINER_ROLE()": "f8742254", "NATIVE_TOKEN_POINTER()": "20b2cf9f", "PAUSER_ROLE()": "e63ab1e9", "SWAPPER_ROLE()": "df668eca", "UPGRADE_INTERFACE_VERSION()": "ad3cb1cc", "UpdateXYZ(address,bool,uint256,uint256)": "e9aea396", "__StablecoinHandler_init()": "924855fa", "__StablecoinHandler_init_unchained()": "3598e3dc", "convertFromEXYZ(address,address,(address,address,uint256,address,uint256))": "9ef84173", "convertToEXYZ(address,address,(address,address,uint256,address,uint256))": "8e13c58e", "drip(address,address)": "eb3839a7", "getDripAmount()": "817204a4", "getEnabledXYZs()": "69e2fda4", "getEnabledXYZsWithMetadata()": "1a0d6c2e", "getLastFulfilledDripTimestamp(address,address)": "c5b059e7", "getMaxLimit(address)": "4f9c2c26", "getMinLimit(address)": "956794b1", "getNativeDripAmount()": "7ad73baf", "getRoleAdmin(bytes32)": "248a9ca3", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "initialize((address,address,address[],uint256,uint256,address[],uint256,uint256))": "16ada6b1", "isEnabledXYZ(address)": "b4fc1335", "isXYZ(address)": "1a95e05d", "pause()": "8456cb59", "paused()": "5c975abb", "proxiableUUID()": "52d1902d", "renounceRole(bytes32,address)": "36568abe", "rescueCrypto(address,uint256)": "40c66f78", "revokeRole(bytes32,address)": "d547741f", "setDripAmount(uint256)": "543f8c58", "setLowBalanceThreshold(uint256)": "a0eba21a", "setNativeDripAmount(uint256)": "959042a2", "supportsInterface(bytes4)": "01ffc9a7", "swapAndSend(address,(address,address,uint256,address,uint256))": "c057bd6b", "unpause()": "3f4ba83a", "upgradeToAndCall(address,bytes)": "4f1ef286"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"AccessControlBadConfirmation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"neededRole\",\"type\":\"bytes32\"}],\"name\":\"AccessControlUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"AddressInsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"AlreadyEnabled\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"ERC1967InvalidImplementation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ERC1967NonPayable\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"EnforcedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ExpectedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedInnerCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"dripAmount\",\"type\":\"uint256\"}],\"name\":\"InvalidDripAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"InvalidMintBurnBoundry\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"InvalidOrDisabled\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LowLevelCallFailure\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"unixTimestamp\",\"type\":\"uint256\"}],\"name\":\"RequestIneligibleUntil\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UUPSUnauthorizedCallContext\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"slot\",\"type\":\"bytes32\"}],\"name\":\"UUPSUnsupportedProxiableUUID\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"value\",\"type\":\"string\"}],\"name\":\"ZeroValueInput\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Drip\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newDripAmount\",\"type\":\"uint256\"}],\"name\":\"DripAmountUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"FaucetLowNativeBalance\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newNativeDripAmount\",\"type\":\"uint256\"}],\"name\":\"NativeDripAmountUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"XYZAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"XYZRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"validity\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"max\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"min\",\"type\":\"uint256\"}],\"name\":\"XYZUpdated\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DEFAULT_ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"FAUCET_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAINTAINER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"NATIVE_TOKEN_POINTER\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"PAUSER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SWAPPER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"UPGRADE_INTERFACE_VERSION\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"validity\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"maxLimit\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minLimit\",\"type\":\"uint256\"}],\"name\":\"UpdateXYZ\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"__StablecoinHandler_init\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"__StablecoinHandler_init_unchained\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"safe\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"destination\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"origin\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"oAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tAmount\",\"type\":\"uint256\"}],\"internalType\":\"struct StablecoinHandler.StablecoinSwap\",\"name\":\"ss\",\"type\":\"tuple\"}],\"name\":\"convertFromEXYZ\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"safe\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"destination\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"origin\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"oAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tAmount\",\"type\":\"uint256\"}],\"internalType\":\"struct StablecoinHandler.StablecoinSwap\",\"name\":\"ss\",\"type\":\"tuple\"}],\"name\":\"convertToEXYZ\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"}],\"name\":\"drip\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getDripAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"dripAmount\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getEnabledXYZs\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"enabledXYZs\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getEnabledXYZsWithMetadata\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"internalType\":\"struct StablecoinManager.XYZMetadata[]\",\"name\":\"enabledXYZMetadatas\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"}],\"name\":\"getLastFulfilledDripTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"getMaxLimit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"getMinLimit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getNativeDripAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"nativeDripAmount\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"admin_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"maintainer_\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"tokens_\",\"type\":\"address[]\"},{\"internalType\":\"uint256\",\"name\":\"initMaxLimit\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"initMinLimit\",\"type\":\"uint256\"},{\"internalType\":\"address[]\",\"name\":\"authorizedFaucets_\",\"type\":\"address[]\"},{\"internalType\":\"uint256\",\"name\":\"dripAmount_\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nativeDripAmount_\",\"type\":\"uint256\"}],\"internalType\":\"struct StablecoinManager.StablecoinManagerInitParams\",\"name\":\"initParams\",\"type\":\"tuple\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"eXYZ\",\"type\":\"address\"}],\"name\":\"isEnabledXYZ\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"isEnabled\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"isXYZ\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proxiableUUID\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"callerConfirmation\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IERC20\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"rescueCrypto\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newDripAmount\",\"type\":\"uint256\"}],\"name\":\"setDripAmount\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newThreshold\",\"type\":\"uint256\"}],\"name\":\"setLowBalanceThreshold\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newNativeDripAmount\",\"type\":\"uint256\"}],\"name\":\"setNativeDripAmount\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"wallet\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"destination\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"origin\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"oAmount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tAmount\",\"type\":\"uint256\"}],\"internalType\":\"struct StablecoinHandler.StablecoinSwap\",\"name\":\"ss\",\"type\":\"tuple\"}],\"name\":\"swapAndSend\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unpause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"upgradeToAndCall\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"author\":\"Robriks \\ud83d\\udcef\\ufe0f\\ud83d\\udcef\\ufe0f\\ud83d\\udcef\\ufe0f.eth\",\"errors\":{\"AccessControlBadConfirmation()\":[{\"details\":\"The caller of a function is not the expected one. NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\"}],\"AccessControlUnauthorizedAccount(address,bytes32)\":[{\"details\":\"The `account` is missing a role.\"}],\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"AddressInsufficientBalance(address)\":[{\"details\":\"The ETH balance of the account is not enough to perform the operation.\"}],\"ERC1967InvalidImplementation(address)\":[{\"details\":\"The `implementation` of the proxy is invalid.\"}],\"ERC1967NonPayable()\":[{\"details\":\"An upgrade function sees `msg.value > 0` that may be lost.\"}],\"EnforcedPause()\":[{\"details\":\"The operation failed because the contract is paused.\"}],\"ExpectedPause()\":[{\"details\":\"The operation failed because the contract is not paused.\"}],\"FailedInnerCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC20 token failed.\"}],\"UUPSUnauthorizedCallContext()\":[{\"details\":\"The call is from an unauthorized context.\"}],\"UUPSUnsupportedProxiableUUID(bytes32)\":[{\"details\":\"The storage `slot` is unsupported as a UUID.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"Paused(address)\":{\"details\":\"Emitted when the pause is triggered by `account`.\"},\"RoleAdminChanged(bytes32,bytes32,bytes32)\":{\"details\":\"Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole` `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite {RoleAdminChanged} not being emitted signaling this.\"},\"RoleGranted(bytes32,address,address)\":{\"details\":\"Emitted when `account` is granted `role`. `sender` is the account that originated the contract call, an admin role bearer except when using {AccessControl-_setupRole}.\"},\"RoleRevoked(bytes32,address,address)\":{\"details\":\"Emitted when `account` is revoked `role`. `sender` is the account that originated the contract call:   - if using `revokeRole`, it is the admin role bearer   - if using `renounceRole`, it is the role bearer (i.e. `account`)\"},\"Unpaused(address)\":{\"details\":\"Emitted when the pause is lifted by `account`.\"},\"Upgraded(address)\":{\"details\":\"Emitted when the implementation is upgraded.\"}},\"kind\":\"dev\",\"methods\":{\"UpdateXYZ(address,bool,uint256,uint256)\":{\"details\":\"Modifies the validity status and supply limits of the specified token. Can only be executed by addresses with the MAINTAINER_ROLE. This method is crucial for maintaining the operational parameters of external XYZ tokens within the system.\",\"params\":{\"maxLimit\":\"The maximum supply limit for the token.\",\"minLimit\":\"The minimum supply limit for the token. Emits an `XYZUpdated` event upon successfully updating the token's parameters.\",\"token\":\"The address of the external XYZ token to update.\",\"validity\":\"A boolean indicating whether the token should be considered valid.\"}},\"convertFromEXYZ(address,address,(address,address,uint256,address,uint256))\":{\"details\":\"Operates within the constraints of roles and the contract's paused state, facilitating the conversion process.\",\"params\":{\"safe\":\"The safe address from which target tokens will be sent.\",\"ss\":\"The details of the stablecoin swap operation.\",\"wallet\":\"The wallet address from which tokens will be burned.\"}},\"convertToEXYZ(address,address,(address,address,uint256,address,uint256))\":{\"details\":\"Ensures the operation is performed according to the roles and pause state, transferring from a wallet to a safe address.\",\"params\":{\"safe\":\"The safe address to receive the origin tokens.\",\"ss\":\"The stablecoin swap details.\",\"wallet\":\"The wallet address from which tokens will be transferred.\"}},\"drip(address,address)\":{\"details\":\"Faucet function defining this contract as the onchain entrypoint for minting testnet tokens to usersTo mint the chain's native token, use `NATIVE_TOKEN_POINTER == address(0x0)`\"},\"getEnabledXYZs()\":{\"details\":\"Fetches all currently valid stablecoin addresses\"},\"getEnabledXYZsWithMetadata()\":{\"details\":\"Fetches all currently valid stablecoins with metadata for dynamic rendering by a frontend\"},\"getLastFulfilledDripTimestamp(address,address)\":{\"details\":\"Exposes the timestamp of the last fulfilled faucet drip for a given `token` and `recipient`\"},\"getMaxLimit(address)\":{\"details\":\"Reads the maximum supply limit set for the token from the contract's storage. This function provides visibility into the operational constraints of external XYZ tokens, specifically the upper bound of the token's supply within the system.\",\"params\":{\"token\":\"The address of the external XYZ token whose maximum supply limit is being queried.\"},\"returns\":{\"_0\":\"uint256 The maximum supply limit for the specified token. This value represents the upper limit on the total supply of the token that can be managed by the contract.\"}},\"getMinLimit(address)\":{\"details\":\"Reads the minimum supply limit set for the token from the contract's storage. This function is essential for understanding the operational constraints of external XYZ tokens, highlighting the lower bound of the token's supply that is considered acceptable within the system.\",\"params\":{\"token\":\"The address of the external XYZ token whose minimum supply limit is being queried.\"},\"returns\":{\"_0\":\"uint256 The minimum supply limit for the specified token. This value indicates the minimum amount of the token that should be maintained or is allowable within the contract's management scope.\"}},\"getRoleAdmin(bytes32)\":{\"details\":\"Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}.\"},\"grantRole(bytes32,address)\":{\"details\":\"Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event.\"},\"hasRole(bytes32,address)\":{\"details\":\"Returns `true` if `account` has been granted `role`.\"},\"initialize((address,address,address[],uint256,uint256,address[],uint256,uint256))\":{\"details\":\"Invokes `__Pausable_init()`\"},\"isXYZ(address)\":{\"details\":\"Reads from the contract's storage to determine the validity of the token address.\",\"params\":{\"token\":\"The address of the token to check.\"},\"returns\":{\"_0\":\"bool True if the token is a valid external XYZ token, false otherwise.\"}},\"pause()\":{\"details\":\"Can only be called by addresses with the PAUSER_ROLE, halting certain functionalities.\"},\"paused()\":{\"details\":\"Returns true if the contract is paused, and false otherwise.\"},\"proxiableUUID()\":{\"details\":\"Implementation of the ERC1822 {proxiableUUID} function. This returns the storage slot used by the implementation. It is used to validate the implementation's compatibility when performing an upgrade. IMPORTANT: A proxy pointing at a proxiable contract should not be considered proxiable itself, because this risks bricking a proxy that upgrades to it, by delegating to itself until out of gas. Thus it is critical that this function revert if invoked through a proxy. This is guaranteed by the `notDelegated` modifier.\"},\"renounceRole(bytes32,address)\":{\"details\":\"Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event.\"},\"rescueCrypto(address,uint256)\":{\"details\":\"Allows for the recovery of both ERC20 tokens and native token sent to the contract.\",\"params\":{\"amount\":\"The amount of the token to rescue.\",\"token\":\"The token to rescue. Use `address(0x0)` for native token.\"}},\"revokeRole(bytes32,address)\":{\"details\":\"Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event.\"},\"setDripAmount(uint256)\":{\"details\":\"Provides a way for Telcoin maintainers to alter the faucet's eXYZ drip amount onchain\"},\"setLowBalanceThreshold(uint256)\":{\"details\":\"Provides a way for Telcoin maintainers to configure the faucet's threshold for top-up alerts\"},\"setNativeDripAmount(uint256)\":{\"details\":\"Provides a way for Telcoin maintainers to alter the faucet's native token drip amount onchain\"},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"},\"swapAndSend(address,(address,address,uint256,address,uint256))\":{\"details\":\"Only callable by addresses with the SWAPPER_ROLE and when the contract is not paused.\",\"params\":{\"ss\":\"The stablecoin swap details, including source, target, and amounts.\",\"wallet\":\"The wallet address from which tokens will be burned.\"}},\"unpause()\":{\"details\":\"Only callable by addresses with the PAUSER_ROLE, reenabling functionalities halted by pausing.\"},\"upgradeToAndCall(address,bytes)\":{\"custom:oz-upgrades-unsafe-allow-reachable\":\"delegatecall\",\"details\":\"Upgrade the implementation of the proxy to `newImplementation`, and subsequently execute the function call encoded in `data`. Calls {_authorizeUpgrade}. Emits an {Upgraded} event.\"}},\"title\":\"StablecoinManager\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"UpdateXYZ(address,bool,uint256,uint256)\":{\"notice\":\"Updates the configuration for an external XYZ token.\"},\"convertFromEXYZ(address,address,(address,address,uint256,address,uint256))\":{\"notice\":\"Converts from an external XYZ token to another asset as specified.\"},\"convertToEXYZ(address,address,(address,address,uint256,address,uint256))\":{\"notice\":\"Converts assets to an external XYZ token with specified parameters.\"},\"drip(address,address)\":{\"notice\":\"This contract must be given `Stablecoin::MINTER_ROLE` on each eXYZ contractImplements Access Control, requiring callers to possess the `FAUCET_ROLE`\"},\"getDripAmount()\":{\"notice\":\"Agnostic to enabled/disabled status for data availability\"},\"getEnabledXYZs()\":{\"notice\":\"Excludes `NATIVE_TOKEN_POINTER` if it is enabled\"},\"getEnabledXYZsWithMetadata()\":{\"notice\":\"Intended for use in a view context to save on RPC calls\"},\"getMaxLimit(address)\":{\"notice\":\"Retrieves the maximum supply limit for a specified external XYZ token.\"},\"getMinLimit(address)\":{\"notice\":\"Retrieves the minimum supply limit for a specified external XYZ token.\"},\"getNativeDripAmount()\":{\"notice\":\"Agnostic to enabled/disabled status for data availability\"},\"isEnabledXYZ(address)\":{\"notice\":\"To identify if faucet has the native token enabled, pass in `address(0x0)`\"},\"isXYZ(address)\":{\"notice\":\"Checks if a given token address is recognized as a valid external XYZ token.\"},\"pause()\":{\"notice\":\"Pauses all pause-sensitive operations within the contract.\"},\"rescueCrypto(address,uint256)\":{\"notice\":\"Rescues crypto assets mistakenly sent to the contract.\"},\"setDripAmount(uint256)\":{\"notice\":\"Rather than set `dripAmount` to 0, disable the token\"},\"setNativeDripAmount(uint256)\":{\"notice\":\"Rather than set `nativeDripAmount` to 0, disable the token\"},\"swapAndSend(address,(address,address,uint256,address,uint256))\":{\"notice\":\"Swaps and sends stablecoins according to specified parameters, enforcing role and pause state.\"},\"unpause()\":{\"notice\":\"Unpauses the contract, allowing previously paused operations to resume.\"}},\"notice\":\"A Telcoin ContractThis contract extends the StablecoinHandler which manages the minting and burning of stablecoins\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/StablecoinManager.sol\":\"StablecoinManager\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@axelar-cgp-solidity/=node_modules/@axelar-network/axelar-cgp-solidity/\",\":@axelar-network/=node_modules/@axelar-network/\",\":@openzeppelin-contracts/=node_modules/recoverable-wrapper/node_modules/@openzeppelin/contracts/\",\":@openzeppelin/=node_modules/@openzeppelin/\",\":@uniswap/=node_modules/@uniswap/\",\":ds-test/=node_modules/recoverable-wrapper/lib/forge-std/lib/ds-test/src/\",\":forge-std/=node_modules/forge-std/src/\",\":recoverable-wrapper/contracts/=node_modules/recoverable-wrapper/contracts/\",\":solady/=node_modules/solady/src/\",\":telcoin-contracts/=node_modules/telcoin-contracts/\"]},\"sources\":{\"external/telcoin-contracts/interfaces/IStablecoin.sol\":{\"keccak256\":\"0xe6bc07e357c669be88e2f83316b7135712ff11132758ba190fc7cda9e1b87abc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a71ed63fd8d6f2188ed37af7c4c7be30bc602c59b03bf6fd9e402af37a56bec2\",\"dweb:/ipfs/QmPtEGzLgREdATYHtVCWwWQvLWkSB5BFM1P9paFpDjksi8\"]},\"node_modules/@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol\":{\"keccak256\":\"0x6662ec4e5cefca03eeadd073e9469df8d2944bb2ee8ec8f7622c2c46aab5f225\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4d8544c6f8daa4d1bc215c6a72fe0acdb748664a105b0e5efc19295667521d45\",\"dweb:/ipfs/QmdGWqdnXT8S3RgCR6aV8XHZrsybieMQLLnug1NtpSjEXN\"]},\"node_modules/@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609\",\"dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM\"]},\"node_modules/@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol\":{\"keccak256\":\"0x9a1766b1921bf91b3e61eb53c7a6e70725254befd4bdcbbcd3af40bd9f66856f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://29bf2fa41a172086a665c9738377b93655aa4b1ffda9fe839c8bdf646f185040\",\"dweb:/ipfs/QmeB21qDuo8WPQSrqXJbQmWHKsdeocGNSUWLhCwniVejrt\"]},\"node_modules/@openzeppelin/contracts-upgradeable/token/ERC20/extensions/ERC20PermitUpgradeable.sol\":{\"keccak256\":\"0x8a97653aeba40e9f0c2e8df1a1379b29b927b6dc3534040c668e71ad9ae89d88\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6e529c294c9d634eb68a1e4aeb66eb8381de5a08ccd2c0bfeebd48a6b28fcff7\",\"dweb:/ipfs/QmWCezuxfZb68nM3Hs6XzQNNiW7VJsymU4sajy2DW1CKbp\"]},\"node_modules/@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"node_modules/@openzeppelin/contracts-upgradeable/utils/NoncesUpgradeable.sol\":{\"keccak256\":\"0x778f4a1546a1c6c726ecc8e2348a2789690fb8f26e12bd9d89537669167b79a4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://851d3dfe724e918ff0a064b206e1ef46b27ab0df2aa2c8af976973a22ef59827\",\"dweb:/ipfs/Qmd4wb7zX8ueYhMVBy5PJjfsANK3Ra3pKPN7qQkNsdwGHn\"]},\"node_modules/@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol\":{\"keccak256\":\"0x92915b7f7f642c6be3f65bfd1522feb5d5b6ef25f755f4dbb51df32c868f2f97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://85ad36d5cc7e190e1ee6c94b24659bc3a31396c4c36b6ffa6a509e10661f8007\",\"dweb:/ipfs/QmPFyc4zMh2zo6YWZt25gjm3YdR2hg6wGETaWw256fMmJJ\"]},\"node_modules/@openzeppelin/contracts-upgradeable/utils/cryptography/EIP712Upgradeable.sol\":{\"keccak256\":\"0x85462422a22578744581e012e9aa0a391958cb360288b0b63f29bf0431d70327\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2bc529e2b9b28da5d26da451058250d85afcaa3c5083ee273ac68fa6bf956b78\",\"dweb:/ipfs/Qmd3Aq59ztmoVmHigsaR4YjkXWKERVpjfQ4a2PHk7Ke6Rx\"]},\"node_modules/@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol\":{\"keccak256\":\"0xdaba3f7c42c55b2896353f32bd27d4d5f8bae741b3b05d4c53f67abc4dc47ce8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1fa2e61141c602510bcd2cd936ed9561922ac8772a9b9c9a9db091a74e354a45\",\"dweb:/ipfs/QmcHQDDoEBwJmwUbzoVkytvJsBx3KVHYFFnDkvRGWh9Wmh\"]},\"node_modules/@openzeppelin/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0xb6b36edd6a2999fd243ff226d6cbf84bd71af2432bbd0dfe19392996a1d9cb41\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1fd2f35495652e57e3f99bc6c510bc5f7dd398a176ea2e72d8ed730aebc6ca26\",\"dweb:/ipfs/QmTQV6X4gkikTib49cho5iDX3JvSQbdsoEChoDwrk3CbbH\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0x92aa1df62dc3d33f1656d63bede0923e0df0b706ad4137c8b10b0a8fe549fd92\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c5c0f29195ad64cbe556da8e257dac8f05f78c53f90323c0d2accf8e6922d33a\",\"dweb:/ipfs/QmQ61TED8uaCZwcbh8KkgRSsCav7x7HbcGHwHts3U4DmUP\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC1822.sol\":{\"keccak256\":\"0x2a1f9944df2015c081d89cd41ba22ffaf10aa6285969f0dc612b235cc448999c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef381843676aec64421200ee85eaa0b1356a35f28b9fc67e746a6bbb832077d9\",\"dweb:/ipfs/QmY8aorMYA2TeTCnu6ejDjzb4rW4t7TCtW4GZ6LoxTFm7v\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x60c65f701957fdd6faea1acb0bb45825791d473693ed9ecb34726fdfaa849dd7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ea290300e0efc4d901244949dc4d877fd46e6c5e43dc2b26620e8efab3ab803f\",\"dweb:/ipfs/QmcLLJppxKeJWqHxE2CUkcfhuRTgHSn8J4kijcLa5MYhSt\"]},\"node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x06a78f9b3ee3e6d0eb4e4cd635ba49960bea34cac1db8c0a27c75f2319f1fd65\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://547d21aa17f4f3f1a1a7edf7167beff8dd9496a0348d5588f15cc8a4b29d052a\",\"dweb:/ipfs/QmT16JtRQSWNpLo9W23jr6CzaMuTAcQcjJJcdRd8HLJ6cE\"]},\"node_modules/@openzeppelin/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"node_modules/@openzeppelin/contracts/proxy/utils/UUPSUpgradeable.sol\":{\"keccak256\":\"0x3ffb56bcb175984a10b1167e2eba560876bfe96a435f5d62ffed8b1bb4ebc4c7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7db94af56aa20efb57c3f9003eacd884faad04118967d8e35cdffe07790bbdcd\",\"dweb:/ipfs/QmXtAshRWFjcQ1kL7gpC5CiLUZgJ9uzrZyeHp2Sux9ojPF\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xaa761817f6cd7892fcf158b3c776b34551cde36f48ff9703d53898bc45a94ea2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ad7c8d4d08938c8dfc43d75a148863fb324b80cf53e0a36f7e5a4ac29008850\",\"dweb:/ipfs/QmcrhfPgVNf5mkdhQvy1pMv51TFokD3Y4Wa5WZhFqVh8UV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d\",\"dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0\",\"dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3\"]},\"node_modules/@openzeppelin/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"node_modules/@openzeppelin/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0x32ba59b4b7299237c8ba56319110989d7978a039faf754793064e967e5894418\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1ae50c8b562427df610cc4540c9bf104acca7ef8e2dcae567ae7e52272281e9c\",\"dweb:/ipfs/QmTHiadFCSJUPpRjNegc5SahmeU8bAoY8i9Aq6tVscbcKR\"]},\"node_modules/@openzeppelin/contracts/utils/Strings.sol\":{\"keccak256\":\"0x55f102ea785d8399c0e58d1108e2d289506dde18abc6db1b7f68c1f9f9bc5792\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6e52e0a7765c943ef14e5bcf11e46e6139fa044be564881378349236bf2e3453\",\"dweb:/ipfs/QmZEeeXoFPW47amyP35gfzomF9DixqqTEPwzBakv6cZw6i\"]},\"node_modules/@openzeppelin/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0xeed0a08b0b091f528356cbc7245891a4c748682d4f6a18055e8e6ca77d12a6cf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba80ba06c8e6be852847e4c5f4492cef801feb6558ae09ed705ff2e04ea8b13c\",\"dweb:/ipfs/QmXRJDv3xHLVQCVXg1ZvR35QS9sij5y9NDWYzMfUfAdTHF\"]},\"node_modules/@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0xba333517a3add42cd35fe877656fc3dfcc9de53baa4f3aabbd6d12a92e4ea435\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ceacff44c0fdc81e48e0e0b1db87a2076d3c1fb497341de077bf1da9f6b406c\",\"dweb:/ipfs/QmRUo1muMRAewxrKQ7TkXUtknyRoR57AyEkoPpiuZQ8FzX\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4296879f55019b23e135000eb36896057e7101fb7fb859c5ef690cf14643757b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://87b3541437c8c443ccd36795e56a338ed12855eec17f8da624511b8d1a7e14df\",\"dweb:/ipfs/QmeJQCtZrQjtJLr6u7ZHWeH3pBnjtLWzvRrKViAi7UZqxL\"]},\"node_modules/@openzeppelin/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x005ec64c6313f0555d59e278f9a7a5ab2db5bdc72a027f255a37c327af1ec02d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4ece9f0b9c8daca08c76b6b5405a6446b6f73b3a15fab7ff56e296cbd4a2c875\",\"dweb:/ipfs/QmQyRpyPRL5SQuAgj6SHmbir3foX65FJjbVTTQrA2EFg6L\"]},\"node_modules/@openzeppelin/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0x5f7e4076e175393767754387c962926577f1660dd9b810187b9002407656be72\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7d533a1c97cd43a57cd9c465f7ee8dd0e39ae93a8fb8ff8e5303a356b081cdcc\",\"dweb:/ipfs/QmVBEei6aTnvYNZp2CHYVNKyZS4q1KkjANfY39WVXZXVoT\"]},\"node_modules/telcoin-contracts/contracts/stablecoin/Stablecoin.sol\":{\"keccak256\":\"0x4782fd54d946422bbb45ddf6a13f73d8f7cd4843946685462468ef761307757d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e1f817e0b88fcbb814ebd68ef31bf700635df39de812203d9ae8e2b28f96c266\",\"dweb:/ipfs/QmZAm6Srd1ej3hnZmJMeSuvqSMDHrxETq8oXcBBSCN1s9M\"]},\"node_modules/telcoin-contracts/contracts/stablecoin/StablecoinHandler.sol\":{\"keccak256\":\"0xac513311ece00215b80000e2f41c728686c65d5ed2cf5a71e1c975d186938397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://472761a9129440a6a49773e55f13c656a24eab6eb8c356257c63a35a074b7cad\",\"dweb:/ipfs/QmNZJGt2jPfui2DyyQqp28WvZvwiKS2jaBqFdRcGTM1gBP\"]},\"node_modules/telcoin-contracts/contracts/util/abstract/Blacklist.sol\":{\"keccak256\":\"0xb8b4825ef9c29eb37cdaf5855e749f40e9b63d8ed746ca80a58585e70efca80f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f8d5e75f35e91f1c53159c41d00bd50c34e5e54fe15b37fa4756ebd576d6fa6c\",\"dweb:/ipfs/QmaUvEy7yT8hVsXPc3WhJey1zaB5y3n5dq9TeTuNugZm7D\"]},\"src/StablecoinManager.sol\":{\"keccak256\":\"0x0f70a9e0efe4abada9ea1f54bb14c14447d572158bbb11e643c13618a0bfed21\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://62759753c605a920dbd861ca28610dc2abf169deba647a672be772b8f0689201\",\"dweb:/ipfs/QmRNEyTnMYpVH14JKdAzCfZgr2tU1k8cP8p7CYZzhbPpCM\"]},\"src/faucet/TNFaucet.sol\":{\"keccak256\":\"0x0c0bab367410b4f28c9d5636f12ec2e13e3064aae63e9c1962fc4cf5569d8a4a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://fdbe7de5a4cf6e55efb34c07877f4cecb4acda9e9e93b407e08d80443fde27d2\",\"dweb:/ipfs/QmWBoP18RxCvxzYaxgUYApo1sWHfduboMjqFgBzyA4wyhU\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "AccessControlBadConfirmation"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "type": "error", "name": "AccessControlUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "AddressInsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "AlreadyEnabled"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "type": "error", "name": "ERC1967InvalidImplementation"}, {"inputs": [], "type": "error", "name": "ERC1967Non<PERSON>ayable"}, {"inputs": [], "type": "error", "name": "EnforcedPause"}, {"inputs": [], "type": "error", "name": "ExpectedPause"}, {"inputs": [], "type": "error", "name": "FailedInnerCall"}, {"inputs": [{"internalType": "uint256", "name": "dripAmount", "type": "uint256"}], "type": "error", "name": "InvalidDripAmount"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "InvalidMintBurnBoundry"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "InvalidOrDisabled"}, {"inputs": [], "type": "error", "name": "LowLevelCallFailure"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [{"internalType": "uint256", "name": "unixTimestamp", "type": "uint256"}], "type": "error", "name": "RequestIneligibleUntil"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [], "type": "error", "name": "UUPSUnauthorizedCallContext"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "type": "error", "name": "UUPSUnsupportedProxiableUUID"}, {"inputs": [{"internalType": "string", "name": "value", "type": "string"}], "type": "error", "name": "ZeroValueInput"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": false}, {"internalType": "address", "name": "recipient", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "newDripAmount", "type": "uint256", "indexed": false}], "type": "event", "name": "DripAmountUpdated", "anonymous": false}, {"inputs": [], "type": "event", "name": "FaucetLowNativeBalance", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "newNativeDripAmount", "type": "uint256", "indexed": false}], "type": "event", "name": "NativeDripAmountUpdated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}], "type": "event", "name": "Paused", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "newAdminRole", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleAdminChanged", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleGranted", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleRevoked", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}], "type": "event", "name": "Unpaused", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": true}], "type": "event", "name": "Upgraded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": false}], "type": "event", "name": "XYZAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": false}], "type": "event", "name": "XYZRemoved", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "token", "type": "address", "indexed": false}, {"internalType": "bool", "name": "validity", "type": "bool", "indexed": false}, {"internalType": "uint256", "name": "max", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "min", "type": "uint256", "indexed": false}], "type": "event", "name": "XYZUpdated", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "FAUCET_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MAINTAINER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "NATIVE_TOKEN_POINTER", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "PAUSER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SWAPPER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "bool", "name": "validity", "type": "bool"}, {"internalType": "uint256", "name": "maxLimit", "type": "uint256"}, {"internalType": "uint256", "name": "minLimit", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "UpdateXYZ"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "__StablecoinHandler_init"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "__StablecoinHandler_init_unchained"}, {"inputs": [{"internalType": "address", "name": "wallet", "type": "address"}, {"internalType": "address", "name": "safe", "type": "address"}, {"internalType": "struct StablecoinHandler.StablecoinSwap", "name": "ss", "type": "tuple", "components": [{"internalType": "address", "name": "destination", "type": "address"}, {"internalType": "address", "name": "origin", "type": "address"}, {"internalType": "uint256", "name": "oAmount", "type": "uint256"}, {"internalType": "address", "name": "target", "type": "address"}, {"internalType": "uint256", "name": "tAmount", "type": "uint256"}]}], "stateMutability": "nonpayable", "type": "function", "name": "convertFromEXYZ"}, {"inputs": [{"internalType": "address", "name": "wallet", "type": "address"}, {"internalType": "address", "name": "safe", "type": "address"}, {"internalType": "struct StablecoinHandler.StablecoinSwap", "name": "ss", "type": "tuple", "components": [{"internalType": "address", "name": "destination", "type": "address"}, {"internalType": "address", "name": "origin", "type": "address"}, {"internalType": "uint256", "name": "oAmount", "type": "uint256"}, {"internalType": "address", "name": "target", "type": "address"}, {"internalType": "uint256", "name": "tAmount", "type": "uint256"}]}], "stateMutability": "nonpayable", "type": "function", "name": "convertToEXYZ"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "drip"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getDripAmount", "outputs": [{"internalType": "uint256", "name": "dripAmount", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getEnabledXYZs", "outputs": [{"internalType": "address[]", "name": "enabledXYZs", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getEnabledXYZsWithMetadata", "outputs": [{"internalType": "struct StablecoinManager.XYZMetadata[]", "name": "enabledXYZMetadatas", "type": "tuple[]", "components": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}]}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getLastFulfilledDripTimestamp", "outputs": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getMaxLimit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getMinLimit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getNativeDripAmount", "outputs": [{"internalType": "uint256", "name": "nativeDripAmount", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "grantRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "struct StablecoinManager.StablecoinManagerInitParams", "name": "initParams", "type": "tuple", "components": [{"internalType": "address", "name": "admin_", "type": "address"}, {"internalType": "address", "name": "maintainer_", "type": "address"}, {"internalType": "address[]", "name": "tokens_", "type": "address[]"}, {"internalType": "uint256", "name": "initMaxLimit", "type": "uint256"}, {"internalType": "uint256", "name": "initMinLimit", "type": "uint256"}, {"internalType": "address[]", "name": "authorizedFaucets_", "type": "address[]"}, {"internalType": "uint256", "name": "dripAmount_", "type": "uint256"}, {"internalType": "uint256", "name": "nativeDripAmount_", "type": "uint256"}]}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "eXYZ", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isEnabledXYZ", "outputs": [{"internalType": "bool", "name": "isEnabled", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isXYZ", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "pause"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "renounceRole"}, {"inputs": [{"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "rescueCrypto"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "revokeRole"}, {"inputs": [{"internalType": "uint256", "name": "newDripAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setDripAmount"}, {"inputs": [{"internalType": "uint256", "name": "newThreshold", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setLowBalanceThreshold"}, {"inputs": [{"internalType": "uint256", "name": "newNativeDripAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "setNativeDripAmount"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "wallet", "type": "address"}, {"internalType": "struct StablecoinHandler.StablecoinSwap", "name": "ss", "type": "tuple", "components": [{"internalType": "address", "name": "destination", "type": "address"}, {"internalType": "address", "name": "origin", "type": "address"}, {"internalType": "uint256", "name": "oAmount", "type": "uint256"}, {"internalType": "address", "name": "target", "type": "address"}, {"internalType": "uint256", "name": "tAmount", "type": "uint256"}]}], "stateMutability": "nonpayable", "type": "function", "name": "swapAndSend"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "unpause"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "upgradeToAndCall"}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {"UpdateXYZ(address,bool,uint256,uint256)": {"details": "Modifies the validity status and supply limits of the specified token. Can only be executed by addresses with the MAINTAINER_ROLE. This method is crucial for maintaining the operational parameters of external XYZ tokens within the system.", "params": {"maxLimit": "The maximum supply limit for the token.", "minLimit": "The minimum supply limit for the token. Emits an `XYZUpdated` event upon successfully updating the token's parameters.", "token": "The address of the external XYZ token to update.", "validity": "A boolean indicating whether the token should be considered valid."}}, "convertFromEXYZ(address,address,(address,address,uint256,address,uint256))": {"details": "Operates within the constraints of roles and the contract's paused state, facilitating the conversion process.", "params": {"safe": "The safe address from which target tokens will be sent.", "ss": "The details of the stablecoin swap operation.", "wallet": "The wallet address from which tokens will be burned."}}, "convertToEXYZ(address,address,(address,address,uint256,address,uint256))": {"details": "Ensures the operation is performed according to the roles and pause state, transferring from a wallet to a safe address.", "params": {"safe": "The safe address to receive the origin tokens.", "ss": "The stablecoin swap details.", "wallet": "The wallet address from which tokens will be transferred."}}, "drip(address,address)": {"details": "Faucet function defining this contract as the onchain entrypoint for minting testnet tokens to usersTo mint the chain's native token, use `NATIVE_TOKEN_POINTER == address(0x0)`"}, "getEnabledXYZs()": {"details": "Fetches all currently valid stablecoin addresses"}, "getEnabledXYZsWithMetadata()": {"details": "Fetches all currently valid stablecoins with metadata for dynamic rendering by a frontend"}, "getLastFulfilledDripTimestamp(address,address)": {"details": "Exposes the timestamp of the last fulfilled faucet drip for a given `token` and `recipient`"}, "getMaxLimit(address)": {"details": "Reads the maximum supply limit set for the token from the contract's storage. This function provides visibility into the operational constraints of external XYZ tokens, specifically the upper bound of the token's supply within the system.", "params": {"token": "The address of the external XYZ token whose maximum supply limit is being queried."}, "returns": {"_0": "uint256 The maximum supply limit for the specified token. This value represents the upper limit on the total supply of the token that can be managed by the contract."}}, "getMinLimit(address)": {"details": "Reads the minimum supply limit set for the token from the contract's storage. This function is essential for understanding the operational constraints of external XYZ tokens, highlighting the lower bound of the token's supply that is considered acceptable within the system.", "params": {"token": "The address of the external XYZ token whose minimum supply limit is being queried."}, "returns": {"_0": "uint256 The minimum supply limit for the specified token. This value indicates the minimum amount of the token that should be maintained or is allowable within the contract's management scope."}}, "getRoleAdmin(bytes32)": {"details": "Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}."}, "grantRole(bytes32,address)": {"details": "Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event."}, "hasRole(bytes32,address)": {"details": "Returns `true` if `account` has been granted `role`."}, "initialize((address,address,address[],uint256,uint256,address[],uint256,uint256))": {"details": "Invokes `__Pausable_init()`"}, "isXYZ(address)": {"details": "Reads from the contract's storage to determine the validity of the token address.", "params": {"token": "The address of the token to check."}, "returns": {"_0": "bool True if the token is a valid external XYZ token, false otherwise."}}, "pause()": {"details": "Can only be called by addresses with the PAUSER_ROLE, halting certain functionalities."}, "paused()": {"details": "Returns true if the contract is paused, and false otherwise."}, "proxiableUUID()": {"details": "Implementation of the ERC1822 {proxiableUUID} function. This returns the storage slot used by the implementation. It is used to validate the implementation's compatibility when performing an upgrade. IMPORTANT: A proxy pointing at a proxiable contract should not be considered proxiable itself, because this risks bricking a proxy that upgrades to it, by delegating to itself until out of gas. Thus it is critical that this function revert if invoked through a proxy. This is guaranteed by the `notDelegated` modifier."}, "renounceRole(bytes32,address)": {"details": "Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event."}, "rescueCrypto(address,uint256)": {"details": "Allows for the recovery of both ERC20 tokens and native token sent to the contract.", "params": {"amount": "The amount of the token to rescue.", "token": "The token to rescue. Use `address(0x0)` for native token."}}, "revokeRole(bytes32,address)": {"details": "Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event."}, "setDripAmount(uint256)": {"details": "Provides a way for Telcoin maintainers to alter the faucet's eXYZ drip amount onchain"}, "setLowBalanceThreshold(uint256)": {"details": "Provides a way for Telcoin maintainers to configure the faucet's threshold for top-up alerts"}, "setNativeDripAmount(uint256)": {"details": "Provides a way for Telcoin maintainers to alter the faucet's native token drip amount onchain"}, "supportsInterface(bytes4)": {"details": "See {IERC165-supportsInterface}."}, "swapAndSend(address,(address,address,uint256,address,uint256))": {"details": "Only callable by addresses with the SWAPPER_ROLE and when the contract is not paused.", "params": {"ss": "The stablecoin swap details, including source, target, and amounts.", "wallet": "The wallet address from which tokens will be burned."}}, "unpause()": {"details": "Only callable by addresses with the PAUSER_ROLE, reenabling functionalities halted by pausing."}, "upgradeToAndCall(address,bytes)": {"custom:oz-upgrades-unsafe-allow-reachable": "delegatecall", "details": "Upgrade the implementation of the proxy to `newImplementation`, and subsequently execute the function call encoded in `data`. Calls {_authorizeUpgrade}. Emits an {Upgraded} event."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"UpdateXYZ(address,bool,uint256,uint256)": {"notice": "Updates the configuration for an external XYZ token."}, "convertFromEXYZ(address,address,(address,address,uint256,address,uint256))": {"notice": "Converts from an external XYZ token to another asset as specified."}, "convertToEXYZ(address,address,(address,address,uint256,address,uint256))": {"notice": "Converts assets to an external XYZ token with specified parameters."}, "drip(address,address)": {"notice": "This contract must be given `Stablecoin::MINTER_ROLE` on each eXYZ contractImplements Access Control, requiring callers to possess the `FAUCET_ROLE`"}, "getDripAmount()": {"notice": "Agnostic to enabled/disabled status for data availability"}, "getEnabledXYZs()": {"notice": "Excludes `NATIVE_TOKEN_POINTER` if it is enabled"}, "getEnabledXYZsWithMetadata()": {"notice": "Intended for use in a view context to save on RPC calls"}, "getMaxLimit(address)": {"notice": "Retrieves the maximum supply limit for a specified external XYZ token."}, "getMinLimit(address)": {"notice": "Retrieves the minimum supply limit for a specified external XYZ token."}, "getNativeDripAmount()": {"notice": "Agnostic to enabled/disabled status for data availability"}, "isEnabledXYZ(address)": {"notice": "To identify if faucet has the native token enabled, pass in `address(0x0)`"}, "isXYZ(address)": {"notice": "Checks if a given token address is recognized as a valid external XYZ token."}, "pause()": {"notice": "Pauses all pause-sensitive operations within the contract."}, "rescueCrypto(address,uint256)": {"notice": "Rescues crypto assets mistakenly sent to the contract."}, "setDripAmount(uint256)": {"notice": "Rather than set `dripAmount` to 0, disable the token"}, "setNativeDripAmount(uint256)": {"notice": "Rather than set `nativeDripAmount` to 0, disable the token"}, "swapAndSend(address,(address,address,uint256,address,uint256))": {"notice": "Swaps and sends stablecoins according to specified parameters, enforcing role and pause state."}, "unpause()": {"notice": "Unpauses the contract, allowing previously paused operations to resume."}}, "version": 1}}, "settings": {"remappings": ["@axelar-cgp-solidity/=node_modules/@axelar-network/axelar-cgp-solidity/", "@axelar-network/=node_modules/@axelar-network/", "@openzeppelin-contracts/=node_modules/recoverable-wrapper/node_modules/@openzeppelin/contracts/", "@openzeppelin/=node_modules/@openzeppelin/", "@uniswap/=node_modules/@uniswap/", "ds-test/=node_modules/recoverable-wrapper/lib/forge-std/lib/ds-test/src/", "forge-std/=node_modules/forge-std/src/", "recoverable-wrapper/contracts/=node_modules/recoverable-wrapper/contracts/", "solady/=node_modules/solady/src/", "telcoin-contracts/=node_modules/telcoin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/StablecoinManager.sol": "StablecoinManager"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"external/telcoin-contracts/interfaces/IStablecoin.sol": {"keccak256": "0xe6bc07e357c669be88e2f83316b7135712ff11132758ba190fc7cda9e1b87abc", "urls": ["bzz-raw://a71ed63fd8d6f2188ed37af7c4c7be30bc602c59b03bf6fd9e402af37a56bec2", "dweb:/ipfs/QmPtEGzLgREdATYHtVCWwWQvLWkSB5BFM1P9paFpDjksi8"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol": {"keccak256": "0x6662ec4e5cefca03eeadd073e9469df8d2944bb2ee8ec8f7622c2c46aab5f225", "urls": ["bzz-raw://4d8544c6f8daa4d1bc215c6a72fe0acdb748664a105b0e5efc19295667521d45", "dweb:/ipfs/QmdGWqdnXT8S3RgCR6aV8XHZrsybieMQLLnug1NtpSjEXN"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol": {"keccak256": "0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b", "urls": ["bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609", "dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol": {"keccak256": "0x9a1766b1921bf91b3e61eb53c7a6e70725254befd4bdcbbcd3af40bd9f66856f", "urls": ["bzz-raw://29bf2fa41a172086a665c9738377b93655aa4b1ffda9fe839c8bdf646f185040", "dweb:/ipfs/QmeB21qDuo8WPQSrqXJbQmWHKsdeocGNSUWLhCwniVejrt"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/token/ERC20/extensions/ERC20PermitUpgradeable.sol": {"keccak256": "0x8a97653aeba40e9f0c2e8df1a1379b29b927b6dc3534040c668e71ad9ae89d88", "urls": ["bzz-raw://6e529c294c9d634eb68a1e4aeb66eb8381de5a08ccd2c0bfeebd48a6b28fcff7", "dweb:/ipfs/QmWCezuxfZb68nM3Hs6XzQNNiW7VJsymU4sajy2DW1CKbp"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/utils/NoncesUpgradeable.sol": {"keccak256": "0x778f4a1546a1c6c726ecc8e2348a2789690fb8f26e12bd9d89537669167b79a4", "urls": ["bzz-raw://851d3dfe724e918ff0a064b206e1ef46b27ab0df2aa2c8af976973a22ef59827", "dweb:/ipfs/Qmd4wb7zX8ueYhMVBy5PJjfsANK3Ra3pKPN7qQkNsdwGHn"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol": {"keccak256": "0x92915b7f7f642c6be3f65bfd1522feb5d5b6ef25f755f4dbb51df32c868f2f97", "urls": ["bzz-raw://85ad36d5cc7e190e1ee6c94b24659bc3a31396c4c36b6ffa6a509e10661f8007", "dweb:/ipfs/QmPFyc4zMh2zo6YWZt25gjm3YdR2hg6wGETaWw256fMmJJ"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/utils/cryptography/EIP712Upgradeable.sol": {"keccak256": "0x85462422a22578744581e012e9aa0a391958cb360288b0b63f29bf0431d70327", "urls": ["bzz-raw://2bc529e2b9b28da5d26da451058250d85afcaa3c5083ee273ac68fa6bf956b78", "dweb:/ipfs/Qmd3Aq59ztmoVmHigsaR4YjkXWKERVpjfQ4a2PHk7Ke6Rx"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol": {"keccak256": "0xdaba3f7c42c55b2896353f32bd27d4d5f8bae741b3b05d4c53f67abc4dc47ce8", "urls": ["bzz-raw://1fa2e61141c602510bcd2cd936ed9561922ac8772a9b9c9a9db091a74e354a45", "dweb:/ipfs/QmcHQDDoEBwJmwUbzoVkytvJsBx3KVHYFFnDkvRGWh9Wmh"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/access/IAccessControl.sol": {"keccak256": "0xb6b36edd6a2999fd243ff226d6cbf84bd71af2432bbd0dfe19392996a1d9cb41", "urls": ["bzz-raw://1fd2f35495652e57e3f99bc6c510bc5f7dd398a176ea2e72d8ed730aebc6ca26", "dweb:/ipfs/QmTQV6X4gkikTib49cho5iDX3JvSQbdsoEChoDwrk3CbbH"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC5267.sol": {"keccak256": "0x92aa1df62dc3d33f1656d63bede0923e0df0b706ad4137c8b10b0a8fe549fd92", "urls": ["bzz-raw://c5c0f29195ad64cbe556da8e257dac8f05f78c53f90323c0d2accf8e6922d33a", "dweb:/ipfs/QmQ61TED8uaCZwcbh8KkgRSsCav7x7HbcGHwHts3U4DmUP"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC1822.sol": {"keccak256": "0x2a1f9944df2015c081d89cd41ba22ffaf10aa6285969f0dc612b235cc448999c", "urls": ["bzz-raw://ef381843676aec64421200ee85eaa0b1356a35f28b9fc67e746a6bbb832077d9", "dweb:/ipfs/QmY8aorMYA2TeTCnu6ejDjzb4rW4t7TCtW4GZ6LoxTFm7v"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x60c65f701957fdd6faea1acb0bb45825791d473693ed9ecb34726fdfaa849dd7", "urls": ["bzz-raw://ea290300e0efc4d901244949dc4d877fd46e6c5e43dc2b26620e8efab3ab803f", "dweb:/ipfs/QmcLLJppxKeJWqHxE2CUkcfhuRTgHSn8J4kijcLa5MYhSt"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x06a78f9b3ee3e6d0eb4e4cd635ba49960bea34cac1db8c0a27c75f2319f1fd65", "urls": ["bzz-raw://547d21aa17f4f3f1a1a7edf7167beff8dd9496a0348d5588f15cc8a4b29d052a", "dweb:/ipfs/QmT16JtRQSWNpLo9W23jr6CzaMuTAcQcjJJcdRd8HLJ6cE"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/proxy/utils/UUPSUpgradeable.sol": {"keccak256": "0x3ffb56bcb175984a10b1167e2eba560876bfe96a435f5d62ffed8b1bb4ebc4c7", "urls": ["bzz-raw://7db94af56aa20efb57c3f9003eacd884faad04118967d8e35cdffe07790bbdcd", "dweb:/ipfs/QmXtAshRWFjcQ1kL7gpC5CiLUZgJ9uzrZyeHp2Sux9ojPF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xaa761817f6cd7892fcf158b3c776b34551cde36f48ff9703d53898bc45a94ea2", "urls": ["bzz-raw://0ad7c8d4d08938c8dfc43d75a148863fb324b80cf53e0a36f7e5a4ac29008850", "dweb:/ipfs/QmcrhfPgVNf5mkdhQvy1pMv51TFokD3Y4Wa5WZhFqVh8UV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff", "urls": ["bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d", "dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386", "urls": ["bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0", "dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/StorageSlot.sol": {"keccak256": "0x32ba59b4b7299237c8ba56319110989d7978a039faf754793064e967e5894418", "urls": ["bzz-raw://1ae50c8b562427df610cc4540c9bf104acca7ef8e2dcae567ae7e52272281e9c", "dweb:/ipfs/QmTHiadFCSJUPpRjNegc5SahmeU8bAoY8i9Aq6tVscbcKR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Strings.sol": {"keccak256": "0x55f102ea785d8399c0e58d1108e2d289506dde18abc6db1b7f68c1f9f9bc5792", "urls": ["bzz-raw://6e52e0a7765c943ef14e5bcf11e46e6139fa044be564881378349236bf2e3453", "dweb:/ipfs/QmZEeeXoFPW47amyP35gfzomF9DixqqTEPwzBakv6cZw6i"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0xeed0a08b0b091f528356cbc7245891a4c748682d4f6a18055e8e6ca77d12a6cf", "urls": ["bzz-raw://ba80ba06c8e6be852847e4c5f4492cef801feb6558ae09ed705ff2e04ea8b13c", "dweb:/ipfs/QmXRJDv3xHLVQCVXg1ZvR35QS9sij5y9NDWYzMfUfAdTHF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0xba333517a3add42cd35fe877656fc3dfcc9de53baa4f3aabbd6d12a92e4ea435", "urls": ["bzz-raw://2ceacff44c0fdc81e48e0e0b1db87a2076d3c1fb497341de077bf1da9f6b406c", "dweb:/ipfs/QmRUo1muMRAewxrKQ7TkXUtknyRoR57AyEkoPpiuZQ8FzX"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4296879f55019b23e135000eb36896057e7101fb7fb859c5ef690cf14643757b", "urls": ["bzz-raw://87b3541437c8c443ccd36795e56a338ed12855eec17f8da624511b8d1a7e14df", "dweb:/ipfs/QmeJQCtZrQjtJLr6u7ZHWeH3pBnjtLWzvRrKViAi7UZqxL"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/math/Math.sol": {"keccak256": "0x005ec64c6313f0555d59e278f9a7a5ab2db5bdc72a027f255a37c327af1ec02d", "urls": ["bzz-raw://4ece9f0b9c8daca08c76b6b5405a6446b6f73b3a15fab7ff56e296cbd4a2c875", "dweb:/ipfs/QmQyRpyPRL5SQuAgj6SHmbir3foX65FJjbVTTQrA2EFg6L"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/math/SignedMath.sol": {"keccak256": "0x5f7e4076e175393767754387c962926577f1660dd9b810187b9002407656be72", "urls": ["bzz-raw://7d533a1c97cd43a57cd9c465f7ee8dd0e39ae93a8fb8ff8e5303a356b081cdcc", "dweb:/ipfs/QmVBEei6aTnvYNZp2CHYVNKyZS4q1KkjANfY39WVXZXVoT"], "license": "MIT"}, "node_modules/telcoin-contracts/contracts/stablecoin/Stablecoin.sol": {"keccak256": "0x4782fd54d946422bbb45ddf6a13f73d8f7cd4843946685462468ef761307757d", "urls": ["bzz-raw://e1f817e0b88fcbb814ebd68ef31bf700635df39de812203d9ae8e2b28f96c266", "dweb:/ipfs/QmZAm6Srd1ej3hnZmJMeSuvqSMDHrxETq8oXcBBSCN1s9M"], "license": "MIT"}, "node_modules/telcoin-contracts/contracts/stablecoin/StablecoinHandler.sol": {"keccak256": "0xac513311ece00215b80000e2f41c728686c65d5ed2cf5a71e1c975d186938397", "urls": ["bzz-raw://472761a9129440a6a49773e55f13c656a24eab6eb8c356257c63a35a074b7cad", "dweb:/ipfs/QmNZJGt2jPfui2DyyQqp28WvZvwiKS2jaBqFdRcGTM1gBP"], "license": "MIT"}, "node_modules/telcoin-contracts/contracts/util/abstract/Blacklist.sol": {"keccak256": "0xb8b4825ef9c29eb37cdaf5855e749f40e9b63d8ed746ca80a58585e70efca80f", "urls": ["bzz-raw://f8d5e75f35e91f1c53159c41d00bd50c34e5e54fe15b37fa4756ebd576d6fa6c", "dweb:/ipfs/QmaUvEy7yT8hVsXPc3WhJey1zaB5y3n5dq9TeTuNugZm7D"], "license": "MIT"}, "src/StablecoinManager.sol": {"keccak256": "0x0f70a9e0efe4abada9ea1f54bb14c14447d572158bbb11e643c13618a0bfed21", "urls": ["bzz-raw://62759753c605a920dbd861ca28610dc2abf169deba647a672be772b8f0689201", "dweb:/ipfs/QmRNEyTnMYpVH14JKdAzCfZgr2tU1k8cP8p7CYZzhbPpCM"], "license": "MIT"}, "src/faucet/TNFaucet.sol": {"keccak256": "0x0c0bab367410b4f28c9d5636f12ec2e13e3064aae63e9c1962fc4cf5569d8a4a", "urls": ["bzz-raw://fdbe7de5a4cf6e55efb34c07877f4cecb4acda9e9e93b407e08d80443fde27d2", "dweb:/ipfs/QmWBoP18RxCvxzYaxgUYApo1sWHfduboMjqFgBzyA4wyhU"], "license": "MIT"}}, "version": 1}, "id": 127}