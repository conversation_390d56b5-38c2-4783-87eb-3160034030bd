[workspace]
members = [
    # common
    "bin/telcoin-network",
    "crates/config",
    "crates/engine",
    "crates/node",
    "crates/storage",
    "crates/test-utils",
    "crates/tn-utils",
    "crates/test-utils",
    "crates/types",
    "crates/network-types",
    "crates/network-libp2p",
    "crates/state-sync",
    "crates/tn-reth",

    # consensus
    "crates/consensus/consensus-metrics",
    "crates/consensus/executor",
    "crates/consensus/primary",
    "crates/consensus/worker",
    "crates/consensus/primary-metrics",

    # execution
    "crates/batch-builder",
    "crates/batch-validator",
    "crates/execution/faucet",
    "crates/execution/tn-rpc",
]

# Explicitly set the resolver to version 2, which is the default for packages with edition >= 2021
# https://doc.rust-lang.org/edition-guide/rust-2021/default-cargo-resolver.html
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
# Remember to update:
# - .clippy.toml
# - README.md
# - Dockerfile
rust-version = "1.86" # see note
license = "MIT OR Apache-2.0"
homepage = "https://telcoin.network"
repository = "https://github.com/telcoin/telcoin-network"
authors = [
    "Grant Kee <<EMAIL>>",
    "Steve Stanfield <<EMAIL>>",
    "Markus Osterlund <<EMAIL>>",
    "Telcoin Association <<EMAIL>>",
]
description = "Telcoin Network protocol."
exclude = [".github/"]

[workspace.lints]

[workspace.dependencies]
# misc
clap = "4"
eyre = "0.6"
futures = "0.3"
tokio = { version = "1.44", default-features = false }
tracing = "0.1.0"
tracing-subscriber = "0.3.18"
metrics = "0.23.0"                                     # Needed for `metrics-macro` to resolve the crate using `::metrics` notation
serde_json = "1.0.94"
humantime-serde = "1.1"
fdlimit = "0.3.0"
snap = "1.1.1"
rayon = "1.7"

# crypto
secp256k1 = { version = "0.31", default-features = false, features = [
    "global-context",
    "rand",
    "recovery",
] }

rpassword = "7.4.0"

# execution
reth = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-primitives = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-primitives-traits = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-chainspec = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-chain-state = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-config = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-db = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-db-common = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-downloaders = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-engine-primitives = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-engine-tree = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-ethereum-engine-primitives = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-ethereum-primitives = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-evm-ethereum = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-evm-config = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-network = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-network-api = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-network-p2p = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-network-peers = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-evm = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-execution-types = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-revm = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-exex = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-rpc = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-rpc-types-compat = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-rpc-eth-types = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-payload-primitives = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-rpc-engine-api = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-tracing = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-provider = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-snapshot = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-stages = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-static-file = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-tasks = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-transaction-pool = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-consensus = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-beacon-consensus = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-tokio-util = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-node-api = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-node-builder = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-node-core = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-node-ethereum = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-node-events = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-node-types = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-discv4 = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-ethereum-payload-builder = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-eth-wire = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-metrics = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-cli-util = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-cli-commands = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-payload-builder = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-basic-payload-builder = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-errors = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-libmdbx = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-trie = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-trie-db = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-execution-errors = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }
reth-fs-util = { git = "https://github.com/paradigmxyz/reth", tag = "v1.4.8" }

# eth
alloy = { version = "1.0.9", features = ["full"] }
alloy-rlp = "0.3.10"
alloy-evm = { version = "0.10" }

tn-batch-validator = { path = "crates/batch-validator" }
tn-engine = { path = "crates/engine" }

# batch maker
tn-batch-builder = { path = "crates/batch-builder" }
futures-util = "0.3.25"
jsonrpsee = { version = "0.25.1" }
jsonrpsee-types = { version = "0.25.1" }
assert_matches = { version = "1.5.0" }

# common
tn-types = { path = "./crates/types" }
tn-reth = { path = "./crates/tn-reth" }
tn-node = { path = "./crates/node" }
tn-config = { path = "./crates/config" }
tn-network-libp2p = { path = "./crates/network-libp2p" }
tn-network-types = { path = "./crates/network-types" }
tn-rpc = { path = "./crates/execution/tn-rpc" }
state-sync = { path = "./crates/state-sync" }
tn-utils = { path = "./crates/tn-utils" }

# optional
tn-faucet = { path = "./crates/execution/faucet" }

# consensus
tn-executor = { path = "./crates/consensus/executor" }
tn-worker = { path = "./crates/consensus/worker" }
tn-storage = { path = "./crates/storage" }
tn-primary = { path = "./crates/consensus/primary" }
tn-test-utils = { path = "./crates/test-utils" }
consensus-metrics = { path = "./crates/consensus/consensus-metrics" }
tn-primary-metrics = { path = "./crates/consensus/primary-metrics" }

# misc
match_opt = "0.1.2"
serde = { version = "^1.0", features = ["derive", "rc"] }
serde_repr = "0.1"
rand = "0.9"
rand_chacha = "0.9"
thiserror = "1.0.50"
tempfile = "3.3.0"
bcs = "0.1.4"
bincode = "1.3.3"
proptest = "1.5.0"
proptest-derive = "0.4"
serde-reflection = "0.3.6"
indexmap = { version = "2.5.0", features = ["serde"] }
once_cell = "1.18.0"
prometheus = { version = "0.13.4", default-features = false }
tonic = { version = "0.13" }
async-trait = "0.1.61"
dashmap = "6.0.1"
parking_lot = "0.12.3"
scopeguard = "1.1"
uuid = { version = "1.1.2", features = ["v4", "fast-rng"] }
quinn-proto = "0.11.8"
derive_builder = "0.12.0"
roaring = "0.10.1"
serde_with = { version = "2.1.0", features = ["hex"] }
ouroboros = "0.17"
proc-macro2 = "1.0.47"
uint = "0.10"
cfg-if = "1.0.0"
lru = "0.10"
tokio-stream = { version = "0.1.14", features = ["sync", "net"] }
serde_yaml = "0.8.26"
byteorder = "1.4.3"
rustversion = "1.0.9"
bytes = "1.4.0"
humantime = "2.1.0"
blst = "0.3.14"
hex = "0.4.3"
dirs-next = "2.0.0"

criterion = { version = "0.5.0", features = [
    "async",
    "async_tokio",
    "html_reports",
] }

reqwest = { version = "0.12", default-features = false, features = [
    "blocking",
    "json",
    "rustls-tls",
] }

axum = { version = "0.6.6", default-features = false, features = [
    "headers",
    "tokio",
    "http1",
    "http2",
    "json",
    "matched-path",
    "original-uri",
    "form",
    "query",
    "ws",
] }

axum-server = { version = "0.5.1", default-features = false, features = [
    "tls-rustls",
] }

tower = { version = "0.4.12", features = [
    "full",
    "util",
    "timeout",
    "load-shed",
    "limit",
] }

backoff = { version = "0.4.0", features = [
    "futures",
    "futures-core",
    "pin-project-lite",
    "tokio",
    "tokio_1",
] }

# patch from v0.55
libp2p = { git = "https://github.com/Telcoin-Association/rust-libp2p", rev = "7c2c28a186f6d22bcc4f21cfefc2b53122a53a47" }
bs58 = { version = "0.5.1" }
blake3 = { version = "1.8.2" }

# On a panic, end entire app not just a thread.
[profile.release]
panic = 'abort'

[profile.dev]
panic = 'abort'
