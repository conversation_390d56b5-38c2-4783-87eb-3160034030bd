// Test file for race condition vulnerability in GasAccumulator
// This test demonstrates the race condition between clear() and concurrent operations

use std::sync::{Arc, Barrier, atomic::{AtomicBool, AtomicU64, Ordering}};
use std::thread;
use std::time::{Duration, Instant};
use tn_types::gas_accumulator::GasAccumulator;

#[test]
fn test_race_condition_gas_accumulator() {
    println!("🔍 Testing race condition in GasAccumulator");
    
    let gas_accumulator = Arc::new(GasAccumulator::new(4));
    let barrier = Arc::new(Barrier::new(3));
    
    // Initial state: add some gas data
    gas_accumulator.inc_block(0, 1000000, ********);
    gas_accumulator.inc_block(1, 2000000, ********);
    gas_accumulator.inc_block(2, 1500000, ********);
    gas_accumulator.inc_block(3, 1800000, ********);
    
    let (blocks_before, gas_before, _) = gas_accumulator.get_values(2);
    println!("Before race - Worker 2: blocks={}, gas={}", blocks_before, gas_before);
    
    // Thread 1: clear() operation
    let gas_acc_clear = gas_accumulator.clone();
    let barrier_clear = barrier.clone();
    let clear_handle = thread::spawn(move || {
        barrier_clear.wait();
        println!("Thread 1: Starting clear()");
        gas_acc_clear.clear();
        println!("Thread 1: clear() completed");
    });
    
    // Thread 2: concurrent inc_block() operations
    let gas_acc_inc = gas_accumulator.clone();
    let barrier_inc = barrier.clone();
    let inc_handle = thread::spawn(move || {
        barrier_inc.wait();
        // Rapid fire inc_block calls during clear
        for i in 0..100 {
            gas_acc_inc.inc_block(2, 50000, ********);
            if i % 25 == 0 {
                thread::sleep(Duration::from_micros(1));
            }
        }
        println!("Thread 2: inc_block() operations completed");
    });
    
    // Main thread: coordinate and observe
    barrier.wait();
    
    // Wait for completion
    clear_handle.join().unwrap();
    inc_handle.join().unwrap();
    
    // Check final state
    let (blocks_after, gas_after, _) = gas_accumulator.get_values(2);
    println!("After race - Worker 2: blocks={}, gas={}", blocks_after, gas_after);
    
    // Demonstrate the race condition
    println!("\n=== RACE CONDITION ANALYSIS ===");
    println!("Expected after clear(): blocks=0, gas=0");
    println!("Expected after 100 inc_block(): blocks=100, gas=5000000");
    println!("Actual result: blocks={}, gas={}", blocks_after, gas_after);
    
    if blocks_after > 0 && blocks_after < 100 {
        println!("🚨 RACE CONDITION DETECTED: Partial state corruption!");
        println!("Some inc_block() operations occurred after partial clear()");
    }
    
    assert!(true); // Test always passes to show the race condition
}

#[test]
fn test_epoch_boundary_race_condition() {
    println!("🌅 Testing epoch boundary race condition");
    
    let gas_accumulator = Arc::new(GasAccumulator::new(3));
    
    // Simulate end-of-epoch state
    for worker_id in 0..3 {
        for _ in 0..10 {
            gas_accumulator.inc_block(worker_id, 1000000, ********);
        }
    }
    
    println!("Pre-epoch-boundary state:");
    for worker_id in 0..3 {
        let (blocks, gas, _) = gas_accumulator.get_values(worker_id);
        println!("  Worker {}: {} blocks, {} gas", worker_id, blocks, gas);
    }
    
    // Simulate the exact scenario from epoch manager
    let barrier = Arc::new(Barrier::new(2));
    
    // Thread 1: Epoch manager clearing for new epoch
    let gas_acc_epoch = gas_accumulator.clone();
    let barrier_epoch = barrier.clone();
    let epoch_handle = thread::spawn(move || {
        barrier_epoch.wait();
        println!("🌅 Epoch Manager: Starting new epoch, clearing accumulator");
        gas_acc_epoch.clear();
        println!("🌅 Epoch Manager: Accumulator cleared for new epoch");
    });
    
    // Thread 2: Engine still processing blocks from previous epoch
    let gas_acc_engine = gas_accumulator.clone();
    let barrier_engine = barrier.clone();
    let engine_handle = thread::spawn(move || {
        barrier_engine.wait();
        println!("⚙️  Engine: Processing remaining blocks from previous epoch");
        
        // Simulate engine processing blocks that were committed but not yet accounted
        for i in 0..5 {
            gas_acc_engine.inc_block(i % 3, 500000, ********);
            thread::sleep(Duration::from_micros(50));
        }
        
        println!("⚙️  Engine: Finished processing previous epoch blocks");
    });
    
    epoch_handle.join().unwrap();
    engine_handle.join().unwrap();
    
    println!("Post-race state:");
    let mut total_blocks = 0;
    for worker_id in 0..3 {
        let (blocks, gas, _) = gas_accumulator.get_values(worker_id);
        total_blocks += blocks;
        println!("  Worker {}: {} blocks, {} gas", worker_id, blocks, gas);
    }
    
    if total_blocks > 0 {
        println!("🚨 EPOCH BOUNDARY RACE DETECTED!");
        println!("   - {} blocks from previous epoch leaked into new epoch", total_blocks);
        println!("   - This corrupts base fee calculations for the new epoch");
    } else {
        println!("✅ No epoch boundary race detected in this run");
    }
    
    assert!(true); // Test always passes to demonstrate the vulnerability
}

#[test]
fn test_concurrent_operations_stress() {
    println!("⚡ Stress testing concurrent operations");
    
    let gas_accumulator = Arc::new(GasAccumulator::new(4));
    let _race_detected = Arc::new(AtomicBool::new(false));
    let operations_completed = Arc::new(AtomicU64::new(0));
    
    let barrier = Arc::new(Barrier::new(4));
    let start_time = Instant::now();
    
    // Thread 1: Clear operations
    let gas_acc_clear = gas_accumulator.clone();
    let barrier_clear = barrier.clone();
    let clear_handle = thread::spawn(move || {
        barrier_clear.wait();
        for _ in 0..10 {
            gas_acc_clear.clear();
            thread::sleep(Duration::from_micros(100));
        }
    });
    
    // Thread 2-4: Concurrent inc_block operations
    let mut handles = vec![];
    for thread_id in 0..3 {
        let gas_acc = gas_accumulator.clone();
        let barrier_clone = barrier.clone();
        let ops_counter = operations_completed.clone();
        
        let handle = thread::spawn(move || {
            barrier_clone.wait();
            for i in 0..200 {
                gas_acc.inc_block((thread_id + i) % 4, 50000 + (i as u64 * 1000), ********);
                ops_counter.fetch_add(1, Ordering::Relaxed);
                
                if i % 50 == 0 {
                    thread::sleep(Duration::from_micros(1));
                }
            }
        });
        handles.push(handle);
    }
    
    // Wait for all threads
    clear_handle.join().unwrap();
    for handle in handles {
        handle.join().unwrap();
    }
    
    let total_time = start_time.elapsed();
    let total_ops = operations_completed.load(Ordering::Relaxed);
    
    println!("Stress test completed:");
    println!("  - Total time: {:?}", total_time);
    println!("  - Operations completed: {}", total_ops);
    
    // Check final state consistency
    let mut total_blocks = 0;
    for worker_id in 0..4 {
        let (blocks, gas, _) = gas_accumulator.get_values(worker_id);
        total_blocks += blocks;
        println!("  Worker {}: {} blocks, {} gas", worker_id, blocks, gas);
    }
    
    println!("  - Total blocks accumulated: {}", total_blocks);
    
    if total_blocks > 0 && total_blocks < 600 {
        println!("🚨 RACE CONDITION DETECTED in stress test!");
        println!("   - Expected 0 or 600 blocks, got {}", total_blocks);
    }
    
    assert!(true); // Test demonstrates the vulnerability
}

#[test]
fn test_reward_counter_race_condition() {
    println!("🏆 Testing reward counter race condition");
    
    let gas_accumulator = Arc::new(GasAccumulator::new(2));
    let rewards_counter = gas_accumulator.rewards_counter();
    
    // This test demonstrates the concept but would need proper Committee setup
    // to fully test the reward counter race condition
    
    let barrier = Arc::new(Barrier::new(2));
    
    // Thread 1: clear rewards
    let rewards_clear = rewards_counter.clone();
    let barrier_clear = barrier.clone();
    let clear_handle = thread::spawn(move || {
        barrier_clear.wait();
        for _ in 0..50 {
            rewards_clear.clear();
            thread::sleep(Duration::from_micros(10));
        }
    });
    
    // Thread 2: This would increment leader counts if we had proper AuthorityIdentifier
    let barrier_inc = barrier.clone();
    let inc_handle = thread::spawn(move || {
        barrier_inc.wait();
        // Simulate concurrent operations that would affect reward counting
        thread::sleep(Duration::from_millis(1));
    });
    
    barrier.wait();
    clear_handle.join().unwrap();
    inc_handle.join().unwrap();
    
    println!("✅ Reward counter race condition test completed");
    println!("   - This test demonstrates the vulnerability concept");
    println!("   - Full test would require proper Committee and AuthorityIdentifier setup");
    
    assert!(true);
}
