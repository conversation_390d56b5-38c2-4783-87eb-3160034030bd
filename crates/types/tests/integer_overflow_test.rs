//! Test for integer overflow vulnerability in Header::nonce() calculation
//! 
//! This test demonstrates the integer overflow vulnerability in the nonce calculation
//! that can lead to nonce collisions and consensus disruption.

use tn_types::{HeaderBuilder, AuthorityIdentifier};
use std::collections::BTreeSet;
use indexmap::IndexMap;

#[test]
fn test_nonce_integer_overflow_vulnerability() {
    println!("=== Testing Integer Overflow Vulnerability in Header::nonce() ===");
    
    // Test case 1: Epoch at overflow boundary (2^32)
    let header1 = HeaderBuilder::default()
        .epoch(u32::MAX) // Maximum u32 value
        .round(0x1234)
        .author(AuthorityIdentifier::default())
        .payload(IndexMap::new())
        .parents(BTreeSet::new())
        .latest_execution_block(Default::default())
        .build();

    let nonce1 = header1.nonce();

    // Test case 2: Epoch after overflow (simulate overflow behavior)
    let header2 = HeaderBuilder::default()
        .epoch(0) // This will simulate overflow wrapping
        .round(0x1234)
        .author(AuthorityIdentifier::default())
        .payload(IndexMap::new())
        .parents(BTreeSet::new())
        .latest_execution_block(Default::default())
        .build();

    let nonce2 = header2.nonce();

    // Test case 3: Normal epoch with same round
    let header3 = HeaderBuilder::default()
        .epoch(0x1)
        .round(0x1234)
        .author(AuthorityIdentifier::default())
        .payload(IndexMap::new())
        .parents(BTreeSet::new())
        .latest_execution_block(Default::default())
        .build();

    let nonce3 = header3.nonce();

    // Test case 4: Maximum safe epoch (2^32 - 1)
    let header4 = HeaderBuilder::default()
        .epoch(0xFFFF_FFFF) // 2^32 - 1 (maximum safe value)
        .round(0x1234)
        .author(AuthorityIdentifier::default())
        .payload(IndexMap::new())
        .parents(BTreeSet::new())
        .latest_execution_block(Default::default())
        .build();
    
    let nonce4 = header4.nonce();
    
    println!("Test Results:");
    println!("Nonce 1 (epoch u32::MAX): 0x{:016x}", nonce1);
    println!("Nonce 2 (epoch 0):        0x{:016x}", nonce2);
    println!("Nonce 3 (epoch 1):        0x{:016x}", nonce3);
    println!("Nonce 4 (epoch 2^32-1):   0x{:016x}", nonce4);

    // Check for overflow behavior
    println!("\nOverflow Analysis:");

    // Demonstrate the vulnerability with bit shifting
    let max_epoch_as_u64 = u32::MAX as u64;
    let shifted_max = max_epoch_as_u64 << 32;
    let expected_max_nonce = shifted_max | 0x1234u64;

    println!("Max epoch as u64: 0x{:016x}", max_epoch_as_u64);
    println!("After left shift: 0x{:016x}", shifted_max);
    println!("Expected nonce:   0x{:016x}", expected_max_nonce);

    if nonce1 == expected_max_nonce {
        println!("✓ Maximum epoch calculation works as expected");
    } else {
        println!("✗ POTENTIAL ISSUE: Unexpected nonce calculation");
        println!("  Expected: 0x{:016x}", expected_max_nonce);
        println!("  Actual:   0x{:016x}", nonce1);
    }
    
    // Check for nonce collision
    if nonce2 == nonce3 {
        println!("✗ CRITICAL: Nonce collision detected!");
        println!("  Epoch {} and Epoch {} produce identical nonce: 0x{:016x}", 
                 header2.epoch(), header3.epoch(), nonce2);
    } else {
        println!("✓ No nonce collision between epoch {} and epoch {}", 
                 header2.epoch(), header3.epoch());
    }
    
    // Verify maximum safe epoch works correctly
    let expected_max_safe = (0xFFFF_FFFFu64 << 32) | 0x1234u64;
    if nonce4 == expected_max_safe {
        println!("✓ Maximum safe epoch (2^32-1) works correctly: 0x{:016x}", nonce4);
    } else {
        println!("✗ Maximum safe epoch calculation incorrect");
        println!("  Expected: 0x{:016x}", expected_max_safe);
        println!("  Actual:   0x{:016x}", nonce4);
    }
    
    // Test different round values with maximum epoch
    println!("\nTesting different rounds with maximum epoch:");
    for round in [0x0, 0x1, 0xFFFF, 0xFFFF_FFFF] {
        let header = HeaderBuilder::default()
            .epoch(u32::MAX) // Maximum epoch
            .round(round)
            .author(AuthorityIdentifier::default())
            .payload(IndexMap::new())
            .parents(BTreeSet::new())
            .latest_execution_block(Default::default())
            .build();

        let nonce = header.nonce();
        println!("  Round 0x{:08x} -> Nonce 0x{:016x}", round, nonce);

        // Calculate expected nonce
        let expected = ((u32::MAX as u64) << 32) | (round as u64);
        if nonce == expected {
            println!("    ✓ Nonce calculation correct");
        } else {
            println!("    ✗ Nonce calculation incorrect");
        }
    }
    
    println!("\n=== Vulnerability Summary ===");
    println!("1. Integer overflow occurs when epoch >= 2^32");
    println!("2. Overflow causes epoch bits to be lost in left shift operation");
    println!("3. Different (epoch, round) pairs can produce identical nonces");
    println!("4. This can lead to consensus disruption and block ordering issues");
    println!("5. Recommendation: Add bounds checking for epoch < 2^32");
}

#[test]
fn test_nonce_collision_scenarios() {
    println!("=== Testing Nonce Collision Scenarios ===");
    
    // Scenario 1: Test potential collision patterns
    let collision_pairs = vec![
        (u32::MAX, 0x1234u32),     // Maximum epoch
        (0, 0x1234u32),            // Zero epoch
        (1, 0x1234u32),            // Small epoch
    ];

    let mut nonces = Vec::new();

    for (epoch, round) in collision_pairs {
        let header = HeaderBuilder::default()
            .epoch(epoch)
            .round(round)
            .author(AuthorityIdentifier::default())
            .payload(IndexMap::new())
            .parents(BTreeSet::new())
            .latest_execution_block(Default::default())
            .build();

        let nonce = header.nonce();
        nonces.push((epoch as u64, round as u64, nonce));
        println!("Epoch: 0x{:08x}, Round: 0x{:08x} -> Nonce: 0x{:016x}",
                 epoch, round, nonce);
    }
    
    // Check for collisions
    for i in 0..nonces.len() {
        for j in i+1..nonces.len() {
            let (epoch1, round1, nonce1) = nonces[i];
            let (epoch2, round2, nonce2) = nonces[j];
            
            if nonce1 == nonce2 {
                println!("✗ COLLISION DETECTED:");
                println!("  ({}, {}) -> 0x{:016x}", epoch1, round1, nonce1);
                println!("  ({}, {}) -> 0x{:016x}", epoch2, round2, nonce2);
            }
        }
    }
}

#[test]
fn test_safe_nonce_calculation_bounds() {
    println!("=== Testing Safe Nonce Calculation Bounds ===");
    
    // Test maximum safe values
    let max_safe_epoch = u32::MAX;
    let max_safe_round = u32::MAX;

    let header = HeaderBuilder::default()
        .epoch(max_safe_epoch)
        .round(max_safe_round)
        .author(AuthorityIdentifier::default())
        .payload(IndexMap::new())
        .parents(BTreeSet::new())
        .latest_execution_block(Default::default())
        .build();

    let nonce = header.nonce();
    let expected = ((max_safe_epoch as u64) << 32) | (max_safe_round as u64);

    println!("Maximum safe epoch: 0x{:08x}", max_safe_epoch);
    println!("Maximum safe round: 0x{:08x}", max_safe_round);
    println!("Calculated nonce:   0x{:016x}", nonce);
    println!("Expected nonce:     0x{:016x}", expected);

    if nonce == expected {
        println!("✓ Maximum safe values work correctly");
    } else {
        println!("✗ Maximum safe values calculation failed");
    }

    // Test boundary condition - demonstrate the vulnerability concept
    println!("\nBoundary test:");
    println!("Current implementation uses u32 for epoch/round, preventing direct overflow");
    println!("However, the vulnerability exists in the mathematical operation:");

    // Simulate what would happen with larger values
    let simulated_large_epoch = 0x1_0000_0000u64; // This would be 2^32
    let simulated_nonce = (simulated_large_epoch << 32) | 0x1234u64;
    println!("If epoch could be 0x{:016x}, nonce would be: 0x{:016x}",
             simulated_large_epoch, simulated_nonce);

    // Show the overflow
    let overflowed = simulated_nonce & 0xFFFF_FFFF_FFFF_FFFFu64;
    println!("After overflow (64-bit): 0x{:016x}", overflowed);
}
