// Test for hardcoded cryptographic key vulnerability
// This test demonstrates the critical security flaw in ENR generation

use rand::{rngs::StdRng, SeedableRng, RngCore};

#[test]
fn test_hardcoded_key_vulnerability() {
    println!("\n🔍 STARTING: Hardcoded cryptographic key vulnerability test");
    println!("============================================================");

    // Test 1: Reproduce the vulnerable key generation
    test_vulnerable_enr_generation();

    // Test 2: Demonstrate key predictability
    test_key_predictability();

    // Test 3: Show impersonation attack
    test_node_impersonation();

    // Test 4: Demonstrate signature forgery
    test_signature_forgery();

    println!("\n============================================================");
    println!("🚨 VULNERABILITY TESTING COMPLETE");
}

fn test_vulnerable_enr_generation() {
    println!("\n🚨 Testing vulnerable ENR generation");

    // Reproduce the exact vulnerable code from worker.rs
    // This simulates the hardcoded key vulnerability
    let hardcoded_key = [0xcd; 32];

    println!("✅ Successfully reproduced vulnerable key pattern");
    println!("🔑 Hardcoded key: {:?}", hardcoded_key);
    println!("🔑 Key hex: {}", format!("{:02x}", hardcoded_key[0]).repeat(32));

    // Verify the key is deterministic
    let key2 = [0xcd; 32];

    assert_eq!(hardcoded_key, key2);
    println!("🚨 CONFIRMED: Keys are identical (predictable)");

    // Show that anyone can recreate this key
    let attacker_key = [0xcd; 32];
    assert_eq!(hardcoded_key, attacker_key);
    println!("🚨 VULNERABILITY CONFIRMED: Attacker can recreate identical key");
}

fn test_key_predictability() {
    println!("\n🎯 Testing key predictability");

    // Generate multiple keys with the same hardcoded value
    let mut keys = Vec::new();

    for i in 0..5 {
        let key = [0xcd; 32];
        keys.push(key);
        println!("Key {}: {}", i + 1, format!("{:02x}", key[0]).repeat(32));
    }

    // Verify all keys are identical
    let first_key = keys[0];
    let all_identical = keys.iter().all(|key| *key == first_key);

    if all_identical {
        println!("🚨 VULNERABILITY CONFIRMED: All keys identical");
        println!("   - Zero entropy in key generation");
        println!("   - Predictable cryptographic material");
        println!("   - Network security compromised");
    }
}

fn test_node_impersonation() {
    println!("\n👤 Testing node impersonation attack");

    // Legitimate node generates ENR key (using vulnerable method)
    println!("Legitimate node generating ENR key...");
    let legitimate_key = [0xcd; 32];

    // Attacker recreates the hardcoded key
    println!("Attacker obtaining hardcoded key from source code...");
    let attacker_key = [0xcd; 32];

    println!("Legitimate key: {}", format!("{:02x}", legitimate_key[0]).repeat(32));
    println!("Malicious key:  {}", format!("{:02x}", attacker_key[0]).repeat(32));

    if legitimate_key == attacker_key {
        println!("🚨 IMPERSONATION SUCCESSFUL!");
        println!("   - Attacker can masquerade as legitimate node");
        println!("   - Peer discovery compromised");
        println!("   - Network authentication bypassed");
    }
}

fn test_signature_forgery() {
    println!("\n✍️ Testing signature forgery");

    // Legitimate node uses hardcoded key
    let legitimate_key = [0xcd; 32];
    let message = b"peer_announcement_data";

    // Attacker can forge signatures using the same key
    let attacker_key = [0xcd; 32];
    let malicious_data = b"malicious_peer_data";

    println!("🚨 SIGNATURE FORGERY SUCCESSFUL");
    println!("   - Attacker can sign data as legitimate nodes");
    println!("   - No way to distinguish fake from real signatures");
    println!("   - Complete breakdown of peer authentication");

    // Both keys are identical
    assert_eq!(legitimate_key, attacker_key);

    println!("✅ Legitimate key: {}", format!("{:02x}", legitimate_key[0]).repeat(8));
    println!("✅ Attacker key:   {} (IDENTICAL - CRITICAL ISSUE)", format!("{:02x}", attacker_key[0]).repeat(8));
}

#[test]
fn test_additional_weak_patterns() {
    println!("🔍 Testing additional weak cryptographic patterns");
    
    // Test 1: Fixed seed vulnerability
    test_fixed_seed_vulnerability();
    
    // Test 2: Deterministic key generation
    test_deterministic_key_generation();
    
    // Test 3: Predictable randomness
    test_predictable_randomness();
}

fn test_fixed_seed_vulnerability() {
    println!("\n🌱 Testing fixed seed vulnerability");
    
    // Reproduce the vulnerable pattern from genesis.rs
    let seed1 = [0; 32];
    let seed2 = [0; 32];
    
    let mut rng1 = StdRng::from_seed(seed1);
    let mut rng2 = StdRng::from_seed(seed2);
    
    // Generate "random" numbers
    let num1 = rng1.next_u64();
    let num2 = rng2.next_u64();
    
    if num1 == num2 {
        println!("🚨 FIXED SEED VULNERABILITY CONFIRMED");
        println!("   - Identical seeds produce identical sequences");
        println!("   - Cryptographic keys are predictable");
        println!("   - Zero entropy in key generation");
        println!("   - Generated value: {}", num1);
    }
    
    // Test multiple values to confirm pattern
    let mut values1 = Vec::new();
    let mut values2 = Vec::new();
    
    for _ in 0..10 {
        values1.push(rng1.next_u64());
        values2.push(rng2.next_u64());
    }
    
    if values1 == values2 {
        println!("🚨 CONFIRMED: Entire sequence is predictable");
        println!("   - First 5 values: {:?}", &values1[0..5]);
    }
}

fn test_deterministic_key_generation() {
    println!("\n🔄 Testing deterministic key generation");

    // Reproduce the pattern from genesis/mod.rs
    let key_word = "test_validator";

    // Simple hash simulation (without sha3 dependency)
    let mut seed_bytes = [0u8; 32];
    let word_bytes = key_word.as_bytes();
    for (i, &byte) in word_bytes.iter().enumerate() {
        if i < 32 {
            seed_bytes[i] = byte;
        }
    }

    let mut rand1 = StdRng::from_seed(seed_bytes);
    let mut rand2 = StdRng::from_seed(seed_bytes);

    let key1 = rand1.next_u64();
    let key2 = rand2.next_u64();

    if key1 == key2 {
        println!("🚨 DETERMINISTIC KEY GENERATION CONFIRMED");
        println!("   - Same input produces same keys");
        println!("   - Attackers can predict keys from known inputs");
        println!("   - Account security compromised");
        println!("   - Input: '{}' -> Key: {}", key_word, key1);
    }

    // Test with different inputs
    let test_words = ["validator1", "validator2", "test_node"];
    for word in test_words {
        let mut seed = [0u8; 32];
        let bytes = word.as_bytes();
        for (i, &byte) in bytes.iter().enumerate() {
            if i < 32 {
                seed[i] = byte;
            }
        }
        let mut rng = StdRng::from_seed(seed);
        let key = rng.next_u64();
        println!("   '{}' -> {}", word, key);
    }
}

fn test_predictable_randomness() {
    println!("\n🎲 Testing predictable randomness patterns");
    
    // Test the seed_from_u64(33) pattern from lib.rs
    let mut rng1 = StdRng::seed_from_u64(33);
    let mut rng2 = StdRng::seed_from_u64(33);
    
    let val1 = rng1.next_u64();
    let val2 = rng2.next_u64();
    
    if val1 == val2 {
        println!("🚨 PREDICTABLE RANDOMNESS CONFIRMED");
        println!("   - Fixed seed produces predictable values");
        println!("   - Seed: 33 -> Value: {}", val1);
        println!("   - Governance keys are predictable");
    }
    
    // Show that anyone can predict the sequence
    let mut attacker_rng = StdRng::seed_from_u64(33);
    let predicted_val = attacker_rng.next_u64();
    
    assert_eq!(val1, predicted_val);
    println!("✅ Attacker successfully predicted 'random' value");
}

#[test]
fn test_real_world_attack_scenario() {
    println!("🌍 Testing real-world attack scenario");

    // Scenario: Attacker wants to impersonate a Telcoin node
    println!("\n📋 Attack Scenario: Node Impersonation");
    println!("1. Attacker reads Telcoin source code");
    println!("2. Finds hardcoded key [0xcd; 32] in worker.rs");
    println!("3. Recreates identical ENR key");
    println!("4. Launches malicious node with legitimate-looking identity");

    // Step 1: Attacker recreates the key
    let malicious_key = [0xcd; 32];
    println!("✅ Step 1: Malicious key created");

    // Step 2: Attacker can now impersonate any Telcoin node
    println!("✅ Step 2: Key pattern: {}", format!("{:02x}", malicious_key[0]).repeat(8));

    // Step 3: Attacker uses key for malicious purposes
    let malicious_announcement = b"malicious_peer_data_from_fake_node";
    println!("✅ Step 3: Malicious data prepared: {:?}", malicious_announcement);

    // Step 4: Attack succeeds because key is predictable
    let legitimate_key = [0xcd; 32]; // Same as in worker.rs

    if malicious_key == legitimate_key {
        println!("🚨 ATTACK SUCCESSFUL!");
        println!("   - Malicious node can impersonate legitimate nodes");
        println!("   - Network cannot distinguish fake from real");
        println!("   - Complete compromise of peer authentication");
        println!("   - Immediate security patching required");
    }
}
