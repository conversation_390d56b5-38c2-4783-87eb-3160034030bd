use tempfile::TempDir;
use tn_types::{Task<PERSON>anager, U256, Address, Bytes, Batch, test_genesis, BatchValidation, max_batch_gas};
use tn_reth::{test_utils::TransactionFactory, RethChainSpec};
use std::sync::Arc;

// استيراد BatchValidator من الكريت الفعلي
use tn_batch_validator::BatchValidator;

// تعريف TestTools وtest_tools محليًا (من كود الاختبار الأصلي)
struct TestTools {
    pub validator: BatchValidator,
    pub valid_batch: Batch,
}

async fn test_tools(path: &std::path::Path, task_manager: &TaskManager) -> TestTools {
    use tn_reth::{RethEnv, test_utils::TransactionFactory};
    use tn_types::{Batch, Address, U256, test_genesis};
    use std::sync::Arc;

    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), path, task_manager).unwrap();
    let tx_pool = reth_env.init_txn_pool().unwrap();
    let validator = BatchValidator::new(reth_env, Some(tx_pool), 0, Default::default());

    let timestamp = chain.genesis_timestamp() + 1;
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let genesis_hash = chain.genesis_hash();
    let transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        None,
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    let valid_batch = Batch {
        transactions: vec![transaction],
        parent_hash: genesis_hash,
        beneficiary: Address::ZERO,
        timestamp,
        base_fee_per_gas: Some(1),
        worker_id: 0,
        received_at: None,
    };
    TestTools { validator, valid_batch }
}

#[tokio::test]
async fn test_excessive_gas_limit_dos() {
    println!("=== Testing Excessive Gas Limit DoS Attack ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملة بحد غاز يتجاوز الحد الأقصى للدفعة
    let max_gas = max_batch_gas(valid_batch.timestamp);
    let excessive_gas_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(max_gas + 1), // يتجاوز الحد الأقصى
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    let excessive_gas_batch = Batch {
        transactions: vec![excessive_gas_transaction],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    println!("Transaction gas limit: {}, Max batch gas: {}", max_gas + 1, max_gas);

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(excessive_gas_batch.seal_slow());
    
    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_err() {
        println!("✓ Gas limit validation working correctly");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly rejects excessive gas limits");
    } else {
        println!("✗ Vulnerability confirmed: Excessive gas limit accepted");
        println!("  Impact: Memory exhaustion, DoS potential");
        println!("  Recommendation: Add individual transaction gas limits");
    }
}

#[tokio::test]
async fn test_individual_transaction_gas_limit() {
    println!("=== Testing Individual Transaction Gas Limit ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملة بحد غاز عالي لكن ضمن حد الدفعة
    let high_gas_per_tx = 15_000_000u64; // 15M gas per transaction
    let high_gas_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(high_gas_per_tx),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    let high_gas_batch = Batch {
        transactions: vec![high_gas_transaction],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    println!("Individual transaction gas limit: {}", high_gas_per_tx);

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(high_gas_batch.seal_slow());
    
    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_ok() {
        println!("✗ Vulnerability confirmed: High individual gas limit accepted");
        println!("  Impact: Single transaction can consume excessive resources");
        println!("  Recommendation: Add MAX_GAS_PER_TRANSACTION limit");
    } else {
        println!("✓ Individual gas limit validation working correctly");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly limits individual transaction gas");
    }
}
