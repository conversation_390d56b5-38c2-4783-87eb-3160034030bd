use tempfile::TempDir;
use tn_types::{TaskManager, U256, Address, Bytes, Batch, test_genesis, BatchValidation, TransactionTrait};
use tn_reth::{test_utils::TransactionFactory, RethChainSpec, recover_raw_transaction};
use std::sync::Arc;

// استيراد BatchValidator من الكريت الفعلي
use tn_batch_validator::BatchValidator;

// تعريف TestTools وtest_tools محليًا (من كود الاختبار الأصلي)
struct TestTools {
    pub validator: <PERSON>chValidator,
    pub valid_batch: Batch,
}

async fn test_tools(path: &std::path::Path, task_manager: &TaskManager) -> TestTools {
    use tn_reth::{RethEnv, test_utils::TransactionFactory};
    use tn_types::{Batch, Address, U256, test_genesis};
    use std::sync::Arc;

    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), path, task_manager).unwrap();
    let tx_pool = reth_env.init_txn_pool().unwrap();
    let validator = BatchValidator::new(reth_env, Some(tx_pool), 0, Default::default());

    let timestamp = chain.genesis_timestamp() + 1;
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let genesis_hash = chain.genesis_hash();
    let transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        None,
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    let valid_batch = Batch {
        transactions: vec![transaction],
        parent_hash: genesis_hash,
        beneficiary: Address::ZERO,
        timestamp,
        base_fee_per_gas: Some(1),
        worker_id: 0,
        received_at: None,
    };
    TestTools { validator, valid_batch }
}

#[tokio::test]
async fn test_invalid_nonce_sequence_dos() {
    println!("=== Testing Invalid Nonce Sequence DoS Attack ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملات بتسلسل nonce غير صحيح
    let tx1 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    // تخطي nonce (إنشاء فجوة)
    tx_factory.inc_nonce();
    tx_factory.inc_nonce();

    let tx2 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    // فك تشفير المعاملات للوصول إلى nonce
    let decoded_tx1 = recover_raw_transaction(&tx1).unwrap().into_inner();
    let decoded_tx2 = recover_raw_transaction(&tx2).unwrap().into_inner();

    println!("Transaction 1 nonce: {}", decoded_tx1.nonce());
    println!("Transaction 2 nonce: {}", decoded_tx2.nonce());
    println!("Nonce gap detected: {} -> {}", decoded_tx1.nonce(), decoded_tx2.nonce());

    let invalid_nonce_batch = Batch {
        transactions: vec![tx1, tx2],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(invalid_nonce_batch.seal_slow());
    
    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_ok() {
        println!("✗ Vulnerability confirmed: Invalid nonce sequences pass validation");
        println!("  Impact: Resource exhaustion, wasted computation");
        println!("  Recommendation: Add nonce sequence validation in validate_batch()");
    } else {
        println!("✓ Nonce validation working correctly");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly rejects invalid nonce sequences");
    }
}

#[tokio::test]
async fn test_duplicate_nonce_dos() {
    println!("=== Testing Duplicate Nonce DoS Attack ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملتين بنفس nonce
    let tx1 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    // فك تشفير المعاملة الأولى للحصول على nonce
    let decoded_tx1 = recover_raw_transaction(&tx1).unwrap().into_inner();

    // إعادة تعيين nonce لإنشاء معاملة مكررة
    tx_factory.set_nonce(decoded_tx1.nonce());
    
    let tx2 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value + U256::from(1000), // قيمة مختلفة لكن نفس nonce
        Bytes::new(),
    );

    // فك تشفير المعاملات للوصول إلى nonce
    let decoded_tx1_dup = recover_raw_transaction(&tx1).unwrap().into_inner();
    let decoded_tx2_dup = recover_raw_transaction(&tx2).unwrap().into_inner();

    println!("Transaction 1 nonce: {}", decoded_tx1_dup.nonce());
    println!("Transaction 2 nonce: {}", decoded_tx2_dup.nonce());
    println!("Duplicate nonce detected: both transactions have nonce {}", decoded_tx1_dup.nonce());

    let duplicate_nonce_batch = Batch {
        transactions: vec![tx1, tx2],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(duplicate_nonce_batch.seal_slow());
    
    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_ok() {
        println!("✗ Vulnerability confirmed: Duplicate nonce transactions pass validation");
        println!("  Impact: Only one transaction will succeed, wasting resources");
        println!("  Recommendation: Add duplicate nonce detection in validate_batch()");
    } else {
        println!("✓ Duplicate nonce validation working correctly");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly rejects duplicate nonce transactions");
    }
}

#[tokio::test]
async fn test_valid_nonce_sequence() {
    println!("=== Testing Valid Nonce Sequence (Control Test) ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملات بتسلسل nonce صحيح
    let tx1 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    let tx2 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    // فك تشفير المعاملات للوصول إلى nonce
    let decoded_tx1_valid = recover_raw_transaction(&tx1).unwrap().into_inner();
    let decoded_tx2_valid = recover_raw_transaction(&tx2).unwrap().into_inner();

    println!("Transaction 1 nonce: {}", decoded_tx1_valid.nonce());
    println!("Transaction 2 nonce: {}", decoded_tx2_valid.nonce());
    println!("Valid nonce sequence: {} -> {}", decoded_tx1_valid.nonce(), decoded_tx2_valid.nonce());

    let valid_nonce_batch = Batch {
        transactions: vec![tx1, tx2],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(valid_nonce_batch.seal_slow());
    
    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_ok() {
        println!("✓ Valid nonce sequence accepted correctly");
    } else {
        println!("✗ Valid nonce sequence rejected unexpectedly");
        println!("  Result: {:?}", result.err().unwrap());
    }
}
