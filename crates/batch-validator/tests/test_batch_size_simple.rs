use tempfile::TempDir;
use tn_types::{TaskManager, U256, Address, Bytes, Batch, test_genesis, max_batch_size, BatchValidation};
use tn_reth::{test_utils::TransactionFactory, RethChainSpec};
use std::sync::Arc;
use tn_batch_validator::BatchValidator;

#[tokio::test]
async fn test_batch_size_validation() {
    println!("=== Testing Batch Size Validation ===");

    // إعداد البيئة
    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = tn_reth::RethEnv::new_for_temp_chain(chain.clone(), tmp_dir.path(), &task_manager).unwrap();
    let tx_pool = reth_env.init_txn_pool().unwrap();
    let validator = BatchValidator::new(reth_env, Some(tx_pool), 0, Default::default());

    let timestamp = chain.genesis_timestamp() + 1;
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let genesis_hash = chain.genesis_hash();

    // الحصول على الحد الأقصى الحالي
    let max_size = max_batch_size(timestamp);
    println!("Max batch size: {} bytes ({:.2} MB)", max_size, max_size as f64 / (1024.0 * 1024.0));

    // اختبار 1: دفعة عادية صغيرة
    let normal_tx = tx_factory.create_eip1559_encoded(
        chain.clone(),
        None,
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    let normal_batch = Batch {
        transactions: vec![normal_tx],
        parent_hash: genesis_hash,
        beneficiary: Address::ZERO,
        timestamp,
        base_fee_per_gas: Some(1),
        worker_id: 0,
        received_at: None,
    };

    println!("Normal batch size: {} bytes", normal_batch.transactions[0].len());
    let result = validator.validate_batch(normal_batch.seal_slow());
    println!("Normal batch validation: {:?}", result.is_ok());

    // اختبار 2: دفعة كبيرة (95% من الحد الأقصى)
    let large_data = vec![0u8; (max_size as f64 * 0.8) as usize]; // 80% من الحد الأقصى
    let large_tx = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000 + (large_data.len() * 16) as u64),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::from(large_data),
    );

    let large_batch = Batch {
        transactions: vec![large_tx.clone()],
        parent_hash: genesis_hash,
        beneficiary: Address::ZERO,
        timestamp,
        base_fee_per_gas: Some(7),
        worker_id: 0,
        received_at: None,
    };

    println!("Large batch size: {} bytes ({:.2} MB)", large_tx.len(), large_tx.len() as f64 / (1024.0 * 1024.0));
    let result = validator.validate_batch(large_batch.seal_slow());
    println!("Large batch validation: {:?}", result.is_ok());

    if result.is_ok() {
        println!("✗ Vulnerability confirmed: Large batch passes validation");
        println!("  Impact: Memory and bandwidth exhaustion possible");
    } else {
        println!("✓ Large batch correctly rejected");
    }

    // اختبار 3: دفعة تتجاوز الحد الأقصى
    let oversized_data = vec![0u8; max_size + 1000];
    let oversized_tx = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000 + (oversized_data.len() * 16) as u64),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::from(oversized_data),
    );

    let oversized_batch = Batch {
        transactions: vec![oversized_tx.clone()],
        parent_hash: genesis_hash,
        beneficiary: Address::ZERO,
        timestamp,
        base_fee_per_gas: Some(7),
        worker_id: 0,
        received_at: None,
    };

    println!("Oversized batch size: {} bytes", oversized_tx.len());
    let result = validator.validate_batch(oversized_batch.seal_slow());
    println!("Oversized batch validation: {:?}", result.is_ok());

    if result.is_err() {
        println!("✓ Oversized batch correctly rejected");
    } else {
        println!("✗ Oversized batch unexpectedly accepted");
    }

    println!("=== Batch Size Validation Test Complete ===");
}
