use tempfile::TempDir;
use tn_types::{Task<PERSON>anager, U256, Address, Bytes, Batch, test_genesis, BatchValidation, max_batch_size};
use tn_reth::{test_utils::TransactionFactory, RethChainSpec};
use std::sync::Arc;

// استيراد BatchValidator من الكريت الفعلي
use tn_batch_validator::BatchValidator;

// تعريف TestTools وtest_tools محليًا (من كود الاختبار الأصلي)
struct TestTools {
    pub validator: BatchValidator,
    pub valid_batch: Batch,
}

async fn test_tools(path: &std::path::Path, task_manager: &TaskManager) -> TestTools {
    use tn_reth::{RethEnv, test_utils::TransactionFactory};
    use tn_types::{Batch, Address, U256, test_genesis};
    use std::sync::Arc;

    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), path, task_manager).unwrap();
    let tx_pool = reth_env.init_txn_pool().unwrap();
    let validator = BatchValidator::new(reth_env, Some(tx_pool), 0, Default::default());

    let timestamp = chain.genesis_timestamp() + 1;
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let genesis_hash = chain.genesis_hash();
    let transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        None,
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    let valid_batch = Batch {
        transactions: vec![transaction],
        parent_hash: genesis_hash,
        beneficiary: Address::ZERO,
        timestamp,
        base_fee_per_gas: Some(1),
        worker_id: 0,
        received_at: None,
    };
    TestTools { validator, valid_batch }
}

#[tokio::test]
async fn test_large_batch_size_dos() {
    println!("=== Testing Large Batch Size DoS Attack ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // الحصول على الحد الأقصى الحالي
    let max_size = max_batch_size(valid_batch.timestamp);
    println!("Current max batch size: {} bytes ({:.2} MB)", max_size, max_size as f64 / (1024.0 * 1024.0));

    // إنشاء معاملات كبيرة لملء معظم الحد الأقصى
    let mut large_transactions = Vec::new();
    let mut total_size = 0;
    let target_size = (max_size as f64 * 0.95) as usize; // 95% من الحد الأقصى

    // إنشاء معاملات كبيرة بـ data كبيرة
    while total_size < target_size {
        // إنشاء data كبيرة (10KB)
        let large_data = vec![0u8; 10 * 1024]; // 10KB of data
        
        let tx = tx_factory.create_eip1559_encoded(
            chain.clone(),
            Some(21000 + (large_data.len() * 16) as u64), // gas for data
            gas_price,
            Some(Address::ZERO),
            value,
            Bytes::from(large_data),
        );

        total_size += tx.len();
        large_transactions.push(tx);

        // تجنب تجاوز الحد الأقصى
        if total_size > target_size {
            break;
        }
    }

    println!("Created {} large transactions", large_transactions.len());
    println!("Total batch size: {} bytes ({:.2} MB)", total_size, total_size as f64 / (1024.0 * 1024.0));
    println!("Percentage of max size: {:.1}%", (total_size as f64 / max_size as f64) * 100.0);

    let large_batch = Batch {
        transactions: large_transactions,
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7),
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(large_batch.seal_slow());
    
    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_ok() {
        println!("✗ Vulnerability confirmed: Large batch ({:.2} MB) passes validation", total_size as f64 / (1024.0 * 1024.0));
        println!("  Impact: Memory and bandwidth exhaustion");
        println!("  Recommendation: Reduce max batch size and add transaction count limits");
    } else {
        println!("✓ Large batch validation working correctly");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly rejects oversized batches");
    }
}

#[tokio::test]
async fn test_maximum_batch_size_boundary() {
    println!("=== Testing Maximum Batch Size Boundary ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // الحصول على الحد الأقصى الحالي
    let max_size = max_batch_size(valid_batch.timestamp);
    
    // إنشاء معاملة واحدة كبيرة جداً تتجاوز الحد الأقصى
    let oversized_data = vec![0u8; max_size + 1000]; // تجاوز الحد بـ 1000 بايت
    
    let oversized_tx = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000 + (oversized_data.len() * 16) as u64),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::from(oversized_data),
    );

    println!("Oversized transaction size: {} bytes", oversized_tx.len());
    println!("Max allowed size: {} bytes", max_size);
    println!("Excess size: {} bytes", oversized_tx.len() - max_size);

    let oversized_batch = Batch {
        transactions: vec![oversized_tx],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7),
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(oversized_batch.seal_slow());
    
    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_err() {
        println!("✓ Oversized batch correctly rejected");
        println!("  System properly enforces size limits");
    } else {
        println!("✗ Oversized batch unexpectedly accepted");
        println!("  This should not happen - size validation may be broken");
    }
}

#[tokio::test]
async fn test_many_small_transactions_dos() {
    println!("=== Testing Many Small Transactions DoS Attack ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(1000); // قيمة صغيرة
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء عدد كبير من المعاملات الصغيرة
    let mut many_transactions = Vec::new();
    let mut total_size = 0;
    let max_size = max_batch_size(valid_batch.timestamp);
    
    // إنشاء معاملات صغيرة حتى نقترب من الحد الأقصى
    while total_size < max_size - 200 { // ترك مساحة للمعاملة الأخيرة
        let tx = tx_factory.create_eip1559_encoded(
            chain.clone(),
            Some(21000), // الحد الأدنى للغاز
            gas_price,
            Some(Address::ZERO),
            value,
            Bytes::new(), // بدون data إضافية
        );

        total_size += tx.len();
        many_transactions.push(tx);

        if many_transactions.len() > 10000 { // حد أمان لتجنب حلقة لا نهائية
            break;
        }
    }

    println!("Created {} small transactions", many_transactions.len());
    println!("Total batch size: {} bytes ({:.2} MB)", total_size, total_size as f64 / (1024.0 * 1024.0));
    println!("Average transaction size: {:.1} bytes", total_size as f64 / many_transactions.len() as f64);

    let many_tx_batch = Batch {
        transactions: many_transactions,
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7),
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    // حفظ عدد المعاملات قبل استهلاك many_tx_batch
    let transaction_count = many_tx_batch.transactions.len();

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(many_tx_batch.seal_slow());

    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_ok() {
        println!("✗ Vulnerability confirmed: Batch with {} transactions passes validation", transaction_count);
        println!("  Impact: Processing overhead and potential DoS");
        println!("  Recommendation: Add maximum transaction count per batch");
    } else {
        println!("✓ Many transactions validation working correctly");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly limits transaction count");
    }
}
