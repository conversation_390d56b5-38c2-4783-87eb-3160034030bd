//! Test for clock drift and timestamp validation behavior

use tn_types::{<PERSON><PERSON>, Address, BatchValidation, test_genesis, Encodable2718, now};
use tn_types::gas_accumulator::BaseFeeContainer;
use tn_batch_validator::BatchValidator;
use tn_reth::{RethEnv, RethChainSpec};
use tn_reth::test_utils::TransactionFactory;
use std::sync::Arc;
use tempfile::TempDir;
use tn_types::TaskManager;

fn create_test_batch_with_timestamp(chain: &RethChainSpec, timestamp: u64) -> Batch {
    // Create a simple transaction for testing
    let mut tx_factory = TransactionFactory::new();
    let tx = tx_factory
        .create_explicit_eip1559(
            Some(chain.chain.id()),
            None,
            None,
            Some(7),
            Some(21000),
            Some(Address::random()),
            Some(tn_types::U256::from(100)),
            None,
            None,
        )
        .encoded_2718();
    
    // Create batch with custom timestamp
    Batch {
        transactions: vec![tx],
        parent_hash: chain.genesis_hash(),
        beneficiary: Address::ZERO,
        timestamp,
        base_fee_per_gas: Some(tn_types::MIN_PROTOCOL_BASE_FEE),
        worker_id: 0,
        received_at: None,
    }
}

#[tokio::test]
async fn test_timestamp_validation_behavior() {
    println!("🔍 Testing timestamp validation and clock drift behavior");
    
    // Create test environment
    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), tmp_dir.path(), &task_manager).unwrap();
    
    let validator = BatchValidator::new(
        reth_env.clone(),
        None,
        0,
        BaseFeeContainer::default()
    );
    
    // Get current time and genesis timestamp
    let current_time = now();
    let genesis_timestamp = chain.genesis().timestamp;
    
    println!("⏰ Current time: {}", current_time);
    println!("⏰ Genesis timestamp: {}", genesis_timestamp);
    
    // Test 1: Valid batch with timestamp > parent (genesis)
    let valid_timestamp = genesis_timestamp + 10;
    let valid_batch = create_test_batch_with_timestamp(&chain, valid_timestamp);
    println!("✅ Testing valid batch with timestamp: {}", valid_timestamp);
    
    let result = validator.validate_batch(valid_batch.seal_slow());
    assert!(result.is_ok(), "Valid batch should be accepted");
    println!("✅ Valid batch accepted");
    
    // Test 2: Invalid batch with timestamp = parent timestamp
    let same_timestamp = genesis_timestamp;
    let same_time_batch = create_test_batch_with_timestamp(&chain, same_timestamp);
    println!("🧪 Testing batch with same timestamp as parent: {}", same_timestamp);
    
    let result = validator.validate_batch(same_time_batch.seal_slow());
    assert!(result.is_err(), "Batch with same timestamp should be rejected");
    if let Err(e) = result {
        println!("⚠️  Batch with same timestamp rejected: {:?}", e);
    }
    
    // Test 3: Invalid batch with timestamp < parent timestamp
    let past_timestamp = genesis_timestamp - 1;
    let past_batch = create_test_batch_with_timestamp(&chain, past_timestamp);
    println!("🧪 Testing batch with past timestamp: {}", past_timestamp);
    
    let result = validator.validate_batch(past_batch.seal_slow());
    assert!(result.is_err(), "Batch with past timestamp should be rejected");
    if let Err(e) = result {
        println!("⚠️  Batch with past timestamp rejected: {:?}", e);
    }
    
    // Test 4: Batch with future timestamp (simulating clock drift)
    let future_timestamp = current_time + 3600; // 1 hour in future
    let future_batch = create_test_batch_with_timestamp(&chain, future_timestamp);
    println!("🧪 Testing batch with future timestamp: {}", future_timestamp);
    
    let result = validator.validate_batch(future_batch.seal_slow());
    // Note: BatchValidator only checks against parent, not current time
    // So this should pass if future_timestamp > genesis_timestamp
    if result.is_ok() {
        println!("⚠️  Batch with future timestamp accepted - potential clock drift issue!");
    } else {
        println!("✅ Batch with future timestamp rejected: {:?}", result.unwrap_err());
    }
    
    // Test 5: Extreme future timestamp
    let extreme_future = current_time + 86400 * 365; // 1 year in future
    let extreme_batch = create_test_batch_with_timestamp(&chain, extreme_future);
    println!("🧪 Testing batch with extreme future timestamp: {}", extreme_future);
    
    let result = validator.validate_batch(extreme_batch.seal_slow());
    if result.is_ok() {
        println!("⚠️  Batch with extreme future timestamp accepted - major clock drift vulnerability!");
    } else {
        println!("✅ Batch with extreme future timestamp rejected: {:?}", result.unwrap_err());
    }
    
    println!("🔍 Timestamp validation test completed");
}

#[tokio::test]
async fn test_clock_drift_scenarios() {
    println!("🔍 Testing various clock drift scenarios");
    
    // Create test environment
    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), tmp_dir.path(), &task_manager).unwrap();
    
    let validator = BatchValidator::new(
        reth_env.clone(),
        None,
        0,
        BaseFeeContainer::default()
    );
    
    let _genesis_timestamp = chain.genesis().timestamp;
    let current_time = now();
    
    // Scenario 1: Small clock drift (1 second ahead)
    let small_drift = current_time + 1;
    let batch1 = create_test_batch_with_timestamp(&chain, small_drift);
    println!("🧪 Small clock drift (+1s): {}", small_drift);
    
    let result = validator.validate_batch(batch1.seal_slow());
    if result.is_ok() {
        println!("⚠️  Small clock drift accepted");
    }
    
    // Scenario 2: Medium clock drift (5 minutes ahead)
    let medium_drift = current_time + 300;
    let batch2 = create_test_batch_with_timestamp(&chain, medium_drift);
    println!("🧪 Medium clock drift (+5min): {}", medium_drift);
    
    let result = validator.validate_batch(batch2.seal_slow());
    if result.is_ok() {
        println!("⚠️  Medium clock drift accepted");
    }
    
    // Scenario 3: Large clock drift (1 hour ahead)
    let large_drift = current_time + 3600;
    let batch3 = create_test_batch_with_timestamp(&chain, large_drift);
    println!("🧪 Large clock drift (+1h): {}", large_drift);
    
    let result = validator.validate_batch(batch3.seal_slow());
    if result.is_ok() {
        println!("⚠️  Large clock drift accepted - this could be problematic!");
    }
    
    println!("🔍 Clock drift scenarios test completed");
}
