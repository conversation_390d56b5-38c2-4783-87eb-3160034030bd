//! Security PoC: Duplicate Transaction DoS Attack
//! 
//! This test demonstrates a vulnerability where duplicate transactions
//! can be included in batches and only rejected at execution time,
//! causing resource exhaustion.

use std::sync::Arc;
use tempfile::TempDir;
use tn_batch_validator::{BatchValidator, BatchValidation};
use tn_reth::{test_utils::TransactionFactory, RethChainSpec};
use tn_types::{
    max_batch_gas, Batch, Encodable2718 as _, TransactionTrait as _, Address, Bytes, U256,
};
use tn_test_utils::{test_genesis, TaskManager};

/// Test tools for batch validation
struct TestTools {
    validator: BatchValidator,
    valid_batch: Batch,
}

async fn test_tools(tmp_dir: &std::path::Path, task_manager: &TaskManager) -> TestTools {
    let reth_env = tn_reth::test_utils::test_reth_env(tmp_dir, task_manager).await;
    let validator = BatchValidator::new(reth_env, None, 0, Default::default());
    
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    
    let transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    
    let valid_batch = Batch {
        transactions: vec![transaction],
        parent_hash: Default::default(),
        beneficiary: Address::ZERO,
        timestamp: 1000,
        base_fee_per_gas: 7,
        worker_id: 0,
        received_at: 1000,
    };
    
    TestTools { validator, valid_batch }
}

#[tokio::test]
async fn test_duplicate_transactions_dos_attack() {
    println!("=== Testing Duplicate Transactions DoS Attack ===");
    
    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;
    
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    
    // Create the same transaction multiple times (duplicates)
    let duplicate_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    
    // Create batch with duplicate transactions
    let duplicate_batch = Batch {
        transactions: vec![
            duplicate_transaction.clone(),
            duplicate_transaction.clone(),
            duplicate_transaction.clone(),
            duplicate_transaction.clone(),
            duplicate_transaction.clone(),
        ],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: valid_batch.base_fee_per_gas,
        worker_id: 0,
        received_at: valid_batch.received_at,
    };
    
    println!("Batch contains {} duplicate transactions", duplicate_batch.transactions.len());
    
    // This should pass validation despite containing duplicates
    let result = validator.validate_batch(duplicate_batch.seal_slow());
    println!("Validation result: {:?}", result);
    
    // The vulnerability: validation passes but execution will reject duplicates
    assert!(result.is_ok(), "Batch with duplicate transactions should pass validation but will fail at execution");
    
    println!("✓ Vulnerability confirmed: Duplicate transactions pass validation");
    println!("  Impact: Resource exhaustion, network congestion");
    println!("  Recommendation: Add duplicate detection in validate_batch()");
}

#[tokio::test]
async fn test_excessive_gas_limit_dos() {
    println!("=== Testing Excessive Gas Limit DoS Attack ===");
    
    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;
    
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    
    // Create transaction with gas limit that exceeds batch maximum
    let max_gas = max_batch_gas(valid_batch.timestamp);
    let excessive_gas_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(max_gas + 1), // Exceeds maximum
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    
    let excessive_gas_batch = Batch {
        transactions: vec![excessive_gas_transaction],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: valid_batch.base_fee_per_gas,
        worker_id: 0,
        received_at: valid_batch.received_at,
    };
    
    println!("Transaction gas limit: {}, Max batch gas: {}", max_gas + 1, max_gas);
    
    // This should fail validation
    let result = validator.validate_batch(excessive_gas_batch.seal_slow());
    println!("Validation result: {:?}", result);
    
    // This should properly fail
    assert!(result.is_err(), "Batch with excessive gas should fail validation");
    
    println!("✓ Gas limit validation working correctly");
}

#[tokio::test]
async fn test_invalid_nonce_sequence_dos() {
    println!("=== Testing Invalid Nonce Sequence DoS Attack ===");
    
    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;
    
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    
    // Create transactions with invalid nonce sequence
    let tx1 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    
    // Skip nonce (create gap)
    tx_factory.increment_nonce();
    tx_factory.increment_nonce();
    
    let tx2 = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    
    let invalid_nonce_batch = Batch {
        transactions: vec![tx1, tx2],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: valid_batch.base_fee_per_gas,
        worker_id: 0,
        received_at: valid_batch.received_at,
    };
    
    println!("Batch contains transactions with nonce gap");
    
    // This passes validation but will fail at execution
    let result = validator.validate_batch(invalid_nonce_batch.seal_slow());
    println!("Validation result: {:?}", result);
    
    // The vulnerability: validation passes but execution will reject invalid nonces
    assert!(result.is_ok(), "Batch with invalid nonce sequence should pass validation but fail at execution");
    
    println!("✓ Vulnerability confirmed: Invalid nonce sequences pass validation");
    println!("  Impact: Resource exhaustion, wasted computation");
    println!("  Recommendation: Add nonce sequence validation in validate_batch()");
}
