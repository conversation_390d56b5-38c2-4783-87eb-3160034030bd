//! اختبار بسيط للمعاملات المكررة

#[tokio::test]
async fn test_simple_duplicate_check() {
    println!("=== اختبار بسيط للمعاملات المكررة ===");
    
    // إنشاء معاملات مكررة بسيطة
    let tx1 = vec![1, 2, 3, 4, 5];
    let tx2 = vec![1, 2, 3, 4, 5]; // نفس المعاملة
    let tx3 = vec![6, 7, 8, 9, 10]; // معاملة مختلفة
    
    let transactions = vec![tx1.clone(), tx2.clone(), tx3.clone()];
    
    // فحص المعاملات المكررة
    let mut unique_txs = std::collections::HashSet::new();
    let mut duplicate_count = 0;
    
    for tx in &transactions {
        if !unique_txs.insert(tx.clone()) {
            duplicate_count += 1;
            println!("🔍 تم العثور على معاملة مكررة: {:?}", tx);
        }
    }
    
    println!("📊 النتائج:");
    println!("   - إجمالي المعاملات: {}", transactions.len());
    println!("   - المعاملات الفريدة: {}", unique_txs.len());
    println!("   - المعاملات المكررة: {}", duplicate_count);
    
    // التحقق من النتائج
    assert_eq!(transactions.len(), 3);
    assert_eq!(unique_txs.len(), 2);
    assert_eq!(duplicate_count, 1);
    
    println!("✅ الاختبار نجح - تم اكتشاف المعاملة المكررة بنجاح");
}

#[test]
fn test_basic_functionality() {
    println!("=== اختبار الوظائف الأساسية ===");
    
    // اختبار بسيط للتأكد من أن الاختبارات تعمل
    let result = 2 + 2;
    assert_eq!(result, 4);
    
    println!("✅ الاختبار الأساسي نجح");
}
