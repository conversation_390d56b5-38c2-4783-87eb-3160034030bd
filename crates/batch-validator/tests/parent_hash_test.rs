//! اختبار التحقق من parent_hash في batch validation

use tn_types::{Batch, Address, U256, BlockHash, BatchValidation, test_genesis, Encodable2718};
use tn_types::gas_accumulator::BaseFeeContainer;
use tn_batch_validator::BatchValidator;
use tn_reth::{RethEnv, RethChainSpec};
use tn_reth::test_utils::TransactionFactory;
use std::sync::Arc;
use tempfile::TempDir;
use tn_types::TaskManager;

#[tokio::test]
async fn test_parent_hash_validation_behavior() {
    println!("🔍 اختبار سلوك التحقق من parent_hash");
    
    // إنشاء بيئة اختبار مؤقتة
    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    
    // إنشاء chain spec للاختبار
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), tmp_dir.path(), &task_manager).unwrap();
    
    // إنشاء validator
    let validator = BatchValidator::new(
        reth_env.clone(),
        None,
        0,
        BaseFeeContainer::default()
    );
    
    // إنشاء batch صحيح أولاً
    let valid_batch = create_test_batch(&chain);
    println!("✅ تم إنشاء batch صحيح مع parent_hash: {:?}", valid_batch.parent_hash);
    
    // اختبار الـ batch الصحيح أولاً
    let result = validator.validate_batch(valid_batch.clone().seal_slow());
    match result {
        Ok(()) => println!("✅ تم قبول batch صحيح"),
        Err(e) => println!("❌ تم رفض batch صحيح: {:?}", e),
    }
    
    // اختبار batch مع parent_hash خاطئ
    let wrong_parent_hash = BlockHash::random();
    let mut invalid_batch = valid_batch.clone();
    invalid_batch.parent_hash = wrong_parent_hash;
    
    println!("🧪 اختبار batch مع parent_hash خاطئ: {:?}", wrong_parent_hash);
    
    // تشغيل التحقق
    let result = validator.validate_batch(invalid_batch.seal_slow());
    
    match result {
        Ok(()) => {
            println!("⚠️  تم قبول batch مع parent_hash خاطئ - هذا يؤكد وجود الثغرة!");
            println!("⚠️  النظام لا يتحقق بصرامة من parent_hash");
        }
        Err(e) => {
            println!("✅ تم رفض batch مع parent_hash خاطئ: {:?}", e);
            println!("✅ النظام يتحقق بصرامة من parent_hash");
        }
    }
    
    // اختبار batch مع parent_hash = zero hash
    let mut zero_hash_batch = valid_batch.clone();
    zero_hash_batch.parent_hash = BlockHash::ZERO;
    
    println!("🧪 اختبار batch مع parent_hash = zero hash");
    
    let result = validator.validate_batch(zero_hash_batch.seal_slow());
    match result {
        Ok(()) => {
            println!("⚠️  تم قبول batch مع parent_hash = zero");
        }
        Err(e) => {
            println!("✅ تم رفض batch مع parent_hash = zero: {:?}", e);
        }
    }
}

fn create_test_batch(chain: &Arc<RethChainSpec>) -> Batch {
    
    let mut tx_factory = TransactionFactory::new();
    let tx = tx_factory
        .create_explicit_eip1559(
            Some(chain.chain.id()),
            None,
            None,
            Some(7),
            Some(21000),
            Some(Address::random()),
            Some(U256::from(100)),
            None,
            None,
        )
        .encoded_2718();
    
    Batch {
        transactions: vec![tx],
        parent_hash: chain.sealed_genesis_header().hash(),
        beneficiary: Address::random(),
        timestamp: tn_types::now(),
        worker_id: 0,
        base_fee_per_gas: Some(7),
        received_at: None,
    }
}

#[tokio::test]
async fn test_fallback_behavior() {
    println!("🔍 اختبار سلوك fallback في parent validation");
    
    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let chain = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), tmp_dir.path(), &task_manager).unwrap();
    
    // اختبار ما يحدث عندما لا يوجد parent_hash في قاعدة البيانات
    let non_existent_hash = BlockHash::random();
    
    println!("🧪 البحث عن header غير موجود: {:?}", non_existent_hash);
    
    match reth_env.header(non_existent_hash) {
        Ok(Some(header)) => {
            println!("✅ تم العثور على header: {:?}", header);
        }
        Ok(None) => {
            println!("⚠️  لم يتم العثور على header - سيتم استخدام fallback");
        }
        Err(e) => {
            println!("❌ خطأ في البحث عن header: {:?}", e);
        }
    }
    
    // اختبار finalized_header كـ fallback
    match reth_env.finalized_header() {
        Ok(Some(header)) => {
            println!("✅ تم العثور على finalized header: timestamp={}", header.timestamp);
        }
        Ok(None) => {
            println!("⚠️  لا يوجد finalized header");
        }
        Err(e) => {
            println!("❌ خطأ في البحث عن finalized header: {:?}", e);
        }
    }
}
