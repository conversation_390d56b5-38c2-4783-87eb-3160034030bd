//! اختبار التحقق من parent_hash في batch validation

use tn_types::{Batch, Address, U256, BlockHash, BatchValidation, test_genesis, Encodable2718};
use tn_types::gas_accumulator::BaseFeeContainer;
use tn_batch_validator::BatchValidator;
use tn_reth::{RethEnv, RethChainSpec};
use tn_reth::test_utils::TransactionFactory;
use std::sync::Arc;
use tempfile::TempDir;
use tn_types::TaskManager;

#[tokio::test]
async fn test_parent_hash_validation_behavior() {
    println!("🔍 Testing parent_hash validation behavior");

    // Create temporary test environment
    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();

    // Create chain spec for testing
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), tmp_dir.path(), &task_manager).unwrap();

    // Create validator
    let validator = BatchValidator::new(
        reth_env.clone(),
        None,
        0,
        BaseFeeContainer::default()
    );

    // Create valid batch first
    let valid_batch = create_test_batch(&chain);
    println!("✅ Created valid batch with parent_hash: {:?}", valid_batch.parent_hash);

    // Test valid batch first
    let result = validator.validate_batch(valid_batch.clone().seal_slow());
    match result {
        Ok(()) => println!("✅ Valid batch accepted"),
        Err(e) => println!("❌ Valid batch rejected: {:?}", e),
    }

    // Test batch with wrong parent_hash
    let wrong_parent_hash = BlockHash::random();
    let mut invalid_batch = valid_batch.clone();
    invalid_batch.parent_hash = wrong_parent_hash;

    println!("🧪 Testing batch with wrong parent_hash: {:?}", wrong_parent_hash);

    // Run validation
    let result = validator.validate_batch(invalid_batch.seal_slow());

    match result {
        Ok(()) => {
            println!("⚠️  Batch with wrong parent_hash accepted - This confirms the vulnerability!");
            println!("⚠️  System does not strictly validate parent_hash");
        }
        Err(e) => {
            println!("✅ Batch with wrong parent_hash rejected: {:?}", e);
            println!("✅ System strictly validates parent_hash");
        }
    }

    // Test batch with parent_hash = zero hash
    let mut zero_hash_batch = valid_batch.clone();
    zero_hash_batch.parent_hash = BlockHash::ZERO;

    println!("🧪 Testing batch with parent_hash = zero hash");

    let result = validator.validate_batch(zero_hash_batch.seal_slow());
    match result {
        Ok(()) => {
            println!("⚠️  Batch with parent_hash = zero accepted");
        }
        Err(e) => {
            println!("✅ Batch with parent_hash = zero rejected: {:?}", e);
        }
    }
}

fn create_test_batch(chain: &Arc<RethChainSpec>) -> Batch {
    
    let mut tx_factory = TransactionFactory::new();
    let tx = tx_factory
        .create_explicit_eip1559(
            Some(chain.chain.id()),
            None,
            None,
            Some(7),
            Some(21000),
            Some(Address::random()),
            Some(U256::from(100)),
            None,
            None,
        )
        .encoded_2718();
    
    Batch {
        transactions: vec![tx],
        parent_hash: chain.sealed_genesis_header().hash(),
        beneficiary: Address::random(),
        timestamp: tn_types::now(),
        worker_id: 0,
        base_fee_per_gas: Some(7),
        received_at: None,
    }
}

#[tokio::test]
async fn test_fallback_behavior() {
    println!("🔍 Testing fallback behavior in parent validation");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), tmp_dir.path(), &task_manager).unwrap();

    // Test what happens when parent_hash doesn't exist in database
    let non_existent_hash = BlockHash::random();

    println!("🧪 Looking for non-existent header: {:?}", non_existent_hash);

    match reth_env.header(non_existent_hash) {
        Ok(Some(header)) => {
            println!("✅ Found header: {:?}", header);
        }
        Ok(None) => {
            println!("⚠️  Header not found - fallback will be used");
        }
        Err(e) => {
            println!("❌ Error searching for header: {:?}", e);
        }
    }

    // Test finalized_header as fallback
    match reth_env.finalized_header() {
        Ok(Some(header)) => {
            println!("✅ Found finalized header: timestamp={}", header.timestamp);
        }
        Ok(None) => {
            println!("⚠️  No finalized header available");
        }
        Err(e) => {
            println!("❌ Error searching for finalized header: {:?}", e);
        }
    }
}
