[package]
name = "tn-network-libp2p"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
exclude.workspace = true

[dependencies]
libp2p = { workspace = true, features = [
    "request-response",
    "identify",
    "gossipsub",
    "tokio",
    "quic",
    "macros",
    "kad",
] }
tokio = { workspace = true, features = ["rt", "net", "sync", "macros", "time"] }
tn-types = { workspace = true }
tn-config = { workspace = true }
futures = { workspace = true }
tracing = { workspace = true }
serde = { workspace = true }
thiserror = { workspace = true }
tn-storage = { workspace = true }
bs58 = { workspace = true }

# req/res requires async_trait
async-trait = { workspace = true }
bcs = { workspace = true }
snap = { workspace = true }

# peer manager
rand = { workspace = true }
serde_with = { workspace = true }

[dev-dependencies]
tempfile = { workspace = true }
tn-reth = { workspace = true, features = ["test-utils"] }
eyre = { workspace = true }
assert_matches = { workspace = true }
tn-storage = { workspace = true }
tn-test-utils = { workspace = true }
tn-types = { workspace = true, features = ["test-utils"] }

[lints]
workspace = true
