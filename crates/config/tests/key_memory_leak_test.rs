//! Test for key memory handling and potential memory leaks

use tn_config::KeyConfig;
use tn_types::{BlsKeypair, NetworkKeypair, BlsSigner};
use tempfile::TempDir;
use std::mem;
use tokio;

/// Test to demonstrate key memory handling behavior
#[tokio::test]
async fn test_key_memory_handling() {
    println!("🔍 Testing key memory handling and potential leaks");

    let tmp_dir = TempDir::new().unwrap();

    // Test 1: Create KeyConfig and check memory layout
    println!("📋 Test 1: Creating KeyConfig and analyzing memory");
    let key_config = KeyConfig::generate_and_save(&tmp_dir.path().to_path_buf(), None)
        .expect("Failed to generate key config");

    // Clone KeyConfig to simulate multiple references
    let key_config_clone = key_config.clone();

    println!("✅ KeyConfig created and cloned successfully");

    // Test 2: Memory size analysis
    println!("📋 Test 2: Analyzing memory sizes");
    let key_config_size = mem::size_of_val(&key_config);
    let bls_keypair_size = mem::size_of::<BlsKeypair>();
    let network_keypair_size = mem::size_of::<NetworkKeypair>();

    println!("🔍 KeyConfig size: {} bytes", key_config_size);
    println!("🔍 BlsKeypair size: {} bytes", bls_keypair_size);
    println!("🔍 NetworkKeypair size: {} bytes", network_keypair_size);

    // Test 3: Key operations demonstrating memory access
    println!("📋 Test 3: Testing key operations and memory persistence");

    // Get public key (this is safe)
    let public_key = key_config.primary_public_key();
    println!("🔍 BLS public key accessible: {:?}", public_key);

    // Perform signature operation
    let message = b"test message for memory leak demonstration".to_vec();
    let signature = key_config.request_signature(message.clone()).await;
    println!("🔍 Signature operation completed successfully");

    // Test that cloned config can also perform operations
    let signature2 = key_config_clone.request_signature(message).await;
    println!("🔍 Cloned config can also sign messages");

    // Both signatures should be identical (same private key)
    assert_eq!(signature, signature2, "Signatures should be identical");
    println!("✅ Both configs use same underlying private key");

    println!("🔍 Key memory handling test completed");
}

/// Test to demonstrate the lack of secure memory clearing
#[test]
fn test_no_secure_memory_clearing() {
    println!("🔍 Testing lack of secure memory clearing");

    let tmp_dir = TempDir::new().unwrap();

    // Create a scope where keys exist
    let original_public_key = {
        let key_config = KeyConfig::generate_and_save(&tmp_dir.path().to_path_buf(), None)
            .expect("Failed to generate key config");

        let public_key = key_config.primary_public_key();
        println!("🔍 Created key with public key: {:?}", public_key);
        public_key
    }; // key_config goes out of scope here

    // At this point, the KeyConfig is dropped, but there's no guarantee
    // that the memory containing the private key has been securely cleared
    println!("⚠️  KeyConfig dropped, but memory may not be securely cleared");
    println!("⚠️  Original public key: {:?}", original_public_key);

    // Create another key to potentially reuse memory
    let key_config2 = KeyConfig::generate_and_save(&tmp_dir.path().to_path_buf(), None)
        .expect("Failed to generate second key config");

    let new_public_key = key_config2.primary_public_key();
    println!("🔍 New public key: {:?}", new_public_key);

    // The keys should be different
    assert_ne!(original_public_key, new_public_key, "Keys should be different");

    // The concern is that the old private key material may still be in memory
    // even though we can't directly access it through the public API
    println!("⚠️  Old key material may still be accessible in memory");

    println!("🔍 Secure memory clearing test completed");
}

/// Test to check if zeroize is implemented (it's not)
#[tokio::test]
async fn test_zeroize_implementation() {
    println!("🔍 Testing for zeroize implementation");

    let tmp_dir = TempDir::new().unwrap();
    let key_config = KeyConfig::generate_and_save(&tmp_dir.path().to_path_buf(), None)
        .expect("Failed to generate key config");

    // Get the public key for reference
    let public_key = key_config.primary_public_key();
    println!("🔍 Public key: {:?}", public_key);

    // Create a signature to demonstrate key usage
    let message = b"test message for zeroize test".to_vec();
    let signature = key_config.request_signature(message).await;
    println!("🔍 Created signature successfully");

    // Drop the key_config
    drop(key_config);

    // The signature and public key variables still contain cryptographic material
    // This demonstrates that there's no automatic zeroization of related data
    println!("⚠️  After dropping KeyConfig, signature still accessible: {:?}", signature);
    println!("⚠️  After dropping KeyConfig, public key still accessible: {:?}", public_key);

    // In a secure implementation, sensitive material should be zeroized
    // when the key is no longer needed
    println!("⚠️  No comprehensive zeroize implementation detected");

    println!("🔍 Zeroize implementation test completed");
}

/// Test Arc behavior with key sharing
#[tokio::test]
async fn test_arc_key_sharing_vulnerability() {
    println!("🔍 Testing Arc key sharing vulnerability");

    let tmp_dir = TempDir::new().unwrap();
    let key_config = KeyConfig::generate_and_save(&tmp_dir.path().to_path_buf(), None)
        .expect("Failed to generate key config");

    // Create multiple references to the same key material
    let clone1 = key_config.clone();
    let clone2 = key_config.clone();
    let clone3 = key_config.clone();

    println!("🔍 Created 4 references to the same key material");

    // All clones should produce identical signatures (same private key)
    let message = b"test message for arc sharing test".to_vec();
    let sig1 = key_config.request_signature(message.clone()).await;
    let sig2 = clone1.request_signature(message.clone()).await;
    let sig3 = clone2.request_signature(message.clone()).await;
    let sig4 = clone3.request_signature(message.clone()).await;

    assert_eq!(sig1, sig2);
    assert_eq!(sig2, sig3);
    assert_eq!(sig3, sig4);

    println!("✅ All clones produce identical signatures (shared private key)");

    // Even if we drop some references, the key material persists
    drop(clone1);
    drop(clone2);

    println!("🔍 After dropping 2 clones, remaining configs still work");

    // Key material is still accessible through remaining references
    let sig_after_drop = key_config.request_signature(message).await;
    assert_eq!(sig1, sig_after_drop);
    println!("⚠️  Key material still accessible through remaining references");

    // This demonstrates that key material persists in memory as long as
    // any reference exists, increasing the window for potential extraction

    println!("🔍 Arc key sharing vulnerability test completed");
}
