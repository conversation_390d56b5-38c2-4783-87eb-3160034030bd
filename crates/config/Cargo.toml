[package]
name = "tn-config"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
publish = false
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
]

[dependencies]
tracing = { workspace = true }
tn-network-types = { workspace = true }
tn-types = { workspace = true }
eyre = { workspace = true }
rand = { workspace = true }
serde = { workspace = true }
serde_yaml = { workspace = true }
serde_json = { workspace = true }
# Use reth directly to avoid circular deps.
reth-chainspec = { workspace = true }
humantime-serde = { workspace = true }
libp2p = { workspace = true, features = ["request-response"] }
backoff = { workspace = true }
bs58 = { workspace = true }

# Crypto specific to key wrapping, not used elsewhere.
aes-gcm-siv = "0.11.1"
pbkdf2 = "0.12.2"
sha2 = "0.10.8"

[dev-dependencies]
tempfile = { workspace = true }
tokio = { workspace = true, features = ["macros", "rt"] }
