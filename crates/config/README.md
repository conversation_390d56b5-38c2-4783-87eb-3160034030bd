# Config

The library that contains configuration information for a Telcoin Network node.

## P<PERSON><PERSON> & Scope

The config crate contains various configuration parameters that allow a node to participate with the protocol.
It contains sensitive information and manages private keys.

## Key Components

### ConsensusConfig

The configuration for consensus layer.
The `Committee` and `WorkerCache` contains all information for committee participates that vote to extend the canonical tip.
The committee is comprised of staked validators and information to verify their communication.

### GenesisConfig

The configuration for network genesis.
This is only used once, at the network's genesis.
The initial committee is generated by a "Master of Ceremony" using a centralized VCS (ie - GitHub).
Each validator must submit a proof-of-possession, which is validated using this logic.

See `bin/telcoin-network/genesis` for more information on how this is used.

### KeyConfig

The most critical config for a node's security.
This contains the node's keys used to authenticate protocol messages.

The protocol requires access to three keys in the current version:

- Primary BLS12-381 key
- Primary Ed25519 network key
- Worker Ed25519 network key (only support 1 worker per node for now)

Node operators can generate BLS keys using the CLI (see bin/telcoin-network/keytool).
The generated keys are stored on the filesystem using AES-GCM-SIV.
This config contains the logic for ensuring appropriate encryption.

The network keys are deterministically derived from a bls signature of a seed string.

Signatures are requested using the trait `tn_types::crypto::BlsSigner`.
The method `request_signature_direct` is used when publishing node records to the kademlia store in `network-libp2p/src/consensus.rs` and when the primary node votes on a header in `types/src/primary/vote.rs`.
The goal is to keep private keys in memory and provide a secure API for obtaining signatures rapidly.

To this end, BLS private keys are not exposed outside of the `KeyConfig`.
However, the network's private key are exposed so they can be used with libp2p.

The BLS key is the more important key to impersonate a validator and network keys can theoretically be rotated (although we don't do that yet).

BLS keys must also be unique to stake on the `ConsensusRegistry`.
Even after a validator node exits, the BLS key must be unique.

### NetworkConfig

The network config holds various configurations for the p2p networking.
Nodes operate two networks based on libp2p behaviours.
The network config is used to create the network in `crates/network-libp2p/consensus.rs`.

The network config also contains qualities for peer manager behaviour (see `crates/network-libp2p/peers`).
The peer manager is responsible for banning peers who are identified as bad actors.

### Node `Config`

Node information used to configure Narwhal/Bullshark parameters and information exchanged between peers.

### RetryConfig

Configurations for retrying state sync requests.
Only used in `crates/consensus/primary/src/state_sync/header_validator.rs` when syncing missing batches during primary header validation.

### Traits

Generic traits for interacting with the underlying filesystem.
This trait is implemented on types that are written/read from the filesystem in YAML or JSON format.

## Security Considerations

### Threat Models

#### Key Management

Keys are paramount for security and should never be available to outsiders.

#### Network Configuration

The network configuration has implications for banning peers and applying their scores.

#### Node Configuration

The configurations are designed to keep nodes secure and live.

### Trust Assumptions

- Keys are stored using the best practices
- Network keys are generated securely as long as the node operator treats their BLS signature of the seed phrase as a password

### Critical Invariants

- Batches are intentionally designed to be agnostic (transactions are `Vec<Vec<u8>>`) so the network can support different execution environments in future versions
- The engine gracefully ignores invalid transactions
- Basefees only adjust at the start of a new epoch
- Batch validators are only valid within a single epoch
