[package]
name = "tn-rpc"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
exclude.workspace = true

[dependencies]
tn-types = { workspace = true }
tn-reth = { workspace = true }
jsonrpsee = { workspace = true, features = ["server", "macros"] }
jsonrpsee-types = { workspace = true }
async-trait = { workspace = true }
thiserror = { workspace = true }
serde = { workspace = true }

[dev-dependencies]
rand = { workspace = true }

[lints]
workspace = true
