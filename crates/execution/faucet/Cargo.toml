[package]
name = "tn-faucet"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
]

[dependencies]
tracing = { workspace = true }
humantime = { workspace = true }
futures = { workspace = true }
tokio = { workspace = true, features = ["sync"] }
tn-reth = { workspace = true }
tn-rpc = { workspace = true }
reth = { workspace = true }
reth-primitives = { workspace = true }
reth-tasks = { workspace = true }
reth-transaction-pool = { workspace = true }
clap = { workspace = true, features = ["env"] }
eyre = { workspace = true }
tokio-stream = { workspace = true, features = ["sync"] }
lru_time_cache = { version = "0.11.11" }
jsonrpsee = { workspace = true, features = ["macros"] }
# still needed for jsonrpsee 0.24.3
async-trait = { workspace = true }

# google cloud feature
gcloud-sdk = { version = "=0.24.6", default-features = false, features = [
    "google-cloud-kms-v1",
    "tls-webpki-roots",
] }
k256 = "0.13.3"
ecdsa = { version = "0.16.9", features = ["pem"] }
secp256k1 = { workspace = true }
tn-types = { workspace = true }

[dev-dependencies]
consensus-metrics = { workspace = true }
tn-test-utils = { workspace = true }
tn-network-types = { workspace = true }
tn-worker = { workspace = true }
tn-storage = { workspace = true }
tempfile = { workspace = true }
tn-config = { workspace = true }
tn-reth = { workspace = true, features = ["test-utils"] }
tn-types = { workspace = true, features = ["test-utils"] }
