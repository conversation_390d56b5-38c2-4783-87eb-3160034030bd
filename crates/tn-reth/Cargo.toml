[package]
name = "tn-reth"
version.workspace = true
edition = "2021"
license = "Apache-2.0"
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
]
publish = false

[dependencies]
jsonrpsee = { workspace = true }
clap = { workspace = true, features = ["derive", "env"] }
tn-config = { workspace = true }
tn-types = { workspace = true }
tn-worker = { workspace = true }
dirs-next = "2.0.0"
parking_lot = { workspace = true }

reth = { workspace = true }
reth-db = { workspace = true }
reth-db-common = { workspace = true }
reth-node-core = { workspace = true }
reth-node-ethereum = { workspace = true }
reth-chainspec = { workspace = true }
reth-chain-state = { workspace = true }
reth-cli-util = { workspace = true }
reth-primitives = { workspace = true }
reth-consensus = { workspace = true }
reth-evm-ethereum = { workspace = true }
reth-engine-primitives = { workspace = true }
reth-engine-tree = { workspace = true }
reth-revm = { workspace = true }
reth-evm = { workspace = true }
reth-node-builder = { workspace = true }
reth-provider = { workspace = true }
reth-trie-db = { workspace = true }
reth-primitives-traits = { workspace = true }
reth-transaction-pool = { workspace = true }

reth-errors = { workspace = true }
reth-rpc-eth-types = { workspace = true }
reth-network-peers = { workspace = true }
reth-network-api = { workspace = true }
reth-discv4 = { workspace = true }
reth-eth-wire = { workspace = true }
reth-tracing = { workspace = true }

rand = { workspace = true }
futures = { workspace = true }
serde = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true }
eyre = { workspace = true }
alloy = { workspace = true, features = ["genesis"] }
alloy-evm = { workspace = true }
tempfile = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true, features = ["macros", "time"] }
reth-ethereum-primitives = { workspace = true }
secp256k1 = { workspace = true, optional = true }

[dev-dependencies]
tn-types = { workspace = true, features = ["test-utils"] }
secp256k1 = { workspace = true }

[features]
default = []
test-utils = ["secp256k1"]
