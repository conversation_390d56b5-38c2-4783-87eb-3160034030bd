[package]
name = "tn-storage"
version.workspace = true
edition = "2021"
license = "MIT or Apache-2.0"
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "Mysten Labs <<EMAIL>>",
]
publish = false

[dependencies]
tn-utils.workspace = true
tracing.workspace = true
tn-types = { workspace = true }
eyre = { workspace = true }
prometheus = { workspace = true }
serde = { workspace = true }
ouroboros = { workspace = true }
parking_lot = { workspace = true }
dashmap = { workspace = true }

# redb backend
redb = { version = "2.1.1", optional = false }

# mdbx backend- default
reth-libmdbx = { workspace = true, optional = true, features = [
    "return-borrowed",
    "read-tx-timeouts",
] }
page_size = { version = "0.6.0", optional = true }

[dev-dependencies]
tempfile = { workspace = true }
tokio = { workspace = true, features = ["sync", "rt", "macros"] }
tn-test-utils = { workspace = true }
futures = { workspace = true }

[features]
redb = []
reth-libmdbx = ["dep:reth-libmdbx", "dep:page_size"]
default = ["reth-libmdbx"]
