[package]
name = "tn-network-types"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "Mysten Labs <<EMAIL>>",
]
description = "Commonly used types in telcoin network's internal network communication."

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
tn-types = { workspace = true }
serde = { workspace = true, features = ["derive"] }
tracing = { workspace = true }
async-trait = { workspace = true }
eyre = { workspace = true }
libp2p = { workspace = true }
parking_lot = { workspace = true }

[build-dependencies]
rustversion = "1.0.9"
