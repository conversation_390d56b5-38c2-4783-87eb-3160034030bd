//! Integration test for signature verification bypass in VotesAggregator (real code)

use std::sync::Arc;
use tn_primary_metrics::PrimaryMetrics;
use tn_types::{
    error::Dag<PERSON><PERSON>r, Committee, Header, Vote, BlsSignature, AuthorityIdentifier, BlsPublicKey, Authority, Epoch, Round, BlockNumHash
};
use tn_primary::aggregators::VotesAggregator;
use std::collections::BTreeMap;

#[test]
fn test_votes_aggregator_signature_verification_bypass() {
    // إعداد لجنة اختبار بسلطة واحدة فقط (لتبسيط السيناريو)
    let epoch = Epoch(1);
    let round = Round(1);
    let block = BlockNumHash::default();
    let authority_id = AuthorityIdentifier::default();
    let bls_pk = BlsPublicKey::default();
    let authority = Authority::new_for_test(authority_id.clone(), bls_pk.clone());
    let mut authorities = BTreeMap::new();
    authorities.insert(bls_pk.clone(), authority);
    let committee = Committee::new_for_test(authorities, epoch);

    // Header بسيط
    let header = Header::new(
        authority_id.clone(),
        round,
        epoch,
        vec![],
        block,
    );

    // Metrics وهمية
    let metrics = Arc::new(PrimaryMetrics::default());
    let mut aggregator = VotesAggregator::new(metrics);

    // إنشاء تصويت مزور (توقيع غير صحيح)
    let fake_signature = BlsSignature::default();
    let fake_vote = Vote::new_with_signature(header.clone(), authority_id.clone(), fake_signature.clone());

    // محاولة إضافة التصويت
    let result = aggregator.append(fake_vote, &committee, &header);

    match result {
        Ok(Some(_cert)) => {
            panic!("Certificate should not be created with invalid signature!");
        }
        Ok(None) => {
            // تحقق هل زاد الوزن قبل التحقق من التوقيع
            assert!(aggregator.weight > 0, "Weight should not be increased for invalid signature!");
            println!("\n🚨 Vulnerability: Weight increased before signature verification!");
        }
        Err(DagError::InvalidSignature) => {
            println!("Signature correctly rejected.");
        }
        Err(e) => panic!("Unexpected error: {:?}", e),
    }
}
