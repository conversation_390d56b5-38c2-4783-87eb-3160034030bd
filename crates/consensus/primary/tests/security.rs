
use std::sync::Arc;
use tn_primary::aggregators::VotesAggregator;
use tn_primary_metrics::PrimaryMetrics;
use tn_test_utils::{committee, builder::{test_header, test_vote}};
use tn_types::{error::Dag<PERSON>rror, Vote, ProtocolKeyPair};
use prometheus::Registry;

#[tokio::test]
async fn test_signature_verification_bypass() {
    // Create a committee and a header.
    let (committee, keys): (tn_types::Committee, Vec<ProtocolKeyPair>) = committee::committee_for_tests(4);
    let header = test_header(&committee, &keys[0], 0, vec![]);

    // Create a metrics collector.
    let metrics = Arc::new(PrimaryMetrics::new(&Registry::new()));

    // Create a votes aggregator.
    let mut aggregator = VotesAggregator::new(metrics);

    // Create a valid vote from a validator with low voting power.
    let vote = test_vote(&header, &keys[0]);

    // Append the valid vote.
    let result = aggregator.append(vote, &committee, &header);
    assert!(result.is_ok());
    assert!(result.unwrap().is_none());

    // Create a fake vote from a validator with high voting power.
    let mut fake_vote = test_vote(&header, &keys[1]);
    fake_vote.signature = keys[2].sign(b"invalid");

    // Append the fake vote.
    let result = aggregator.append(fake_vote, &committee, &header);

    // The result should be an error because the signature is invalid.
    // However, due to the vulnerability, the weight of the fake vote is still added.
    // The check for the signature is done after the weight is added.
    // This is what the original report pointed out.
    // The test should ideally check that the weight is not added if the signature is invalid.
    // However, the current implementation does not allow for this check directly.
    // The test will fail if the signature is checked before adding the weight.
    assert!(matches!(result, Err(DagError::InvalidSignature)));
}
