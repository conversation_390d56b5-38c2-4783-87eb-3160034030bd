//! Test for signature verification bypass in VotesAggregator (real code)

use std::sync::Arc;
use tn_primary_metrics::PrimaryMetrics;
use tn_types::{
    error::DagError, Committee, Header, Vote, BlsSignature, AuthorityIdentifier,
};
use crate::aggregators::VotesAggregator;

#[test]
fn test_votes_aggregator_signature_verification_bypass() {
    // Setup dummy committee, header, and metrics
    let metrics = Arc::new(PrimaryMetrics::default());
    let committee = Committee::default_for_tests();
    let header = Header::default_for_tests();

    // Create a VotesAggregator
    let mut aggregator = VotesAggregator::new(metrics);

    // Create a fake vote with invalid signature from a high-weight authority
    let high_weight_author = committee.authorities().next().unwrap().id().clone();
    let fake_signature = BlsSignature::default(); // Invalid signature
    let fake_vote = Vote::new_for_tests(high_weight_author.clone(), fake_signature.clone());

    // Try to append the fake vote
    let result = aggregator.append(fake_vote, &committee, &header);

    match result {
        Ok(Some(_cert)) => {
            panic!("Certificate should not be created with invalid signature!");
        }
        Ok(None) => {
            // Check if weight was increased before signature verification
            assert!(aggregator.weight > 0, "Weight should not be increased for invalid signature!");
            println!("\n🚨 Vulnerability: Weight increased before signature verification!");
        }
        Err(DagError::InvalidSignature) => {
            println!("Signature correctly rejected.");
        }
        Err(e) => panic!("Unexpected error: {:?}", e),
    }
}
