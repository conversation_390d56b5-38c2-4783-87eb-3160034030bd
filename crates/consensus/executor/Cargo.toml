[package]
name = "tn-executor"
version.workspace = true
edition = "2021"
license = "Apache-2.0"
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "Mysten Labs <<EMAIL>>",
]
publish = false

[dependencies]
futures.workspace = true
thiserror.workspace = true
tokio = { workspace = true, features = ["sync"] }
tracing = { workspace = true }
tn-storage = { workspace = true }
tn-network-types = { workspace = true }
state-sync = { workspace = true }
tn-types = { workspace = true }
tn-config = { workspace = true }
consensus-metrics.workspace = true
tn-primary = { workspace = true }

[dev-dependencies]
indexmap.workspace = true
eyre = { workspace = true }
tn-test-utils = { workspace = true }
tn-primary = { workspace = true, features = ["test-utils"] }
tn-network-libp2p = { workspace = true }
tn-reth = { workspace = true, features = ["test-utils"] }
