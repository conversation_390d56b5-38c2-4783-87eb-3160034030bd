[package]
name = "consensus-metrics"
version.workspace = true
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "Mysten Labs <<EMAIL>>",
]
license = "Apache-2.0"
publish = false
edition = "2021"

[dependencies]
axum = { workspace = true }
tracing = { workspace = true }
scopeguard = { workspace = true }
prometheus = { workspace = true }
once_cell = { workspace = true }
tokio = { workspace = true }
parking_lot = { workspace = true }
futures = { workspace = true }
tn-types = { workspace = true }

[lints.rust]
unexpected_cfgs = { level = "warn", check-cfg = ['cfg(msim)'] }
