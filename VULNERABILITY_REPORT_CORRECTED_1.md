# تقرير الثغرة الأمنية #1: عدم فحص المعاملات المكررة

## Finding Title
Duplicate Transaction Processing Vulnerability

## Summary
عدم وجود فحص للمعاملات المكررة في نفس الدفعة يسمح بمعالجة معاملات متطابقة مما يؤدي إلى استنزاف الموارد.

## Finding Description

تم اكتشاف ثغرة أمنية في دالة `validate_batch` في ملف `crates/batch-validator/src/validator.rs`. النظام الحالي لا يتحقق من وجود معاملات مكررة في نفس الدفعة، مما يسمح بمعالجة معاملات لها نفس الهاش أكثر من مرة.

### الكود المصاب

في ملف `crates/batch-validator/src/validator.rs` السطور 31-78:

```rust
fn validate_batch(&self, sealed_batch: SealedBatch) -> BatchValidationResult<()> {
    // ensure digest matches batch
    let (batch, digest) = sealed_batch.split();
    let verified_hash = batch.clone().seal_slow().digest();
    if digest != verified_hash {
        return Err(BatchValidationError::InvalidDigest);
    }

    // A validator belongs to a worker and that worker only handles batches with it's id.
    if batch.worker_id != self.worker_id {
        return Err(BatchValidationError::InvalidWorkerId {
            expected_worker_id: self.worker_id,
            worker_id: batch.worker_id,
        });
    }

    // TODO: validate individual transactions against parent

    // obtain info for validation
    let transactions = batch.transactions();

    // validate batch size (bytes)
    self.validate_batch_size_bytes(transactions, batch.timestamp)?;

    // validate txs decode
    let decoded_txs = self.decode_transactions(transactions, digest)?;

    // validate gas limit
    self.validate_batch_gas(&decoded_txs, batch.timestamp)?;

    // validate base fee- all batches for a worker and epoch have the same base fee.
    self.validate_basefee(batch.base_fee_per_gas)?;
    Ok(())
}
```

**المشكلة:** لا يوجد فحص للتأكد من عدم تكرار المعاملات في الدفعة الواحدة.

## Impact

1. **استنزاف الموارد:** معالجة معاملات مكررة تستهلك موارد المعالجة والذاكرة دون فائدة
2. **تأخير الشبكة:** المعاملات المكررة تؤخر معالجة المعاملات الصحيحة
3. **استهلاك النطاق الترددي:** نقل ومعالجة بيانات مكررة عبر الشبكة
4. **فشل في التنفيذ:** المعاملات المكررة ستفشل في مرحلة التنفيذ مما يضيع الموارد

## Likelihood

عالية - يمكن للمهاجم بسهولة إنشاء دفعات تحتوي على معاملات مكررة لأن النظام الحالي لا يتحقق من التكرار.

## Proof of Concept

تم إنشاء اختبار حقيقي يستدعي دالة `validate_batch` الفعلية في `crates/batch-validator/tests/security_poc_duplicate_transactions.rs`:

```rust
use tempfile::TempDir;
use tn_types::{TaskManager, U256, Address, Bytes, Batch, test_genesis, BatchValidation};
use tn_reth::{test_utils::TransactionFactory, RethChainSpec};
use std::sync::Arc;

// استيراد BatchValidator من الكريت الفعلي
use tn_batch_validator::BatchValidator;

// تعريف TestTools وtest_tools محليًا (من كود الاختبار الأصلي)
struct TestTools {
    pub validator: BatchValidator,
    pub valid_batch: Batch,
}

async fn test_tools(path: &std::path::Path, task_manager: &TaskManager) -> TestTools {
    use tn_reth::{RethEnv, test_utils::TransactionFactory};
    use tn_types::{Batch, Address, U256, test_genesis};
    use std::sync::Arc;

    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), path, task_manager).unwrap();
    let tx_pool = reth_env.init_txn_pool().unwrap();
    let validator = BatchValidator::new(reth_env, Some(tx_pool), 0, Default::default());

    let timestamp = chain.genesis_timestamp() + 1;
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let genesis_hash = chain.genesis_hash();
    let transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        None,
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    let valid_batch = Batch {
        transactions: vec![transaction],
        parent_hash: genesis_hash,
        beneficiary: Address::ZERO,
        timestamp,
        base_fee_per_gas: Some(1),
        worker_id: 0,
        received_at: None,
    };
    TestTools { validator, valid_batch }
}

#[tokio::test]
async fn test_duplicate_transactions_dos_attack() {
    println!("=== Testing Duplicate Transactions DoS Attack ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // Create the same transaction multiple times (duplicate)
    let duplicate_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(21000),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    // Create a batch with duplicate transactions
    let duplicate_batch = Batch {
        transactions: vec![
            duplicate_transaction.clone(),
            duplicate_transaction.clone(),
            duplicate_transaction.clone(),
            duplicate_transaction.clone(),
            duplicate_transaction.clone(),
        ],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    // Call the real validate_batch function
    let result = validator.validate_batch(duplicate_batch.seal_slow());

    println!("Validation result: {:?}", result);

    // Check if validation passes or fails
    if result.is_ok() {
        println!("✓ Vulnerability confirmed: Duplicate transactions pass validation");
        println!("  Impact: Resource exhaustion, network congestion");
        println!("  Recommendation: Add duplicate detection in validate_batch()");
    } else {
        println!("✓ Security working correctly: Duplicate transactions rejected");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly detects and rejects duplicate transactions");
    }
}
```

### خطوات التنفيذ

```bash
cd crates/batch-validator
cargo test --test security_poc_duplicate_transactions test_duplicate_transactions_dos_attack -- --show-output
```

### النتائج الفعلية

```bash
running 1 test
test test_duplicate_transactions_dos_attack ... ok

successes:

---- test_duplicate_transactions_dos_attack stdout ----
=== Testing Duplicate Transactions DoS Attack ===
Validation result: Ok(())
✓ Vulnerability confirmed: Duplicate transactions pass validation
  Impact: Resource exhaustion, network congestion
  Recommendation: Add duplicate detection in validate_batch()

successes:
    test_duplicate_transactions_dos_attack

test result: ok. 1 passed; 0 failed; 0 ignored; 0 measured; 0 filtered out
```

## Recommendation

### إضافة فحص المعاملات المكررة في دالة `validate_batch`

يجب تعديل دالة `validate_batch` في `crates/batch-validator/src/validator.rs` لإضافة فحص المعاملات المكررة:

```rust
use std::collections::HashSet;
use sha3::{Digest, Keccak256};

fn validate_batch(&self, sealed_batch: SealedBatch) -> BatchValidationResult<()> {
    // ensure digest matches batch
    let (batch, digest) = sealed_batch.split();
    let verified_hash = batch.clone().seal_slow().digest();
    if digest != verified_hash {
        return Err(BatchValidationError::InvalidDigest);
    }

    // A validator belongs to a worker and that worker only handles batches with it's id.
    if batch.worker_id != self.worker_id {
        return Err(BatchValidationError::InvalidWorkerId {
            expected_worker_id: self.worker_id,
            worker_id: batch.worker_id,
        });
    }

    // obtain info for validation
    let transactions = batch.transactions();

    // إضافة فحص المعاملات المكررة
    let mut seen_hashes = HashSet::new();
    for tx_bytes in transactions {
        // حساب هاش المعاملة
        let mut hasher = Keccak256::new();
        hasher.update(tx_bytes);
        let tx_hash = hasher.finalize();
        
        if !seen_hashes.insert(tx_hash) {
            return Err(BatchValidationError::RecoverTransaction(
                digest, 
                "Duplicate transaction detected in batch".to_string()
            ));
        }
    }

    // validate batch size (bytes)
    self.validate_batch_size_bytes(transactions, batch.timestamp)?;

    // validate txs decode
    let decoded_txs = self.decode_transactions(transactions, digest)?;

    // validate gas limit
    self.validate_batch_gas(&decoded_txs, batch.timestamp)?;

    // validate base fee
    self.validate_basefee(batch.base_fee_per_gas)?;
    Ok(())
}
```

## Severity Justification

**الخطورة: عالية (High)**

- **التأثير:** عالي - استنزاف الموارد وتأخير معالجة المعاملات الصحيحة
- **الاحتمالية:** عالية - سهل التنفيذ ولا يتطلب صلاحيات خاصة  
- **قابلية الاستغلال:** عالية - يمكن للمهاجم إنشاء دفعات بمعاملات مكررة بسهولة

## Conclusion

تم إثبات وجود ثغرة أمنية تسمح بمعالجة معاملات مكررة في نفس الدفعة. هذه الثغرة يمكن أن تؤدي إلى استنزاف الموارد وهجمات رفض الخدمة. يجب إصلاحها فوراً بإضافة فحص للمعاملات المكررة في دالة `validate_batch`. الحل المقترح يستخدم أنواع الأخطاء الموجودة حالياً ولن يؤثر على الأداء بشكل كبير.
