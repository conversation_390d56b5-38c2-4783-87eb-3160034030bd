# تقرير الثغرة الأمنية #2: عدم وجود حدود قصوى للغاز في المعاملات الفردية

## Finding Title
Individual Transaction Gas Limit Vulnerability

## Summary
عدم وجود حدود قصوى لاستهلاك الغاز في المعاملات الفردية يسمح بإنشاء معاملات بحدود غاز مفرطة مما يؤدي إلى استنزاف الموارد.

## Finding Description

تم اكتشاف ثغرة أمنية في دالة `validate_batch_gas` في ملف `crates/batch-validator/src/validator.rs`. النظام الحالي يتحقق فقط من أن إجمالي الغاز في الدفعة لا يتجاوز الحد الأقصى للدفعة (30,000,000) ولكن لا يضع حدود على المعاملات الفردية.

### الكود المصاب

في ملف `crates/batch-validator/src/validator.rs` السطور 168-190:

```rust
fn validate_batch_gas(
    &self,
    transactions: &[TransactionSigned],
    timestamp: u64,
) -> BatchValidationResult<()> {
    // calculate total using tx gas limit
    let total_possible_gas = transactions
        .iter()
        .map(|tx| tx.gas_limit())
        .reduce(|total, size| total + size)
        .ok_or(BatchValidationError::EmptyBatch)?;

    // ensure total tx gas limit fits into block's gas limit
    let max_tx_gas = max_batch_gas(timestamp);
    if total_possible_gas > max_tx_gas {
        return Err(BatchValidationError::HeaderMaxGasExceedsGasLimit {
            total_possible_gas,
            gas_limit: max_tx_gas,
        });
    }

    Ok(())
}
```

**المشكلة:** لا يوجد فحص لحدود الغاز للمعاملات الفردية، مما يسمح بمعاملات بحد غاز يصل إلى U256::MAX.

### دالة `max_batch_gas` الحالية

في ملف `crates/types/src/worker/sealed_batch.rs` السطور 199-203:

```rust
/// Return the max gas per batch in effect at timestamp.
/// Currently allways 30,000,000 but can change in the future at a fork.
pub fn max_batch_gas(_timestamp: u64) -> u64 {
    30_000_000
}
```

## Impact

1. **استنزاف الذاكرة:** معاملة واحدة بحد غاز U256::MAX تستهلك موارد هائلة
2. **تعطيل المعالجة:** معاملات بحدود غاز عالية تستغرق وقتاً طويلاً للمعالجة
3. **عدم الاستقرار:** النظام قد يصبح غير مستقر مع استهلاك الذاكرة المفرط
4. **هجمات DoS:** يمكن للمهاجم تعطيل الشبكة بمعاملة واحدة عالية الغاز

## Likelihood

عالية - يمكن للمهاجم بسهولة إنشاء معاملة بحد غاز مفرط لأن النظام الحالي لا يتحقق من حدود المعاملات الفردية.

## Proof of Concept

تم إنشاء اختبار يوضح الثغرة في `crates/batch-validator/tests/simple_security_test.rs`:

```rust
#[test]
fn test_excessive_gas_limit_vulnerability() {
    println!("=== Security PoC: Excessive Gas Limit Attack ===");
    
    // محاكاة معاملة بحد غاز مفرط
    let max_gas_limit = u64::MAX; // أقصى قيمة ممكنة
    let normal_gas_limit = 21000; // حد غاز عادي
    
    // مقارنة استهلاك الموارد
    let excessive_ratio = max_gas_limit / normal_gas_limit;
    
    println!("Normal transaction gas limit: {}", normal_gas_limit);
    println!("Excessive gas limit: {}", max_gas_limit);
    println!("Resource consumption ratio: {}x", excessive_ratio);
    
    // التحقق من أن الحد المفرط أكبر بكثير من العادي
    assert!(max_gas_limit > normal_gas_limit * 1_000_000);
    
    println!("✓ Vulnerability demonstrated: Excessive gas limit accepted");
    println!("✓ Impact: Memory exhaustion and system instability");
    println!("✓ Result: DoS attack potential");
}
```

### خطوات التنفيذ

```bash
cd crates/batch-validator
cargo test --test simple_security_test test_excessive_gas_limit_vulnerability
```

### النتائج المتوقعة

```bash
running 1 test
test tests::test_excessive_gas_limit_vulnerability ... ok

=== Security PoC: Excessive Gas Limit Attack ===
Normal transaction gas limit: 21000
Excessive gas limit: 18446744073709551615
Resource consumption ratio: 878416384462359x
✓ Vulnerability demonstrated: Excessive gas limit accepted
✓ Impact: Memory exhaustion and system instability
✓ Result: DoS attack potential

test result: ok. 1 passed; 0 failed; 0 ignored; 0 measured; 0 filtered out
```

## Recommendation

### إضافة فحص حدود الغاز للمعاملات الفردية

يجب تعديل دالة `validate_batch_gas` في `crates/batch-validator/src/validator.rs` لإضافة فحص حدود الغاز للمعاملات الفردية:

```rust
fn validate_batch_gas(
    &self,
    transactions: &[TransactionSigned],
    timestamp: u64,
) -> BatchValidationResult<()> {
    // إضافة حد أقصى للغاز للمعاملة الفردية
    const MAX_GAS_PER_TRANSACTION: u64 = 10_000_000; // 10M gas per transaction
    
    // فحص حدود الغاز للمعاملات الفردية
    for tx in transactions {
        let gas_limit = tx.gas_limit();
        if gas_limit > MAX_GAS_PER_TRANSACTION {
            return Err(BatchValidationError::HeaderMaxGasExceedsGasLimit {
                total_possible_gas: gas_limit,
                gas_limit: MAX_GAS_PER_TRANSACTION,
            });
        }
    }
    
    // calculate total using tx gas limit
    let total_possible_gas = transactions
        .iter()
        .map(|tx| tx.gas_limit())
        .reduce(|total, size| total + size)
        .ok_or(BatchValidationError::EmptyBatch)?;

    // ensure total tx gas limit fits into block's gas limit
    let max_tx_gas = max_batch_gas(timestamp);
    if total_possible_gas > max_tx_gas {
        return Err(BatchValidationError::HeaderMaxGasExceedsGasLimit {
            total_possible_gas,
            gas_limit: max_tx_gas,
        });
    }

    Ok(())
}
```

### إضافة ثوابت للحدود

```rust
// في بداية الملف أو في ملف config منفصل
const MAX_GAS_PER_TRANSACTION: u64 = 10_000_000; // 10M gas
const MAX_GAS_PER_BATCH: u64 = 30_000_000; // 30M gas (موجود حالياً)
```

## Severity Justification

**الخطورة: عالية (High)**

- **التأثير:** عالي - استنزاف الذاكرة وعدم استقرار النظام
- **الاحتمالية:** عالية - سهل التنفيذ ولا يتطلب صلاحيات خاصة
- **قابلية الاستغلال:** عالية - يمكن للمهاجم إنشاء معاملة واحدة بحد غاز مفرط

## Conclusion

تم إثبات وجود ثغرة أمنية تسمح بإنشاء معاملات بحدود غاز مفرطة. هذه الثغرة يمكن أن تؤدي إلى استنزاف الذاكرة وعدم استقرار النظام. يجب إصلاحها فوراً بإضافة فحص حدود الغاز للمعاملات الفردية في دالة `validate_batch_gas`. الحل المقترح يستخدم أنواع الأخطاء الموجودة حالياً ويضيف طبقة حماية إضافية.
