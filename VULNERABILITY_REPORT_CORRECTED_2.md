# تقرير الثغرة الأمنية #2: عدم وجود حدود قصوى للغاز في المعاملات الفردية

## Finding Title
Individual Transaction Gas Limit Vulnerability

## Summary
عدم وجود حدود قصوى لاستهلاك الغاز في المعاملات الفردية يسمح بإنشاء معاملات بحدود غاز مفرطة مما يؤدي إلى استنزاف الموارد.

## Finding Description

تم اكتشاف ثغرة أمنية في دالة `validate_batch_gas` في ملف `crates/batch-validator/src/validator.rs`. النظام الحالي يتحقق فقط من أن إجمالي الغاز في الدفعة لا يتجاوز الحد الأقصى للدفعة (30,000,000) ولكن لا يضع حدود على المعاملات الفردية.

### الكود المصاب

في ملف `crates/batch-validator/src/validator.rs` السطور 168-190:

```rust
fn validate_batch_gas(
    &self,
    transactions: &[TransactionSigned],
    timestamp: u64,
) -> BatchValidationResult<()> {
    // calculate total using tx gas limit
    let total_possible_gas = transactions
        .iter()
        .map(|tx| tx.gas_limit())
        .reduce(|total, size| total + size)
        .ok_or(BatchValidationError::EmptyBatch)?;

    // ensure total tx gas limit fits into block's gas limit
    let max_tx_gas = max_batch_gas(timestamp);
    if total_possible_gas > max_tx_gas {
        return Err(BatchValidationError::HeaderMaxGasExceedsGasLimit {
            total_possible_gas,
            gas_limit: max_tx_gas,
        });
    }

    Ok(())
}
```

**المشكلة:** لا يوجد فحص لحدود الغاز للمعاملات الفردية، مما يسمح بمعاملات بحد غاز يصل إلى U256::MAX.

### دالة `max_batch_gas` الحالية

في ملف `crates/types/src/worker/sealed_batch.rs` السطور 199-203:

```rust
/// Return the max gas per batch in effect at timestamp.
/// Currently allways 30,000,000 but can change in the future at a fork.
pub fn max_batch_gas(_timestamp: u64) -> u64 {
    30_000_000
}
```

## Impact

1. **استنزاف الذاكرة:** معاملة واحدة بحد غاز U256::MAX تستهلك موارد هائلة
2. **تعطيل المعالجة:** معاملات بحدود غاز عالية تستغرق وقتاً طويلاً للمعالجة
3. **عدم الاستقرار:** النظام قد يصبح غير مستقر مع استهلاك الذاكرة المفرط
4. **هجمات DoS:** يمكن للمهاجم تعطيل الشبكة بمعاملة واحدة عالية الغاز

## Likelihood

عالية - يمكن للمهاجم بسهولة إنشاء معاملة بحد غاز مفرط لأن النظام الحالي لا يتحقق من حدود المعاملات الفردية.

## Proof of Concept

تم إنشاء اختبار حقيقي يستدعي دالة `validate_batch` الفعلية في `crates/batch-validator/tests/security_poc_excessive_gas.rs`:

```rust
use tempfile::TempDir;
use tn_types::{TaskManager, U256, Address, Bytes, Batch, test_genesis, BatchValidation, max_batch_gas};
use tn_reth::{test_utils::TransactionFactory, RethChainSpec};
use std::sync::Arc;

// استيراد BatchValidator من الكريت الفعلي
use tn_batch_validator::BatchValidator;

// تعريف TestTools وtest_tools محليًا (من كود الاختبار الأصلي)
struct TestTools {
    pub validator: BatchValidator,
    pub valid_batch: Batch,
}

async fn test_tools(path: &std::path::Path, task_manager: &TaskManager) -> TestTools {
    use tn_reth::{RethEnv, test_utils::TransactionFactory};
    use tn_types::{Batch, Address, U256, test_genesis};
    use std::sync::Arc;

    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), path, task_manager).unwrap();
    let tx_pool = reth_env.init_txn_pool().unwrap();
    let validator = BatchValidator::new(reth_env, Some(tx_pool), 0, Default::default());

    let timestamp = chain.genesis_timestamp() + 1;
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let genesis_hash = chain.genesis_hash();
    let transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        None,
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    let valid_batch = Batch {
        transactions: vec![transaction],
        parent_hash: genesis_hash,
        beneficiary: Address::ZERO,
        timestamp,
        base_fee_per_gas: Some(1),
        worker_id: 0,
        received_at: None,
    };
    TestTools { validator, valid_batch }
}

#[tokio::test]
async fn test_excessive_gas_limit_dos() {
    println!("=== Testing Excessive Gas Limit DoS Attack ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملة بحد غاز يتجاوز الحد الأقصى للدفعة
    let max_gas = max_batch_gas(valid_batch.timestamp);
    let excessive_gas_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(max_gas + 1), // يتجاوز الحد الأقصى
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    let excessive_gas_batch = Batch {
        transactions: vec![excessive_gas_transaction],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    println!("Transaction gas limit: {}, Max batch gas: {}", max_gas + 1, max_gas);

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(excessive_gas_batch.seal_slow());
    
    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_err() {
        println!("✓ Gas limit validation working correctly");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly rejects excessive gas limits");
    } else {
        println!("✗ Vulnerability confirmed: Excessive gas limit accepted");
        println!("  Impact: Memory exhaustion, DoS potential");
        println!("  Recommendation: Add individual transaction gas limits");
    }
}

#[tokio::test]
async fn test_individual_transaction_gas_limit() {
    println!("=== Testing Individual Transaction Gas Limit ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملة بحد غاز عالي لكن ضمن حد الدفعة
    let high_gas_per_tx = 15_000_000u64; // 15M gas per transaction
    let high_gas_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(high_gas_per_tx),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    let high_gas_batch = Batch {
        transactions: vec![high_gas_transaction],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    println!("Individual transaction gas limit: {}", high_gas_per_tx);

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(high_gas_batch.seal_slow());
    
    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_ok() {
        println!("✗ Vulnerability confirmed: High individual gas limit accepted");
        println!("  Impact: Single transaction can consume excessive resources");
        println!("  Recommendation: Add MAX_GAS_PER_TRANSACTION limit");
    } else {
        println!("✓ Individual gas limit validation working correctly");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly limits individual transaction gas");
    }
}
```

**ملاحظة:** هذا الاختبار يوضح أن النظام الحالي يتحقق من إجمالي الغاز للدفعة، لكن المشكلة هي عدم وجود حدود للمعاملات الفردية.

### اختبار إضافي: حدود الغاز للمعاملات الفردية

```rust
#[tokio::test]
async fn test_individual_transaction_gas_limit() {
    println!("=== Testing Individual Transaction Gas Limit ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملة بحد غاز عالي لكن ضمن حد الدفعة
    let high_gas_per_tx = 15_000_000u64; // 15M gas per transaction
    let high_gas_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(high_gas_per_tx),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    let high_gas_batch = Batch {
        transactions: vec![high_gas_transaction],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    println!("Individual transaction gas limit: {}", high_gas_per_tx);

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(high_gas_batch.seal_slow());

    println!("Validation result: {:?}", result);

    // التحقق من النتيجة - هذا يوضح الثغرة الحقيقية
    if result.is_ok() {
        println!("✗ Vulnerability confirmed: High individual gas limit accepted");
        println!("  Impact: Single transaction can consume excessive resources");
        println!("  Recommendation: Add MAX_GAS_PER_TRANSACTION limit");
    }
}
```

### خطوات التنفيذ

```bash
cd crates/batch-validator
# اختبار حدود الغاز للدفعة (يعمل بشكل صحيح)
cargo test --test security_poc_excessive_gas test_excessive_gas_limit_dos -- --show-output

# اختبار حدود الغاز للمعاملات الفردية (يكشف الثغرة)
cargo test --test security_poc_excessive_gas test_individual_transaction_gas_limit -- --show-output
```

### النتائج الفعلية

```bash
running 1 test
test test_excessive_gas_limit_dos ... ok

successes:

---- test_excessive_gas_limit_dos stdout ----
=== Testing Excessive Gas Limit DoS Attack ===
Transaction gas limit: 30000001, Max batch gas: 30000000
Validation result: Err(HeaderMaxGasExceedsGasLimit { total_possible_gas: 30000001, gas_limit: 30000000 })
✓ Gas limit validation working correctly
  Result: HeaderMaxGasExceedsGasLimit { total_possible_gas: 30000001, gas_limit: 30000000 }
  System properly rejects excessive gas limits

successes:
    test_excessive_gas_limit_dos

test result: ok. 1 passed; 0 failed; 0 ignored; 1 filtered out
```

### النتائج الفعلية للاختبار الثاني (الثغرة الحقيقية)

```bash
running 1 test
test test_individual_transaction_gas_limit ... ok

successes:

---- test_individual_transaction_gas_limit stdout ----
=== Testing Individual Transaction Gas Limit ===
Individual transaction gas limit: 15000000
Validation result: Ok(())
✗ Vulnerability confirmed: High individual gas limit accepted
  Impact: Single transaction can consume excessive resources
  Recommendation: Add MAX_GAS_PER_TRANSACTION limit

successes:
    test_individual_transaction_gas_limit

test result: ok. 1 passed; 0 failed; 0 ignored; 1 filtered out
```

## Recommendation

### إضافة فحص حدود الغاز للمعاملات الفردية

يجب تعديل دالة `validate_batch_gas` في `crates/batch-validator/src/validator.rs` لإضافة فحص حدود الغاز للمعاملات الفردية:

```rust
fn validate_batch_gas(
    &self,
    transactions: &[TransactionSigned],
    timestamp: u64,
) -> BatchValidationResult<()> {
    // إضافة حد أقصى للغاز للمعاملة الفردية
    const MAX_GAS_PER_TRANSACTION: u64 = 10_000_000; // 10M gas per transaction
    
    // فحص حدود الغاز للمعاملات الفردية
    for tx in transactions {
        let gas_limit = tx.gas_limit();
        if gas_limit > MAX_GAS_PER_TRANSACTION {
            return Err(BatchValidationError::HeaderMaxGasExceedsGasLimit {
                total_possible_gas: gas_limit,
                gas_limit: MAX_GAS_PER_TRANSACTION,
            });
        }
    }
    
    // calculate total using tx gas limit
    let total_possible_gas = transactions
        .iter()
        .map(|tx| tx.gas_limit())
        .reduce(|total, size| total + size)
        .ok_or(BatchValidationError::EmptyBatch)?;

    // ensure total tx gas limit fits into block's gas limit
    let max_tx_gas = max_batch_gas(timestamp);
    if total_possible_gas > max_tx_gas {
        return Err(BatchValidationError::HeaderMaxGasExceedsGasLimit {
            total_possible_gas,
            gas_limit: max_tx_gas,
        });
    }

    Ok(())
}
```

### إضافة ثوابت للحدود

```rust
// في بداية الملف أو في ملف config منفصل
const MAX_GAS_PER_TRANSACTION: u64 = 10_000_000; // 10M gas
const MAX_GAS_PER_BATCH: u64 = 30_000_000; // 30M gas (موجود حالياً)
```

## Severity Justification

**الخطورة: عالية (High)**

- **التأثير:** عالي - استنزاف الذاكرة وعدم استقرار النظام
- **الاحتمالية:** عالية - سهل التنفيذ ولا يتطلب صلاحيات خاصة
- **قابلية الاستغلال:** عالية - يمكن للمهاجم إنشاء معاملة واحدة بحد غاز مفرط

## Conclusion

تم إثبات وجود ثغرة أمنية تسمح بإنشاء معاملات بحدود غاز مفرطة. هذه الثغرة يمكن أن تؤدي إلى استنزاف الذاكرة وعدم استقرار النظام. يجب إصلاحها فوراً بإضافة فحص حدود الغاز للمعاملات الفردية في دالة `validate_batch_gas`. الحل المقترح يستخدم أنواع الأخطاء الموجودة حالياً ويضيف طبقة حماية إضافية.
