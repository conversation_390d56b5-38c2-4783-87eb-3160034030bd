# تقرير الثغرة الأمنية #2: عدم وجود حدود قصوى للغاز في المعاملات الفردية

## Finding Title
Individual Transaction Gas Limit Resource Allocation Issue

## Summary
عدم وجود حد أقصى للغاز للمعاملات الفردية يسمح بسوء تخصيص موارد الدفعة، مما يمكن المهاجمين من تنفيذ هجمات Griefing وإهدار موارد الشبكة عبر معاملات تستهلك معظم غاز الدفعة.

## Finding Description

تم اكتشاف مشكلة في تخصيص الموارد في دالة `validate_batch_gas` في ملف `crates/batch-validator/src/validator.rs`. النظام الحالي يتحقق من أن إجمالي الغاز في الدفعة لا يتجاوز الحد الأقصى للدفعة (`max_batch_gas` = 30,000,000) ولكن لا يضع حدود على المعاملات الفردية.

**الحماية الموجودة:** النظام يمنع بنجاح المعاملات التي تتجاوز حد الدفعة الإجمالي.

**المشكلة الحقيقية:** يمكن للمهاجم إنشاء معاملة واحدة تستهلك معظم غاز الدفعة (مثل 15-25 مليون غاز)، مما يؤدي إلى:

1. **هجمات Griefing** - منع المعاملات الصغيرة الأخرى من التضمين في نفس الدفعة
2. **إهدار موارد الشبكة** - معالجة ونشر معاملات ضخمة قد تفشل في التنفيذ
3. **تقليل كفاءة الشبكة** - استغلال سيء لمساحة الدفعة المتاحة

### الكود المصاب

في ملف `crates/batch-validator/src/validator.rs` السطور 168-190:

```rust
fn validate_batch_gas(
    &self,
    transactions: &[TransactionSigned],
    timestamp: u64,
) -> BatchValidationResult<()> {
    // calculate total using tx gas limit
    let total_possible_gas = transactions
        .iter()
        .map(|tx| tx.gas_limit())
        .reduce(|total, size| total + size)
        .ok_or(BatchValidationError::EmptyBatch)?;

    // ensure total tx gas limit fits into block's gas limit
    let max_tx_gas = max_batch_gas(timestamp);
    if total_possible_gas > max_tx_gas {
        return Err(BatchValidationError::HeaderMaxGasExceedsGasLimit {
            total_possible_gas,
            gas_limit: max_tx_gas,
        });
    }

    Ok(())
}
```

**المشكلة:** لا يوجد فحص لحدود الغاز للمعاملات الفردية، مما يسمح بمعاملة واحدة باستهلاك معظم غاز الدفعة (حتى 29,999,999 غاز).

### دالة `max_batch_gas` الحالية

في ملف `crates/types/src/worker/sealed_batch.rs` السطور 199-203:

```rust
/// Return the max gas per batch in effect at timestamp.
/// Currently allways 30,000,000 but can change in the future at a fork.
pub fn max_batch_gas(_timestamp: u64) -> u64 {
    30_000_000
}
```

## Impact

1. **هجمات Griefing:** معاملة واحدة تستهلك معظم غاز الدفعة تمنع تضمين معاملات صغيرة أخرى
2. **إهدار موارد الشبكة:** معالجة ونشر معاملات ضخمة قد تفشل في التنفيذ
3. **تقليل كفاءة الشبكة:** استغلال سيء لمساحة الدفعة المتاحة
4. **تأثير على الأداء:** معاملات بحدود غاز عالية تستغرق وقتاً أطول للمعالجة

## Likelihood

عالية - يمكن للمهاجم بسهولة إنشاء معاملة بحد غاز عالي (ضمن حد الدفعة) لأن النظام الحالي لا يتحقق من حدود المعاملات الفردية. الهجوم بسيط ولا يتطلب صلاحيات خاصة.

## Proof of Concept

تم إنشاء اختبار حقيقي يستدعي دالة `validate_batch` الفعلية في `crates/batch-validator/tests/security_poc_excessive_gas.rs`:

```rust
use tempfile::TempDir;
use tn_types::{TaskManager, U256, Address, Bytes, Batch, test_genesis, BatchValidation, max_batch_gas};
use tn_reth::{test_utils::TransactionFactory, RethChainSpec};
use std::sync::Arc;

// استيراد BatchValidator من الكريت الفعلي
use tn_batch_validator::BatchValidator;

// تعريف TestTools وtest_tools محليًا (من كود الاختبار الأصلي)
struct TestTools {
    pub validator: BatchValidator,
    pub valid_batch: Batch,
}

async fn test_tools(path: &std::path::Path, task_manager: &TaskManager) -> TestTools {
    use tn_reth::{RethEnv, test_utils::TransactionFactory};
    use tn_types::{Batch, Address, U256, test_genesis};
    use std::sync::Arc;

    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());
    let reth_env = RethEnv::new_for_temp_chain(chain.clone(), path, task_manager).unwrap();
    let tx_pool = reth_env.init_txn_pool().unwrap();
    let validator = BatchValidator::new(reth_env, Some(tx_pool), 0, Default::default());

    let timestamp = chain.genesis_timestamp() + 1;
    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let genesis_hash = chain.genesis_hash();
    let transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        None,
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );
    let valid_batch = Batch {
        transactions: vec![transaction],
        parent_hash: genesis_hash,
        beneficiary: Address::ZERO,
        timestamp,
        base_fee_per_gas: Some(1),
        worker_id: 0,
        received_at: None,
    };
    TestTools { validator, valid_batch }
}

#[tokio::test]
async fn test_excessive_gas_limit_dos() {
    println!("=== Testing Excessive Gas Limit DoS Attack ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملة بحد غاز يتجاوز الحد الأقصى للدفعة
    let max_gas = max_batch_gas(valid_batch.timestamp);
    let excessive_gas_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(max_gas + 1), // يتجاوز الحد الأقصى
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    let excessive_gas_batch = Batch {
        transactions: vec![excessive_gas_transaction],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    println!("Transaction gas limit: {}, Max batch gas: {}", max_gas + 1, max_gas);

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(excessive_gas_batch.seal_slow());
    
    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_err() {
        println!("✓ Gas limit validation working correctly");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly rejects excessive gas limits");
    } else {
        println!("✗ Vulnerability confirmed: Excessive gas limit accepted");
        println!("  Impact: Memory exhaustion, DoS potential");
        println!("  Recommendation: Add individual transaction gas limits");
    }
}

#[tokio::test]
async fn test_individual_transaction_gas_limit() {
    println!("=== Testing Individual Transaction Gas Limit ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملة بحد غاز عالي لكن ضمن حد الدفعة
    let high_gas_per_tx = 15_000_000u64; // 15M gas per transaction
    let high_gas_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(high_gas_per_tx),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    let high_gas_batch = Batch {
        transactions: vec![high_gas_transaction],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    println!("Individual transaction gas limit: {}", high_gas_per_tx);

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(high_gas_batch.seal_slow());
    
    println!("Validation result: {:?}", result);

    // التحقق من النتيجة
    if result.is_ok() {
        println!("✗ Vulnerability confirmed: High individual gas limit accepted");
        println!("  Impact: Single transaction can consume excessive resources");
        println!("  Recommendation: Add MAX_GAS_PER_TRANSACTION limit");
    } else {
        println!("✓ Individual gas limit validation working correctly");
        println!("  Result: {:?}", result.err().unwrap());
        println!("  System properly limits individual transaction gas");
    }
}
```

**ملاحظة مهمة:** الاختبار الأول (`test_excessive_gas_limit_dos`) يثبت أن **الحماية الموجودة تعمل بشكل صحيح**. النظام يرفض بنجاح المعاملات التي تتجاوز حد الدفعة الإجمالي. المشكلة الحقيقية تظهر في الاختبار الثاني حيث لا توجد حدود للمعاملات الفردية.

### اختبار إضافي: حدود الغاز للمعاملات الفردية

```rust
#[tokio::test]
async fn test_individual_transaction_gas_limit() {
    println!("=== Testing Individual Transaction Gas Limit ===");

    let tmp_dir = TempDir::new().unwrap();
    let task_manager = TaskManager::default();
    let TestTools { validator, valid_batch } = test_tools(tmp_dir.path(), &task_manager).await;

    let mut tx_factory = TransactionFactory::new();
    let value = U256::from(10).checked_pow(U256::from(18)).unwrap();
    let gas_price = 7;
    let chain: Arc<RethChainSpec> = Arc::new(test_genesis().into());

    // إنشاء معاملة بحد غاز عالي لكن ضمن حد الدفعة
    let high_gas_per_tx = 15_000_000u64; // 15M gas per transaction
    let high_gas_transaction = tx_factory.create_eip1559_encoded(
        chain.clone(),
        Some(high_gas_per_tx),
        gas_price,
        Some(Address::ZERO),
        value,
        Bytes::new(),
    );

    let high_gas_batch = Batch {
        transactions: vec![high_gas_transaction],
        parent_hash: valid_batch.parent_hash,
        beneficiary: valid_batch.beneficiary,
        timestamp: valid_batch.timestamp,
        base_fee_per_gas: Some(7), // Match the gas_price
        worker_id: 0,
        received_at: valid_batch.received_at,
    };

    println!("Individual transaction gas limit: {}", high_gas_per_tx);

    // استدعاء دالة validate_batch الحقيقية
    let result = validator.validate_batch(high_gas_batch.seal_slow());

    println!("Validation result: {:?}", result);

    // التحقق من النتيجة - هذا يوضح الثغرة الحقيقية
    if result.is_ok() {
        println!("✗ Vulnerability confirmed: High individual gas limit accepted");
        println!("  Impact: Single transaction can consume excessive resources");
        println!("  Recommendation: Add MAX_GAS_PER_TRANSACTION limit");
    }
}
```

### خطوات التنفيذ

```bash
cd crates/batch-validator
# اختبار حدود الغاز للدفعة (يعمل بشكل صحيح)
cargo test --test security_poc_excessive_gas test_excessive_gas_limit_dos -- --show-output

# اختبار حدود الغاز للمعاملات الفردية (يكشف الثغرة)
cargo test --test security_poc_excessive_gas test_individual_transaction_gas_limit -- --show-output
```

### النتائج الفعلية

```bash
running 1 test
test test_excessive_gas_limit_dos ... ok

successes:

---- test_excessive_gas_limit_dos stdout ----
=== Testing Excessive Gas Limit DoS Attack ===
Transaction gas limit: 30000001, Max batch gas: 30000000
Validation result: Err(HeaderMaxGasExceedsGasLimit { total_possible_gas: 30000001, gas_limit: 30000000 })
✓ Gas limit validation working correctly
  Result: HeaderMaxGasExceedsGasLimit { total_possible_gas: 30000001, gas_limit: 30000000 }
  System properly rejects excessive gas limits

successes:
    test_excessive_gas_limit_dos

test result: ok. 1 passed; 0 failed; 0 ignored; 1 filtered out
```

### النتائج الفعلية للاختبار الثاني (الثغرة الحقيقية)

```bash
running 1 test
test test_individual_transaction_gas_limit ... ok

successes:

---- test_individual_transaction_gas_limit stdout ----
=== Testing Individual Transaction Gas Limit ===
Individual transaction gas limit: 15000000
Validation result: Ok(())
✗ Vulnerability confirmed: High individual gas limit accepted
  Impact: Single transaction can consume excessive resources
  Recommendation: Add MAX_GAS_PER_TRANSACTION limit

successes:
    test_individual_transaction_gas_limit

test result: ok. 1 passed; 0 failed; 0 ignored; 1 filtered out
```

## Recommendation

### إضافة حد أقصى للغاز للمعاملات الفردية

يجب تعديل دالة `validate_batch_gas` في `crates/batch-validator/src/validator.rs` لإضافة فحص حدود الغاز للمعاملات الفردية. هذا سيمنع هجمات Griefing ويحسن كفاءة تخصيص الموارد:

```rust
fn validate_batch_gas(
    &self,
    transactions: &[TransactionSigned],
    timestamp: u64,
) -> BatchValidationResult<()> {
    // إضافة حد أقصى للغاز للمعاملة الفردية
    const MAX_GAS_PER_TRANSACTION: u64 = 10_000_000; // 10M gas per transaction
    
    // فحص حدود الغاز للمعاملات الفردية
    for tx in transactions {
        let gas_limit = tx.gas_limit();
        if gas_limit > MAX_GAS_PER_TRANSACTION {
            return Err(BatchValidationError::HeaderMaxGasExceedsGasLimit {
                total_possible_gas: gas_limit,
                gas_limit: MAX_GAS_PER_TRANSACTION,
            });
        }
    }
    
    // calculate total using tx gas limit
    let total_possible_gas = transactions
        .iter()
        .map(|tx| tx.gas_limit())
        .reduce(|total, size| total + size)
        .ok_or(BatchValidationError::EmptyBatch)?;

    // ensure total tx gas limit fits into block's gas limit
    let max_tx_gas = max_batch_gas(timestamp);
    if total_possible_gas > max_tx_gas {
        return Err(BatchValidationError::HeaderMaxGasExceedsGasLimit {
            total_possible_gas,
            gas_limit: max_tx_gas,
        });
    }

    Ok(())
}
```

### إضافة ثوابت للحدود

```rust
// في بداية الملف أو في ملف config منفصل
const MAX_GAS_PER_TRANSACTION: u64 = 10_000_000; // 10M gas
const MAX_GAS_PER_BATCH: u64 = 30_000_000; // 30M gas (موجود حالياً)
```

## Severity Justification

**الخطورة: متوسطة (Medium)**

- **التأثير:** متوسط - تأثير على كفاءة الشبكة وإمكانية هجمات Griefing، لكن لا يهدد استقرار النظام
- **الاحتمالية:** عالية - سهل التنفيذ ولا يتطلب صلاحيات خاصة
- **قابلية الاستغلال:** عالية - يمكن للمهاجم إنشاء معاملة تستهلك معظم غاز الدفعة
- **الحماية الموجودة:** النظام يمنع تجاوز حد الدفعة الإجمالي، مما يحد من التأثير

## Conclusion

تم إثبات وجود مشكلة في تخصيص الموارد تسمح للمعاملات الفردية باستهلاك معظم غاز الدفعة. هذه المشكلة يمكن أن تؤدي إلى هجمات Griefing وتقليل كفاءة الشبكة.

**النتائج الرئيسية:**
- النظام يحمي بنجاح من تجاوز حد الدفعة الإجمالي
- المشكلة الحقيقية هي عدم وجود حد للمعاملات الفردية
- التأثير محدود لكنه يؤثر على كفاءة الشبكة

يُوصى بإضافة `MAX_GAS_PER_TRANSACTION` لتحسين تخصيص الموارد ومنع هجمات Griefing. الحل المقترح يستخدم أنواع الأخطاء الموجودة حالياً ويضيف طبقة حماية إضافية.
